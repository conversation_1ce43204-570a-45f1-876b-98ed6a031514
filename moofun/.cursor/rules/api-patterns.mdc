---
description: 
globs: 
alwaysApply: false
---
# API and Data Handling Patterns

## HTTP Client Setup
```js
// Base axios instance pattern
const instance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  headers: { 'Content-Type': 'application/json' }
})

// Error handling interceptor
instance.interceptors.response.use(
  response => response.data,
  error => {
    if (error.response?.status === 401) {
      // Handle authentication errors
    }
    return Promise.reject(error)
  }
)
```

## API Function Pattern
```js
// API function pattern
export async function someApiCall(params) {
  const { data } = await instance.get('/endpoint', { params })
  return data
}
```

## Error Handling
- Use try-catch blocks in composables
- Implement consistent error notifications
- Handle network errors gracefully
- Provide user-friendly error messages
- Log errors for debugging

## Data Management
- Use TypeScript interfaces for API responses
- Transform API data in composables, not components
- Cache data when appropriate
- Implement proper loading states
- Handle pagination consistently

## Authentication
- Handle token management in auth composables
- Implement proper token refresh logic
- Clear auth state on 401 errors
- Use interceptors for auth headers
- Secure sensitive data

## API Response Pattern
```typescript
interface ApiResponse<T> {
  data: T
  message?: string
  status: number
}

interface PaginatedResponse<T> extends ApiResponse<T> {
  pagination: {
    page: number
    pageSize: number
    total: number
  }
}
```

## API Integration Guidelines
- Group related API calls in feature modules
- Use proper TypeScript types
- Implement proper error boundaries
- Handle loading and error states
- Cache responses when appropriate
- Use proper HTTP methods
- Follow REST conventions

## Data Transformation
- Transform data close to API layer
- Use TypeScript for type safety
- Keep transformations simple and focused
- Document complex transformations
- Handle edge cases properly

## Caching Strategy
- Implement proper cache invalidation
- Use composables for cache management
- Clear cache on relevant actions
- Handle stale data appropriately
- Document caching behavior

