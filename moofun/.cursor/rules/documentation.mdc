---
description: 
globs: 
alwaysApply: true
---
# Documentation Standards

## Code Documentation
- Use JSDoc comments for functions and classes
- Document complex algorithms and business logic
- Include examples for non-obvious code
- Keep comments up to date with code changes

## Type Documentation
- Document complex type definitions
- Include examples of type usage
- Document any constraints or assumptions

## Component Documentation
- Document component props and their types
- Include usage examples
- Document any side effects or important lifecycle behaviors

## API Documentation
- Document all API endpoints
- Include request/response examples
- Document error scenarios and handling
- Keep API documentation in sync with implementation

## README Guidelines
- Maintain clear project setup instructions
- Document development workflows
- List key dependencies and their versions
- Include troubleshooting guides

