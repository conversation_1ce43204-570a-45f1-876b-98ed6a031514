# 全局公共参数

**全局Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**全局Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**全局Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**全局认证方式**

> 无需认证

# 状态码说明

| 状态码 | 中文描述 |
| --- | ---- |
| 暂无参数 |

# 获取payload

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-01-15 15:10:47

> 更新时间: 2025-03-03 15:58:08

#### 接口说明

**该接口用于生成TON钱包验证所需的payload。生成的payload用于后续的钱包验证过程，包含随机数据和过期时间信息。**

#### 请求URL

```
POST /api/ton-proof/generate-payload
```

#### 请求参数

**无需请求参数**

#### 响应格式

##### 成功响应

```json
{
  "ok": true,
  "payload": "string"  // 32字节的16进制格式payload
}
```

##### 错误响应

```json
{
  "ok": false,
  "message": "string"  // 错误信息
}
```

#### 技术说明


 1. Payload生成过程：
 2. 
 3. 生成8字节随机数
 4. 添加8字节过期时间戳
 5. 计算HMAC-SHA256签名
 6. 组合前32字节作为最终payload
 7. 
 8. 
 9. Payload格式：
 10. 
 11. 总长度：32字节
 12. 前16字节：随机数据和过期时间
 13. 后16字节：签名数据
 14. 
 15. 
 16. 有效期设置：
 17. 
 18. Payload默认有效期为15分钟
 19. 超过有效期后需要重新生成
 20. 接口有频率限制：每个IP 15分钟内最多100次请求
    生成的payload仅在有效期内可用
    每次调用生成的payload都是唯一的
    建议在发起钱包验证前实时获取payload

#### 错误代码


 - 500: 生成payload失败

**接口状态**

> 开发中

**接口URL**

> api/ton-proof/generate-payload

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> none

**认证方式**

> 无需认证

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 绑定钱包、登陆、注册

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-01-15 15:23:16

> 更新时间: 2025-03-22 01:10:32

### check-proof 接口

#### 接口说明

**该接口用于验证TON钱包的proof信息，并根据验证结果进行用户注册或登录。接口会验证payload、过期时间、地址和公钥等信息的有效性。**

#### 请求URL

```
POST /api/ton-proof/check-proof
```

#### 请求参数

```json
{
  "proof": {
    "payload": "string",      // 通过payload接口获取的数据
    "timestamp": "number",    // 时间戳
    "state_init": "string",  // Base64格式的state_init
    "signature": "string",   // Base64格式的签名
    "domain": {
      "lengthBytes": "number", // domain值的字节长度
      "value": "string"       // domain值
    }
  },
  "address": "string",      // TON钱包地址
  "network": "string",      // 网络类型 (MAINNET 或 TESTNET)
  "public_key": "string",  // 16进制格式的公钥
  "code": "string",        // 可选，邀请码
  "initData": "string"     // TG 初始化数据
}
```

#### 响应格式

##### 成功响应

```json
{
  "ok": true,
  "data": {
    "address": "string",           // 原始钱包地址
    "userFriendlyAddress": "string", // 用户友好格式的钱包地址
    "network": "string"            // 网络类型
  },
  "token": "string"               // JWT令牌
}
```

##### 错误响应

```json
{
  "ok": false,
  "message": "string",  // 错误信息
  "error": [            // 可选，参数验证错误详情
    {
      "field": "string",
      "message": "string"
    }
  ]
}
```

#### 验证流程


 1. 参数验证：验证请求参数的完整性和格式。
 2. 
 3. Payload验证：
 4. 
 5. 验证payload长度（必须为32字节）
 6. 验证payload签名
 7. 
 8. 
 9. 过期时间验证：
 10. 
 11. 验证payload是否过期
 12. 验证proof时间戳是否在有效期内（15分钟）
 13. 
 14. 
 15. Domain验证：
 16. 
 17. 验证domain长度是否与lengthBytes匹配
 18. 
 19. 
 20. 地址和公钥验证：
 21. 
 22. 从state_init解析或通过API获取公钥
 23. 验证公钥是否匹配
 24. 验证合约地址是否匹配

**验证成功后，系统会：
注册新用户（如果是新用户）
创建或验证钱包绑定
生成邀请码
处理推荐关系（如果有推荐码）
创建初始化奖励箱

**


 - 注册新用户（如果是新用户）
 - 创建或验证钱包绑定
 - 生成邀请码
 - 处理推荐关系（如果有推荐码）
 - 创建初始化奖励箱

#### 错误代码


 - 400: 参数验证失败
 - 404: 用户或钱包未找到
 - 500: 服务器内部错误

#### 注意事项


 1. 每个Telegram账号只能绑定一个钱包地址
 2. 每个钱包地址只能绑定一个用户账号
 3. 接口有频率限制：每个IP 15分钟内最多100次请求
 4. JWT令牌有效期为60天

**接口状态**

> 开发中

**接口URL**

> api/ton-proof/check-proof

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "address": "0:e2a0f4d24e874ebe925ee5ccd8f7adc9af2e108a51f7b4c073667fb5bc56e136",
    "network": "-3",
    "proof": {
        "timestamp": 1736926792,
        "domain": {
            "lengthBytes": 21,
            "value": "ton-connect.github.io"
        },
        "signature": "QYwR4e8s3KiETvXl/Q3DLZrP6YCV6VuiM1sdDOT0I1NhIiKS4ezOyVHy2ei0UpYSGOg93bY6mQulfthD6oUMBw==",
        "payload": "{{payload}}",
        "state_init": "te6cckECFgEAArEAAgE0AgEAUYAAAAA////+8QNZBlU1kWaIxC6LU7J1abIwIZvcEYPaIEZRCfp+YAOgART/APSkE/S88sgLAwIBIAYEAQLyBQEeINcLH4IQc2lnbrry4Ip/EQIBSBAHAgEgCQgAGb5fD2omhAgKDrkPoCwCASANCgIBSAwLABGyYvtRNDXCgCAAF7Ml+1E0HHXIdcLH4AIBbg8OABmvHfaiaEAQ65DrhY/AABmtznaiaEAg65Drhf/AAtzQINdJwSCRW49jINcLHyCCEGV4dG69IYIQc2ludL2wkl8D4IIQZXh0brqOtIAg1yEB0HTXIfpAMPpE+Cj6RDBYvZFb4O1E0IEBQdch9AWDB/QOb6ExkTDhgEDXIXB/2zzgMSDXSYECgLmRMOBw4hIRAeaO8O2i7fshgwjXIgKDCNcjIIAg1yHTH9Mf0x/tRNDSANMfINMf0//XCgAK+QFAzPkQmiiUXwrbMeHywIffArNQB7Dy0IRRJbry4IVQNrry4Ib4I7vy0IgikvgA3gGkf8jKAMsfAc8Wye1UIJL4D95w2zzYEgP27aLt+wL0BCFukmwhjkwCIdc5MHCUIccAs44tAdcoIHYeQ2wg10nACPLgkyDXSsAC8uCTINcdBscSwgBSMLDy0InXTNc5MAGk6GwShAe78uCT10rAAPLgk+1V4tIAAcAAkVvg69csCBQgkXCWAdcsCBwS4lIQseMPINdKFRQTABCTW9sx4ddM0AByMNcsCCSOLSHy4JLSAO1E0NIAURO68tCPVFAwkTGcAYEBQNch1woA8uCO4sjKAFjPFsntVJPywI3iAJYB+kAB+kT4KPpEMFi68uCR7UTQgQFB1xj0BQSdf8jKAEAEgwf0U/Lgi44UA4MH9Fvy4Iwi1woAIW4Bs7Dy0JDiyFADzxYS9ADJ7VQ1seTn"
    },
    "public_key": "e206b20caa6b22cd11885d16a764ead364604337b82307b4408ca213f4fcc007",
    "initData": "user=%7B%22id%22%3A5160149730%2C%22first_name%22%3A%22Zo%22%2C%22last_name%22%3A%22YonLian%22%2C%22username%22%3A%22Zoyonlian%22%2C%22language_code%22%3A%22zh-hans%22%2C%22allows_write_to_pm%22%3Atrue%7D&chat_instance=-4974430574441768306&chat_type=sender&auth_date=1730357460&hash=80bac0b64bb2262b99f8aac7dfd00b005338dc0981a907ce3da44dbd031cc84a"
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| address | gosh offset psst ouch perky | string | 否 | 地址 |
| network | astride ugh confound | string | 否 | 网络 |
| proof | - | object | 否 | 证明 |
| proof.timestamp | 1736926792 | number | 否 | 证明时间戳 |
| proof.domain | - | object | 否 | 证明域名 |
| proof.domain.lengthBytes | 21 | number | 否 | 证明域名长度字节数 |
| proof.domain.value | until a lest grok implode | string | 否 | 证明域名值 |
| proof.signature | crooked puppet how finally knowledgeably | string | 否 | 证明签名 |
| proof.payload | pivot | string | 否 | 证明有效负载 |
| proof.state_init | chapel yeast yuck misspend vista | string | 否 | 证明初始状态 |
| public_key | warp | string | 否 | 公钥 |
| initData | furiously abaft unlawful | string | 否 | TG初始化数据 |

**认证方式**

> 无需认证

**响应示例**

* 成功(200)

```javascript
暂无数据
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| data | - | object | 数据内容 |
| data.address | sham slowly since amongst concerning | string | 数据地址 |
| data.userFriendlyAddress | dreary an or | string | 数据用户友好地址 |
| data.network | once reiterate colorfully | string | 数据网络 |
| ok | true | boolean | 是否成功 |
| token | pro | string | 令牌 |

* 失败(404)

```javascript
暂无数据
```

**Query**

# 绑定推荐

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-01-19 16:34:36

> 更新时间: 2025-04-15 17:41:53

#### 接口说明

**该接口用于绑定用户的推荐关系。用户可以通过此接口输入推荐码，建立与推荐人的关联关系。**

#### 请求URL

```
POST /api/referral/bind
```

#### 请求参数

```json
{
  "code": "string"  // 推荐码，必填
}
```

#### 响应格式

##### 成功响应

```json
{
  "ok": true,
  "data": {    // 绑定结果信息
    "referrerId": "string",    // 推荐人ID
    "referrerWalletId": "string"  // 推荐人钱包ID
  },
  "message": "string"  // 成功提示信息
}
```

##### 错误响应

```json
{
  "ok": false,
  "message": "string",  // 错误信息
  "error": [            // 可选，参数验证错误详情
    {
      "field": "string",
      "message": "string"
    }
  ]
}
```

#### 请求头要求


 - 需要携带有效的JWT令牌进行身份验证

#### 业务规则


 1. 每个用户只能绑定一次推荐关系
 2. 推荐码必须是有效的且存在的用户推荐码
 3. 不能绑定自己的推荐码
 4. 不能与已经建立推荐关系的用户重复绑定

#### 错误代码


 - 400: 参数验证失败
 - 401: 未授权访问
 - 404: 推荐码无效或不存在
 - 409: 已存在推荐关系

**接口状态**

> 开发中

**接口URL**

> api/referral/bind

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "code":"1B2A4E"
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 完成领取任务

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-01-20 18:04:57

> 更新时间: 2025-04-14 11:33:57

#### 接口说明

**该接口用于完成指定的任务。用户需要提供任务ID，系统会验证任务的有效性并更新任务状态。**

#### 请求URL

```
POST /api/tasks/complete
```

#### 请求头

```
Authorization: Bearer <token>
```

#### 请求参数

```json
{
  "taskId": 1  // 任务ID，必须为正整数
}
```

#### 响应格式

##### 成功响应

```json
{
    "ok": true,
    "data": {
        "message": "Task completed successfully",
        "task": {
            "id": 1,
            "name": "每日奖励 +1 宝箱",
            "type": "DAILY_SIGNIN",
            "repeatInterval": "day",
            "createdAt": "2025-03-27 09:44:22",
            "updatedAt": "2025-03-27 09:44:22"
        },
        "chestReward": {
            "openedCount": 1,
            "chestIds": [
                10
            ],
            "rewards": [
                {
                    "level": 1,
                    "items": [
                        {
                            "type": "fragment_green",
                            "amount": 19
                        },
                        {
                            "type": "gem",
                            "amount": 4468
                        }
                    ]
                }
            ],
            "summary": {
                "ticket": 0,
                "fragment_green": 19,
                "fragment_blue": 0,
                "fragment_purple": 0,
                "fragment_gold": 0,
                "ton": 0,
                "gem": 4468
            },
            "levelSummary": {
                "level1": 1,
                "level2": 0,
                "level3": 0,
                "level4": 0
            },
            "shareLinks": [],
            "jackpotWinner": {
                            "level": 1,  //获奖池子等级
                            "amount": 10,
                            "userId": 1,
                            "walletId": 1,
                            "poolId": 1,
                            "winTime": "2025-04-06T05:57:42.861Z"
                    }
        }
    },
    "message": "Task completed successfully"
}
```

#### 请求头要求


 - 需要携带有效的JWT令牌进行身份验证

#### 业务规则


 1. 用户必须已登录并通过钱包认证
 2. 任务ID必须存在且有效
 3. 同一任务不能重复完成
 4. 任务完成后会更新用户的任务状态

#### 错误代码


 - 400: 参数验证失败
 - taskId无效或缺失
 - taskId不是正整数
 - 
 - 
 - 401: 未授权访问
 - 缺少token或token无效
 - 
 - 
 - 404: 任务不存在
 - 409: 任务已完成

**接口需要钱包认证，请确保在请求头中包含有效的token**

**taskId必须为正整数**

**建议在完成任务前先调用任务列表接口检查任务状态**

**任务完成后的奖励将自动发放到用户钱包**

**接口状态**

> 开发中

**接口URL**

> api/tasks/complete

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "taskId":2
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
	"ok": true,
	"data": {
		"message": "Task completed successfully",
		"task": {
			"id": 1,
			"name": "每日奖励 +1 宝箱",
			"type": "DAILY_SIGNIN",
			"repeatInterval": "day",
			"createdAt": "2025-03-27 09:44:22",
			"updatedAt": "2025-03-27 09:44:22"
		},
		"chestReward": {
			"openedCount": 1,
			"chestIds": [
				10
			],
			"rewards": [
				{
					"level": 1,
					"items": [
						{
							"type": "fragment_green",
							"amount": 19
						},
						{
							"type": "gem",
							"amount": 4468
						}
					]
				}
			],
			"summary": {
				"ticket": 0,
				"fragment_green": 19,
				"fragment_blue": 0,
				"fragment_purple": 0,
				"fragment_gold": 0,
				"ton": 0,
				"gem": 4468
			},
			"levelSummary": {
				"level1": 1,
				"level2": 0,
				"level3": 0,
				"level4": 0
			},
			"shareLinks": [],
			"jackpotWinner": null
		}
	},
	"message": "Task completed successfully"
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 任务列表

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-01-20 22:57:36

> 更新时间: 2025-04-14 11:01:35

#### 接口说明

**该接口用于获取用户的任务列表及其完成状态。接口会返回所有可用任务及用户对应的完成情况，并支持多语言显示任务名称。**

#### 请求URL

```
GET /api/tasks
```

#### 请求头

```
Authorization: Bearer <token>
```

#### 请求参数

**无需请求参数**

#### 响应格式

##### 成功响应

```json
{
  "ok": true,
  "data": [
    {
      "id": 1,           // 任务ID
      "type": "string",  // 任务类型
      "name": "string",  // 任务名称（已翻译）
      "completed": true,  // 是否已完成
      "reward": 100      // 任务奖励
    }
  ]
}
```

##### 错误响应

```json
{
  "ok": false,
  "message": "string"  // 错误信息
}
```

#### 业务规则


 1. 用户必须已登录并通过钱包认证
 2. 任务名称会根据请求头中的语言设置自动翻译
 3. 系统会自动追踪任务的完成状态
 4. 已完成的任务会显示completed为true

#### 错误代码


 - 400: 获取任务列表失败
 - 401: 未授权访问
 - 缺少token或token无效

**缺少token或token无效**

#### 调用示例

**请求：**

```bash
curl -X GET https://https://wolf.jpegonapechain.com/api/tasks \
  -H "Authorization: Bearer <your-token>" \
  -H "Accept-Language: zh-CN"
```

**成功响应：**

```json
{
  "ok": true,
  "data": [
    {
      "id": 1,
      "type": "dailySignin",
      "name": "每日签到",
      "completed": false,
      "reward": 100
    },
    {
      "id": 2,
      "type": "joinTelegram",
      "name": "加入电报群",
      "completed": true,
      "reward": 200
    }
  ]
}
```

**错误响应：**

```json
{
  "ok": false,
  "message": "获取任务列表失败"
}
```

#### 注意事项


 1. 接口需要钱包认证，请确保在请求头中包含有效的token
 2. 建议在显示任务列表时同时显示任务奖励和完成状态
 3. 任务完成状态实时更新，建议定期刷新列表
 4. 支持多语言，会根据Accept-Language请求头返回对应语言的任务名称

**接口状态**

> 开发中

**接口URL**

> api/tasks

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Accept-Language | zh | string | 是 | - |

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Accept-Language | zh | string | 是 | - |

**Query**

# 用户信息

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-01-21 15:32:08

> 更新时间: 2025-04-07 15:17:41

## 用户信息查询接口

### 接口说明

**该接口用于获取当前登录用户的详细信息，包括用户基本信息和钱包信息。需要用户通过钱包认证后才能访问。**

### 请求URL

```
GET /api/user/me
```

### 请求头

```
Authorization: Bearer <token>
```

### 请求参数

**无需请求参数**

### 响应格式

#### 成功响应

```json
{
    "ok": true,
    "data": [
        {
            "referralCount": 0,
            "telegramId": "5160149730",
            "username": "Zoyonlian",
            "firstName": "Zo",
            "lastName": "YonLian",
            "photoUrl": "",
            "telegram_premium": false,
            "walletAddress": "0QDioPTSTodOvpJe5czY963Jry4QilH3tMBzZn-1vFbhNmLO",
            "referral": {
                "walletAddress": null
            },
            "gem": 3846,
            "ton": 0,
            "usd": 80090,
            "ticket": 100107,
                        "free_ticket":1,//免费门票
            "moof": 100,  //未解锁数量
            "unlockMoof":1000, //已解数量
            "fragment_green":1, //绿色碎片
            "fragment_blue":1, //蓝色碎片
            "fragment_purple":1, //紫色碎片
            "fragment_gold":1, //金色碎片
            "code": "F82A3D",
            "createdAt": "2025-03-02 08:21:51",
            "email": "<EMAIL>" //已绑定的邮箱地址
        }
    ]
}
```

#### 错误响应

```json
{
  "ok": false,
  "message": "string"  // 错误信息
}
```

### 错误代码


 - 400: 获取用户信息失败
 - 401: 未授权访问
 - 缺少token或token无效
 - 用户未完成钱包认证

**用户未完成钱包认证**

**错误响应：**

```json
{
  "ok": false,
  "message": "获取用户信息失败"
}
```

### 注意事项


 1. 接口需要钱包认证，请确保在请求头中包含有效的token
 2. token可以通过钱包认证接口获取
 3. 用户信息实时更新，建议按需获取最新数据
 4. 接口有频率限制，请合理调用

**接口状态**

> 开发中

**接口URL**

> api/user/me

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 推荐列表

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-01-21 17:31:06

> 更新时间: 2025-03-03 16:51:20

## 推荐列表查询接口

### 接口信息


 - 接口路径: /api/referral/list
 - 请求方式: GET
 - 接口描述: 获取当前用户的推荐列表信息

**接口描述: 获取当前用户的推荐列表信息**

### 认证要求

**该接口需要进行钱包认证，请在请求头中包含有效的认证令牌：**

```
Authorization: Bearer <token>
```

### 请求参数

**无需请求参数**

### 响应格式

#### 成功响应

```json
{
  "code": 0,
  "data": {
    // 推荐列表数据
  },
  "message": "获取推荐列表成功"
}
```

#### 错误响应

```json
{
  "code": 400,
  "message": "错误信息",
  "data": null
}
```

### 错误码说明

| 状态码 | 说明  |
| --- | --- |
| 400 | 请求参数错误 |
| 401 | 未授权或Token无效 |
| 500 | 服务器内部错误 |

### 注意事项


 1. 接口需要有效的钱包认证Token
 2. 用户ID必须存在，否则将返回错误
 3. 返回的数据包含用户的推荐相关信息

**接口状态**

> 开发中

**接口URL**

> api/referral/list

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 购买门票

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-01-22 10:26:08

> 更新时间: 2025-03-03 16:53:56

## 购买门票接口

### 接口信息


 - 接口路径: /api/ticket/purchase
 - 请求方式: POST
 - 接口描述: 用于购买指定数量的门票

**接口描述: 用于购买指定数量的门票**

### 认证要求


 - 需要在请求头中携带有效的认证令牌
 - Header: Authorization: Bearer <token>

```
Authorization: Bearer <token>
```

### 请求参数

#### 请求体 (JSON)

```json
{
  "quantity": number // 购买门票数量，必须为正整数
}
```

#### 参数说明

| 参数名 | 类型  | 必填  | 说明  |
| --- | --- | --- | --- |
| quantity | integer | 是 | 购买门票数量，最小值为1 |

### 响应格式

#### 成功响应

```json
{
  "code": 0,
  "msg": "购买成功",
  "data": {
    "balance": {
      "usd": number,    // 用户剩余USD余额
      "ticket": number  // 用户剩余门票数量
    }
  }
}
```

#### 错误响应

```json
{
  "code": 400,
  "msg": "错误信息",
  "data": null
}
```

### 错误码说明

| 状态码 | 说明  |
| --- | --- |
| 400 | 参数验证失败 |
| 400 | 余额不足 |
| 401 | 未授权或Token无效 |

### 注意事项


 1. 请确保在发起请求前已完成用户认证
 2. 购买数量必须为正整数
 3. 用户需要有足够的USD余额才能完成购买
 4. 接口会返回更新后的USD余额和门票数量

### 示例

#### 请求示例

```bash
curl -X POST "http://api.example.com/api/ticket/purchase" \
     -H "Authorization: Bearer your-token-here" \
     -H "Content-Type: application/json" \
     -d '{"quantity": 1}'
```

#### 成功响应示例

```json
{
  "code": 0,
  "msg": "购买成功",
  "data": {
    "balance": {
      "usd": 95.5,
      "ticket": 3
    }
  }
}
```

**接口状态**

> 开发中

**接口URL**

> api/ticket/purchase

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Accept-Language | zh | string | 是 | - |

**请求Body参数**

```javascript
{
    "quantity":1
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Accept-Language | zh | string | 是 | - |

**Query**

# 钱包记录

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-01-22 17:39:01

> 更新时间: 2025-03-03 16:56:27

## 钱包历史记录查询接口

### 接口说明

**获取用户的钱包历史流水记录，支持分页查询功能。**

### 接口信息


 - 接口路径: /api/wallet/history
 - 请求方式: GET
 - 认证要求: 需要钱包认证（walletAuthMiddleware）

**认证要求: 需要钱包认证（walletAuthMiddleware）**

### 请求参数

#### Query 参数

| 参数名 | 类型  | 是否必需 | 默认值 | 说明  |
| --- | --- | ---- | --- | --- |
| page | string | 否 | "1" | 页码，必须为正整数 |
| limit | string | 否 | "20" | 每页记录数，必须为正整数 |

#### 参数验证规则


 - page: 必须匹配正则表达式 ^[0-9]+$
 - limit: 必须匹配正则表达式 ^[0-9]+$
 - 不允许其他额外参数

```
^[0-9]+$
```

**不允许其他额外参数**

### 响应格式

#### 成功响应

```json
{
  "success": true,
  "message": "获取钱包历史记录成功",
  "data": {
    "records": [
      {
        "id": "记录ID",
        "amount": "金额",
        "currency": "货币类型",
        "reference": "关联引用",
        "createdAt": "创建时间",
        "action": "操作类型",
        "fe_display_remark": "前端显示备注",
        "developer_remark": "开发者备注"
      }
    ],
    "total": "总记录数",
    "page": "当前页码",
    "limit": "每页条数",
    "totalPages": "总页数"
  }
}
```

#### 错误响应

```json
{
  "success": false,
  "message": "错误信息",
  "errors": ["详细错误说明"]
}
```

### 错误码说明

| 状态码 | 说明  |
| --- | --- |
| 400 | 参数验证失败 |
| 400 | 获取钱包历史记录失败 |

### 注意事项


 1. 分页参数（page和limit）如果为负数或0，会被自动调整：
 2. 
 3. 负数的page会被设置为1
 4. 负数的limit会被设置为20
 5. 
 6. 
 7. 历史记录按创建时间倒序排列（最新记录在前）
 8. 
 9. 返回的记录包含以下字段：
 10. 
 11. id: 记录唯一标识
 12. amount: 交易金额
 13. currency: 货币类型
 14. reference: 交易关联引用
 15. createdAt: 创建时间
 16. action: 操作类型
 17. fe_display_remark: 前端显示备注
 18. developer_remark: 开发者备注

### 调用示例

#### 请求示例

```
GET /api/wallet/history?page=1&limit=20
Authorization: Bearer <token>
```

#### 成功响应示例

```json
{
  "success": true,
  "message": "获取钱包历史记录成功",
  "data": {
    "records": [
      {
        "id": 1,
        "amount": 100.00,
        "currency": "USD",
        "reference": "DEPOSIT",
        "createdAt": "2024-01-20T08:30:00Z",
        "action": "deposit",
        "fe_display_remark": "充值",
        "developer_remark": "用户充值"
      }
    ],
    "total": 50,
    "page": 1,
    "limit": 20,
    "totalPages": 3
  }
}
```

**接口状态**

> 开发中

**接口URL**

> api/wallet/history

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| limit | 1 | string | 是 | - |
| page | 1 | string | 是 | - |

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取宝箱总数

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-01-24 09:10:43

> 更新时间: 2025-03-03 16:58:34

## 宝箱数量查询接口

### 接口信息


 - 接口路径: /api/chest/count
 - 请求方式: GET
 - 接口描述: 获取用户当前拥有的宝箱数量

**接口描述: 获取用户当前拥有的宝箱数量**

### 认证要求


 - 需要在请求头中携带有效的身份验证令牌（Bearer Token）
 - 用户必须已登录且钱包已绑定

### 请求参数

**无需请求参数**

### 响应格式

#### 成功响应

```json
{
  "code": 0,
  "msg": "获取宝箱数量成功",
  "data": {
    "count": 5  // 用户当前拥有的宝箱数量
  }
}
```

#### 错误响应


 1. 未提供钱包ID

```json
{
  "code": 1,
  "msg": "缺少钱包ID",
  "data": null
}
```


 1. 认证失败

```json
{
  "code": 1,
  "msg": "认证失败",
  "data": null
}
```

### 注意事项


 1. 返回的宝箱数量包括用户通过各种途径获得的所有宝箱
 2. 如果用户未绑定钱包，将返回错误信息

### 使用示例

```bash
curl -X GET \
  'http://api.example.com/api/chest/count' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE'
```

**接口状态**

> 开发中

**接口URL**

> api/chest/count

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取全部session

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-02-07 17:11:58

> 更新时间: 2025-03-03 17:02:14

## 获取全部游戏场次接口

### 接口说明

**获取当天所有游戏场次信息，包括每个场次的所有回合状态。每个场次包含3个回合（sub_session）。**

### 请求信息

#### 请求URL

```
GET /api/game/all_sessions
```

#### 请求参数

**无需参数**

#### 请求头

**无需特殊请求头**

### 响应信息

#### 响应格式

```json
{
  "success": true,
  "message": "获取场次信息成功",
  "data": {
    "all_session": [
      {
        "session_category": "04:00:00",
        "session_dt": "2025-02-07 04:00:00",
        "sub_session": [
          {
            "start_time": "2025-02-07 04:00:00",
            "result_time": "2025-02-07 04:10:00",
            "room_status": "done",
            "room_count": 32,
            "round_id": "ROUND 1 - 1"
          }
        ]
      }
    ]
  }
}
```

#### 响应字段说明

##### all_session 数组项

| 字段  | 类型  | 说明  |
| --- | --- | --- |
| session_category | string | 场次类别，例如："04:00:00" |
| session_dt | string | 场次日期时间，格式：YYYY-MM-DD HH:mm:ss |
| sub_session | array | 回合信息数组，包含3个回合 |

##### sub_session 数组项

| 字段  | 类型  | 说明  |
| --- | --- | --- |
| start_time | string | 回合开始时间，格式：YYYY-MM-DD HH:mm:ss |
| result_time | string | 回合结果时间，格式：YYYY-MM-DD HH:mm:ss |
| room_status | string | 房间状态，"done"表示已结束，其他状态表示进行中 |
| room_count | number | 当前回合的房间数量，仅在room_status为"done"时显示 |
| round_id | string | 回合ID标识 |

### 注意事项


 1. 接口仅返回当天的场次信息
 2. 每个场次包含3个回合，每个回合间隔10分钟
 3. 回合状态在结果时间2分钟前会自动变更为"done"
 4. room_count字段仅在回合状态为"done"时才会显示具体数值，其他状态显示为0

### 错误响应

#### 服务器错误

```json
{
  "success": false,
  "message": "服务器错误",
  "error": "错误详细信息"
}
```

### 示例

#### 请求示例

```bash
curl -X GET "http://your-domain.com/api/game/all_sessions"
```

#### 成功响应示例

```json
{
  "success": true,
  "message": "获取场次信息成功",
  "data": {
    "all_session": [
      {
        "session_category": "04:00:00",
        "session_dt": "2025-02-07 04:00:00",
        "sub_session": [
          {
            "start_time": "2025-02-07 04:00:00",
            "result_time": "2025-02-07 04:10:00",
            "room_status": "done",
            "room_count": 32,
            "round_id": "ROUND 1 - 1"
          },
          {
            "start_time": "2025-02-07 04:10:00",
            "result_time": "2025-02-07 04:20:00",
            "room_status": "in_progress",
            "room_count": 0,
            "round_id": "ROUND 1 - 2"
          }
        ]
      }
    ]
  }
}
```

**接口状态**

> 开发中

**接口URL**

> api/game/all_sessions

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 预约场次

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-02-08 17:31:07

> 更新时间: 2025-04-08 15:47:46

## 游戏预约接口

### 接口信息


 - 接口路径: /api/game/reserve
 - 请求方式: POST
 - 接口描述: 用于预约指定场次和回合的游戏

**接口描述: 用于预约指定场次和回合的游戏**

### 请求参数

#### 请求头

| 参数名 | 必选  | 类型  | 说明  |
| --- | --- | --- | --- |
| Authorization | 是 | string | Bearer Token认证信息 |

#### 请求体

```json
{
  "sessions": [
    {
      "session_dt": "2025-03-03 12:00:00",
      "session_category": "12:00:00",
      "rounds": [1,2,3],
      "tickets": ["ticket", "free_ticket", "ticket"]
    },
    {
      "session_dt": "2025-03-03 20:00:00",
      "session_category": "20:00:00",
      "rounds": [1,2,3],
      "tickets": ["ticket", "ticket", "free_ticket"]
    }
  ]
}
```

| 参数名 | 必选  | 类型  | 说明  |
| --- | --- | --- | --- |
| sessions | 是 | array | 预约场次信息数组 |
| session_dt | 是 | string | 场次日期和时间（格式：YYYY-MM-DD HH:mm:ss） |
| session_category | 是 | string | 场次时间（格式：HH:mm:ss） |
| rounds | 是 | array | 预约的回合数组（数组元素为1-3之间的整数） |
| tickets | 是 | array | 指定每个回合使用的票类型数组，可选值为"ticket"(普通票)或"free_ticket"(免费票)，数组长度必须与rounds数组长度一致 |

### 响应参数

#### 成功响应

```json
{
    "ok": true,
    "data": {
        "reservations": [
            {
                "sessionId": 4,
                "reservations": [
                    {
                        "roundIndex": 1,
                        "roomId": "1",
                        "ticketType": "ticket"
                    },
                    {
                        "roundIndex": 2,
                        "roomId": "2",
                        "ticketType": "free_ticket"
                    },
                    {
                        "roundIndex": 3,
                        "roomId": "3",
                        "ticketType": "ticket"
                    }
                ]
            },
            {
                "sessionId": 4,
                "reservations": [
                    {
                        "roundIndex": 1,
                        "roomId": "4",
                        "ticketType": "ticket"
                    },
                    {
                        "roundIndex": 2,
                        "roomId": "5",
                        "ticketType": "ticket"
                    },
                    {
                        "roundIndex": 3,
                        "roomId": "6",
                        "ticketType": "free_ticket"
                    }
                ]
            }
        ]
    },
    "message": "预约成功"
}
```

#### 错误响应

```json
{
  "success": false,
  "message": "错误信息"
}
```

### 注意事项


 1. 用户必须完成身份认证才能使用该接口
 2. 每个用户在同一场次只能预约一次
 3. 预约成功后会自动分配到一个游戏房间
 4. 预约状态说明：
 5. reserved: 已预约
 6. refunded: 已退款
 7. success: 预约成功且游戏已完成
 8. failed: 预约失败

**failed: 预约失败**

**接口状态**

> 开发中

**接口URL**

> api/game/reserve

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Accept-Language | zh | string | 是 | - |

**请求Body参数**

```javascript
 {
    "sessions": [
      {
        "session_dt": "{{session_dt}}",
        "session_category": "{{session_category}}",
        "rounds": {{rounds1}},
        "tickets":["ticket","free_ticket"]
      },
      {
        "session_dt": "{{session_dt}}",
        "session_category": "{{session_category}}",
        "rounds": {{rounds2}},
        "tickets":["ticket"]
      }
    ]
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
	"ok": true,
	"data": {
		"reservations": [
			{
				"sessionId": 14,
				"reservations": [
					{
						"roundIndex": 1,
						"roomId": "2",
						"ticketType": "ticket"
					},
					{
						"roundIndex": 2,
						"roomId": "3",
						"ticketType": "free_ticket"
					}
				]
			},
			{
				"sessionId": 14,
				"reservations": [
					{
						"roundIndex": 3,
						"roomId": "4",
						"ticketType": "ticket"
					}
				]
			}
		]
	},
	"message": "预约成功"
}
```

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Accept-Language | zh | string | 是 | - |

**Query**

# 测试-加测试币

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-02-11 18:04:31

> 更新时间: 2025-04-15 16:18:03

## 添加测试币接口

### 接口说明

**该接口用于为用户钱包添加测试币，支持单个用户或所有用户的批量添加操作。**


 - 接口路径: /api/reset/add-test-coin
 - 请求方式: POST
 - 需要认证: 是（需要钱包认证中间件）

**需要认证: 是（需要钱包认证中间件）**

### 请求参数

#### 请求体（JSON）

```json
{
  "fields": {
    "ton": number,
    "gem": number,
    "ticket": number,
    "usd": number,
    "moof": number,
    "unlockMoof": number
  },
  "targetAll": boolean
}
```

#### 参数说明


 - fields: 对象，包含要添加的测试币类型和数量
 - 所有字段值必须为大于 0.000001 的数字
 - 支持的字段类型：
 - ton: TON 代币数量
 - gem: 宝石数量
 - ticket: 门票数量
 - usd: USD 数量
 - moof: MOOF 代币数量
 - unlockMoof: 解锁的 MOOF 数量
 - "fragment_green",
 - "fragment_blue",
 - "fragment_purple",
 - "fragment_gold"
 - 
 - 
 - 
 - 
 - targetAll: 布尔值，可选，默认为 false
 - true: 更新所有用户的钱包
 - false: 仅更新当前用户的钱包

**"fragment_purple",**

**"fragment_gold"**

**targetAll: 布尔值，可选，默认为 false
true: 更新所有用户的钱包
false: 仅更新当前用户的钱包

**

```
targetAll
```


 - true: 更新所有用户的钱包
 - false: 仅更新当前用户的钱包

**false: 仅更新当前用户的钱包**

```
false
```

### 响应结果

#### 成功响应

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "updatedCount": number
  }
}
```

#### 参数说明


 - updatedCount: 更新成功的钱包数量

```
updatedCount
```

### 错误码

| 状态码 | 错误说明 |
| --- | ---- |
| 400 | 参数验证失败 |
| 400 | 无效的字段 |
| 400 | 无效的字段名称 |
| 400 | 无效的字段值 |
| 400 | 没有有效的更新字段 |
| 401 | 未授权 |
| 500 | 服务器错误 |

### 请求示例

#### 为当前用户添加测试币

```json
{
  "fields": {
    "ton": 100,
    "usd": 50,
    "ticket": 10
  }
}
```

#### 为所有用户添加测试币

```json
{
  "fields": {
    "moof": 1000,
    "gem": 50
  },
  "targetAll": true
}
```

### 注意事项


 1. 所有数值字段必须大于 0.000001
 2. 至少需要提供一个有效的字段
 3. 不支持的字段名称将被忽略并返回错误
 4. 当 targetAll 为 false 且用户未登录时将返回未授权错误

**接口状态**

> 开发中

**接口URL**

> api/reset/add-test-coin

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
  "fields": {
    "usd": 10000,
    "ticket": 5000,
    "moof": 20000
  },
  "targetAll": true
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 我的预约记录

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-02-13 16:45:07

> 更新时间: 2025-03-03 17:23:22

## 我的预约查询接口

### 接口信息


 - 接口路径: /api/game/my_reservations
 - 请求方法: GET
 - 接口描述: 获取当前用户在当天的所有预约记录

**接口描述: 获取当前用户在当天的所有预约记录**

### 认证要求


 - 请求头需要包含有效的 JWT Token

### 请求参数

**此接口不需要任何查询参数。**

### 响应格式

#### 成功响应

```json
{
  "code": 0,
  "msg": "获取预约记录成功",
  "data": {
    "reservations": [
      {
        "id": "预约ID",
        "userId": "用户ID",
        "walletId": "钱包ID",
        "sessionId": "场次ID",
        "roundIndex": "回合索引",
        "reservedAt": "预约时间",
        "Session": {
          "session_dt": "场次日期时间",
          "session_category": "场次类别"
        },
        "Round": {
          "result_time": "结果时间"
        }
      }
    ]
  }
}
```

#### 错误响应


 1. 参数验证错误 (400)

```json
{
  "code": 400,
  "msg": "参数验证失败",
  "errors": ["具体错误信息"]
}
```


 1. 用户或钱包ID缺失 (400)

```json
{
  "code": 400,
  "msg": "缺少用户ID或钱包ID"
}
```


 1. 服务器错误 (500)

```json
{
  "code": 500,
  "msg": "服务器错误",
  "error": "具体错误信息"
}
```

### 注意事项


 1. 接口仅返回当天（按中国时区 Asia/Shanghai）的预约记录
 2. 预约记录按时间顺序排列
 3. 需要确保请求头中包含有效的认证信息

### 数据说明


 - session_dt: 场次的日期时间
 - session_category: 场次类别
 - result_time: 回合结果时间
 - reservedAt: 用户进行预约的时间

**result_time: 回合结果时间**

```
result_time
```

**reservedAt: 用户进行预约的时间**

```
reservedAt
```

### 使用示例

#### 请求示例

```bash
curl -X GET \
  'http://api.example.com/api/my_reservations' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN'
```

#### 响应示例

```json
{
  "code": 0,
  "msg": "获取预约记录成功",
  "data": {
    "reservations": [
      {
        "id": 1,
        "userId": 123,
        "walletId": "wallet123",
        "sessionId": 456,
        "roundIndex": 1,
        "reservedAt": "2025-02-07T08:30:00.000Z",
        "Session": {
          "session_dt": "2025-02-07T08:00:00.000Z",
          "session_category": "08:00:00"
        },
        "Round": {
          "result_time": "2025-02-07T08:40:00.000Z"
        }
      }
    ]
  }
}
```

**接口状态**

> 开发中

**接口URL**

> api/game/my_reservations

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 领取每日推荐宝箱

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-02-24 15:24:39

> 更新时间: 2025-04-15 17:41:48

## 每日宝箱领取接口

### 接口信息


 - 接口路径: /api/invite/claim-daily
 - 请求方法: POST
 - 认证要求: 需要钱包认证（Bearer Token）

**认证要求: 需要钱包认证（Bearer Token）**

### 接口描述

**该接口用于领取用户的每日宝箱奖励。每个用户每天可以领取一次宝箱奖励。**

### 请求头

```
Authorization: Bearer <token>
```

### 请求参数

**请求体为空对象，不需要任何参数。**

```json
{}
```

### 响应格式

#### 成功响应

```json
{
    "ok": true,
    "data": {
        "dailyChests": 1,
        "rewards": {
            "openedCount": 1,
            "chestIds": [
                15
            ],
            "rewards": [
                {
                    "level": 2,
                    "items": [
                        {
                            "type": "fragment_green",
                            "amount": 11
                        },
                        {
                            "type": "fragment_blue",
                            "amount": 4
                        },
                        {
                            "type": "gem",
                            "amount": 5735
                        }
                    ]
                }
            ],
            "summary": {
                "ticket": 0,
                "fragment_green": 11,
                "fragment_blue": 4,
                "fragment_purple": 0,
                "fragment_gold": 0,
                "ton": 0,
                "gem": 5735
            },
            "levelSummary": {
                "level1": 0,
                "level2": 1,
                "level3": 0,
                "level4": 0
            },
            "shareLinks": [],
            "jackpotWinner": null
        }
    },
    "message": "Successfully claimed 1 daily chests"
}
```

#### 错误响应

```json
{
  "success": false,
  "message": string,  // 错误信息
  "errors": array     // 可选，详细错误信息
}
```

### 错误码


 - 400: 参数验证失败或请求无效
 - 401: 未授权（token无效或已过期）

### 注意事项


 1. 接口需要有效的钱包认证token
 2. 用户ID必须存在且有效
 3. 每个用户每天只能领取一次宝箱
 4. 接口会根据用户设置的语言返回对应语言的提示信息

### 示例

#### 请求示例

```bash
curl -X POST \
  'http://api.example.com/api/invite/claim-daily' \
  -H 'Authorization: Bearer <your-token>' \
  -H 'Content-Type: application/json' \
  -d '{}'
```

#### 成功响应示例

```json
{
  "success": true,
  "data": {
    "dailyChests": 1
  },
  "message": "成功领取每日宝箱"
}
```

#### 错误响应示例

```json
{
  "success": false,
  "message": "今日已领取宝箱"
}
```

**接口状态**

> 开发中

**接口URL**

> api/invite/claim-daily

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
	"ok": true,
	"data": {
		"dailyChests": 1,
		"rewards": {
			"openedCount": 1,
			"chestIds": [
				15
			],
			"rewards": [
				{
					"level": 2,
					"items": [
						{
							"type": "fragment_green",
							"amount": 11
						},
						{
							"type": "fragment_blue",
							"amount": 4
						},
						{
							"type": "gem",
							"amount": 5735
						}
					]
				}
			],
			"summary": {
				"ticket": 0,
				"fragment_green": 11,
				"fragment_blue": 4,
				"fragment_purple": 0,
				"fragment_gold": 0,
				"ton": 0,
				"gem": 5735
			},
			"levelSummary": {
				"level1": 0,
				"level2": 1,
				"level3": 0,
				"level4": 0
			},
			"shareLinks": [],
			"jackpotWinner": null
		}
	},
	"message": "Successfully claimed 1 daily chests"
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取游戏记录

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-02-24 16:05:57

> 更新时间: 2025-03-03 17:30:07

## 游戏历史查询接口

### 接口信息


 - 接口路径: /api/game/history
 - 请求方法: GET
 - 认证要求: 需要钱包认证（Bearer Token）

**认证要求: 需要钱包认证（Bearer Token）**

### 接口说明

**该接口用于查询用户的游戏历史记录，支持分页查询。返回的历史记录按创建时间倒序排列，包含游戏场次、回合、投注和支付等详细信息。**

### 请求参数

#### 查询参数（Query Parameters）

| 参数名 | 类型  | 必填  | 说明  | 示例  |
| --- | --- | --- | --- | --- |
| page | string | 否 | 页码，默认值：1 | "1" |
| limit | string | 否 | 每页记录数，默认值：10 | "10" |

### 响应格式

#### 成功响应

```json
{
    "ok": true,
    "data": [
        {
            "id": 2,
            "userId": 1,
            "walletId": 1,
            "session": 4,
            "session_category": "20:00:00",
            "session_dt": "2025-03-03 20:00:00",
            "round": 2,
            "betAmount": 100,
            "game_status": "refunded",
            "payout_status": "refunded",
            "payout": 0,
            "is_moof": false,
            "roomId": "2",
            "createdAt": "2025-03-03 17:08:43"
        },
        {
            "id": 3,
            "userId": 1,
            "walletId": 1,
            "session": 4,
            "session_category": "20:00:00",
            "session_dt": "2025-03-03 20:00:00",
            "round": 3,
            "betAmount": 100,
            "game_status": "refunded",
            "payout_status": "refunded",
            "payout": 0,
            "is_moof": false,
            "roomId": "3",
            "createdAt": "2025-03-03 17:08:43"
        },
        {
            "id": 1,
            "userId": 1,
            "walletId": 1,
            "session": 4,
            "session_category": "20:00:00",
            "session_dt": "2025-03-03 20:00:00",
            "round": 1,
            "betAmount": 100,
            "game_status": "refunded",
            "payout_status": "refunded",
            "payout": 0,
            "is_moof": false,
            "roomId": "1",
            "createdAt": "2025-03-03 17:08:42"
        }
    ],
    "message": "Game history retrieved successfully",
    "pagination": {
        "pagination": {
            "total": 3,
            "page": 1,
            "limit": 10,
            "totalPages": 1
        }
    }
}
```

#### 响应字段说明

##### 游戏历史记录字段

| 字段名 | 类型  | 说明  |
| --- | --- | --- |
| id | number | 历史记录ID |
| userId | number | 用户ID |
| walletId | string | 钱包ID |
| session | number | 场次ID |
| session_category | string | 场次类别 |
| session_dt | string | 场次日期时间 |
| round | number | 回合数 |
| betAmount | number | 投注金额 |
| game_status | string | 游戏状态 |
| payout_status | string | 支付状态 |
| payout | number | 支付金额 |
| is_moof | boolean | 是否为MOOF |
| roomId | string | 房间ID |
| createdAt | string | 记录创建时间 |

##### 分页信息字段

| 字段名 | 类型  | 说明  |
| --- | --- | --- |
| total | number | 总记录数 |
| page | number | 当前页码 |
| limit | number | 每页记录数 |
| totalPages | number | 总页数 |

#### 错误响应

```json
{
  "success": false,
  "message": "参数验证失败",
  "error": "页码必须是正整数"
}
```

### 错误码说明

| HTTP状态码 | 说明  |
| ------- | --- |
| 400 | 请求参数验证失败 |
| 401 | 未授权或Token无效 |
| 500 | 服务器内部错误 |

### 注意事项


 1. 分页参数必须是正整数
 2. 需要在请求头中携带有效的认证Token
 3. 历史记录按创建时间倒序排列

**接口状态**

> 开发中

**接口URL**

> api/game/history

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 牛王宝座排行榜

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-02-28 22:45:32

> 更新时间: 2025-03-03 17:34:35

## 牛王宝座排行榜接口

### 接口信息


 - 接口路径: /api/bull-king/leaderboard
 - 请求方式: GET
 - 接口描述: 获取牛王宝座排行榜数据，展示用户排名和相关信息

**接口描述: 获取牛王宝座排行榜数据，展示用户排名和相关信息**

### 认证要求


 - 需要在请求头中携带有效的身份认证token
 - 使用 Bearer Token 认证方式

```
Authorization: Bearer <your-token>
```

### 请求参数

#### 查询参数 (Query Parameters)

| 参数名 | 类型  | 是否必需 | 默认值 | 描述  |
| --- | --- | ---- | --- | --- |
| limit | Number | 否 | 100 | 限制返回的记录数量，必须大于0 |

### 响应格式

#### 成功响应

```json
{
    "ok": true,
    "data": {
        "leaderboard": [],
        "poolAmount": 0,
        "userRank": null,
        "userReward": 0,
        "userTransformCount": "0"
    }
}
```

#### 错误响应

```json
{
  "success": false,
  "message": "错误信息",
  "errors": ["详细错误说明"]
}
```

### 错误码说明

| 状态码 | 说明  |
| --- | --- |
| 400 | 参数验证失败 |
| 401 | 未授权访问 |
| 500 | 服务器内部错误 |

### 注意事项


 1. limit参数必须是正整数
 2. 返回的数据按照排名顺序排序
 3. 接口有访问频率限制，请合理控制请求频率

**接口状态**

> 开发中

**接口URL**

> api/bull-king/leaderboard

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取MOOF持有者排行榜

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-01 00:03:37

> 更新时间: 2025-03-03 17:37:00

## MOOF持有者排行榜接口

### 接口说明

**获取MOOF持有者排行榜数据，展示持有者排名和相关信息。**

### 接口信息


 - 接口路径: /api/bull-king/moof-holders
 - 请求方式: GET
 - 需要认证: 是

**需要认证: 是**

### 请求参数

#### Query参数

| 参数名 | 类型  | 必填  | 说明  | 默认值 |
| --- | --- | --- | --- | --- |
| limit | number | 否 | 返回记录数量限制 | 100 |

#### 请求头

| 参数名 | 说明  |
| --- | --- |
| Authorization | Bearer Token认证信息 |

### 响应格式

#### 成功响应

```json
{
    "ok": true,
    "data": {
        "leaderboard": [
            {
                "walletId": 1,
                "userId": 1,
                "moof": 10400,
                "walletAddress": "0QDioPTSTodOvpJe5czY963Jry4QilH3tMBzZn-1vFbhNmLO",
                "username": "Zoyonlian",
                "photoUrl": "",
                "rank": 1
            },
            {
                "walletId": 8,
                "userId": 8,
                "moof": 10200,
                "walletAddress": "UQBww7-y-oc8yLVN49_TxmHE8rcWPYryoiyEBJx2y0rTKqq5",
                "username": "felerford",
                "photoUrl": "",
                "rank": 2
            },
            {
                "walletId": 2,
                "userId": 2,
                "moof": 10000,
                "walletAddress": "UQBhQxkk_fkznrqzwOAwrNJlHdp1el2f-AJoa1UBMUz-ERGb",
                "username": "",
                "photoUrl": "",
                "rank": 3
            },
            {
                "walletId": 3,
                "userId": 3,
                "moof": 10000,
                "walletAddress": "UQCD55QhtRuqNBYsxEMsh80ZIrvJ6GridII1gyBqI-NV4Rlr",
                "username": "zoyonlian",
                "photoUrl": "",
                "rank": 4
            },
            {
                "walletId": 4,
                "userId": 4,
                "moof": 10000,
                "walletAddress": "UQCQPmcEtqpGU2i730zxcdWGpdTRzJnmc2Sb29bCTW68zM0x",
                "username": "lisenton",
                "photoUrl": "",
                "rank": 5
            },
            {
                "walletId": 5,
                "userId": 5,
                "moof": 10000,
                "walletAddress": "UQCFV2JvWRQnWAuQXcOZfyDC3CdHwfAKRc8C-H8aYGFYE4VM",
                "username": "malinmar",
                "photoUrl": "",
                "rank": 6
            },
            {
                "walletId": 6,
                "userId": 6,
                "moof": 10000,
                "walletAddress": "UQCpVbPmM6MTsyKNco_m5tsEkWslgRqnC9Umpn0mka-n3sQg",
                "username": "xiverzen",
                "photoUrl": "",
                "rank": 7
            },
            {
                "walletId": 7,
                "userId": 7,
                "moof": 10000,
                "walletAddress": "UQBsrkSm0nP5Vdn_K5bwi98yNWLspZFEj12qyVAHxzuQyhkp",
                "username": "borondell",
                "photoUrl": "",
                "rank": 8
            },
            {
                "walletId": 9,
                "userId": 9,
                "moof": 10000,
                "walletAddress": "UQBU2J1dH9CA29N7TtErfmq543mSGUcGq1GEGgwl2bPNmr1g",
                "username": "jozenron",
                "photoUrl": "",
                "rank": 9
            },
            {
                "walletId": 10,
                "userId": 10,
                "moof": 10000,
                "walletAddress": "UQDP9eF7s_2IMrCT5faHucpwK2552cBv1-ndEjEUFRuVLCgO",
                "username": "kisonlian",
                "photoUrl": "",
                "rank": 10
            }
        ],
        "poolInfo": {
            "totalAmount": 0,
            "distributionAmount": 0
        },
        "userInfo": {
            "rank": 1,
            "reward": 0
        }
    }
}
```

#### 错误响应

```json
{
  "success": false,
  "message": "错误信息",
  "errors": ["详细错误说明"]
}
```

### 错误码说明

| 状态码 | 说明  |
| --- | --- |
| 400 | 参数验证失败 |
| 401 | 未授权访问 |
| 500 | 服务器内部错误 |

### 注意事项


 1. 接口需要通过钱包认证才能访问
 2. limit参数必须为正整数
 3. 默认返回前100条记录
 4. 返回数据按照持有量降序排序

**接口状态**

> 开发中

**接口URL**

> api/bull-king/moof-holders

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 领取牛王宝座奖励

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-01 00:08:27

> 更新时间: 2025-03-03 17:42:09

## 领取牛王宝座奖励

### 接口说明

**领取牛王宝座奖励的接口，用户需要先获得牛王宝座资格才能领取奖励。**

### 请求信息


 - 请求路径：/api/bull-king/claim
 - 请求方法：POST
 - 需要认证：是

```
POST
```

**需要认证：是**

#### 认证方式

**请求头中需要包含有效的 JWT Token：**

```
Authorization: Bearer <your-token>
```

#### 请求参数

**无需请求参数**

### 响应信息

#### 成功响应


 - 状态码：200
 - 响应格式：JSON

```json
{
  "success": true,
  "message": "成功领取牛王宝座奖励",
  "data": {
    "amount": 1000,
    "currency": "MOOF"
  }
}
```

#### 响应字段说明

| 字段名 | 类型  | 描述  |
| --- | --- | --- |
| success | boolean | 请求是否成功 |
| message | string | 响应消息 |
| data.amount | number | 领取的奖励数量 |
| data.currency | string | 奖励的货币类型 |

#### 错误响应


 - 状态码：400/401/500
 - 响应格式：JSON

```json
{
  "success": false,
  "message": "错误信息",
  "error": "详细错误说明"
}
```

#### 可能的错误码

| 状态码 | 错误信息 | 说明  |
| --- | ---- | --- |
| 400 | errors.walletNotFound | 钱包地址未找到 |
| 401 | errors.unauthorized | 未授权或Token无效 |
| 500 | errors.claimBullKingRewardFailed | 领取奖励失败 |

### 示例

#### 请求示例

```bash
curl -X POST \
  'https://api.example.com/api/bull-king/claim' \
  -H 'Authorization: Bearer your-token-here'
```

#### 成功响应示例

```json
{
  "success": true,
  "message": "成功领取牛王宝座奖励",
  "data": {
    "amount": 1000,
    "currency": "MOOF"
  }
}
```

#### 错误响应示例

```json
{
  "success": false,
  "message": "领取牛王宝座奖励失败",
  "error": "没有可领取的牛王宝座奖励"
}
```

### 注意事项


 1. 请确保在请求时携带有效的认证Token
 2. 用户必须是当前的牛王宝座持有者才能领取奖励
 3. 每个奖励周期内只能领取一次奖励

**接口状态**

> 开发中

**接口URL**

> api/bull-king/claim

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 领取MOOF持有者奖励

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-01 00:09:08

> 更新时间: 2025-03-03 17:44:22

## MOOF持有者奖励领取接口

### 接口信息


 - 接口路径: /api/bull-king/moof-holders/claim
 - 请求方式: POST
 - 需要认证: 是

**需要认证: 是**

### 接口描述

**该接口用于领取MOOF持有者奖励。用户需要持有MOOF代币才能领取相应的奖励。**

### 认证要求

**请求头中需要包含有效的认证token：**

```
Authorization: Bearer <token>
```

### 请求参数

**无需请求参数**

### 响应格式

#### 成功响应

```json
{
  "success": true,
  "message": "成功领取MOOF持有者奖励",
  "data": {
    // 奖励领取结果
  }
}
```

#### 错误响应

```json
{
  "success": false,
  "message": "领取MOOF持有者奖励失败",
  "error": "错误详细信息"
}
```

### 错误码说明

| 状态码 | 说明  |
| --- | --- |
| 400 | 请求参数错误 |
| 401 | 未授权或token无效 |
| 500 | 服务器内部错误 |

### 错误信息

**以下是可能遇到的错误信息：**


 - errors.walletNotFound: 钱包地址未找到
 - errors.claimMoofHoldersRewardFailed: 领取MOOF持有者奖励失败

**errors.claimMoofHoldersRewardFailed: 领取MOOF持有者奖励失败**

```
errors.claimMoofHoldersRewardFailed
```

### 注意事项


 1. 确保在请求时已登录并获取有效的认证token
 2. 用户需要持有MOOF代币才能成功领取奖励
 3. 建议在调用接口前先检查用户是否有资格领取奖励

**接口状态**

> 开发中

**接口URL**

> api/bull-king/moof-holders/claim

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取个人KOL排行榜

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-01 00:25:18

> 更新时间: 2025-03-03 17:47:56

## 个人KOL排行榜接口

### 接口概述

**获取个人KOL排行榜数据，包括排行榜信息、奖金池信息和用户个人信息。**


 - 接口路径: /api/bull-king/personal-kol
 - 请求方法: GET
 - 需要认证: 是

**需要认证: 是**

### 请求参数

#### 查询参数

| 参数名 | 类型  | 必填  | 默认值 | 描述  |
| --- | --- | --- | --- | --- |
| limit | Number | 否 | 100 | 限制返回的记录数量 |

### 响应格式

#### 成功响应

```json
{
    "ok": true,
    "data": {
        "leaderboard": [],
        "poolInfo": {
            "totalAmount": 0,
            "distributionAmount": 0
        },
        "userInfo": {
            "rank": 1,
            "reward": null,
            "gameVolume": 0
        }
    }
}
```

#### 错误响应

```json
{
  "success": false,
  "error": "错误信息",
  "details": "详细错误信息"
}
```

### 错误码说明

| HTTP状态码 | 说明  |
| ------- | --- |
| 400 | 参数验证失败 |
| 401 | 未授权访问 |
| 500 | 服务器内部错误 |

### 注意事项


 1. 接口需要在请求头中携带有效的认证信息
 2. limit参数必须为正整数
 3. 返回的排行榜数据按照排名顺序排序
 4. 如果用户未登录或认证失败，将返回401错误

**接口状态**

> 开发中

**接口URL**

> api/bull-king/personal-kol

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 领取个人KOL奖励

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-01 00:45:23

> 更新时间: 2025-03-03 17:51:59

## 领取个人KOL奖励接口

### 接口概述

**领取个人KOL奖励的接口，用户可以通过此接口领取其作为个人KOL获得的奖励。**


 - 接口路径: /api/bull-king/personal-kol/claim
 - 请求方法: POST
 - 需要认证: 是

**需要认证: 是**

### 认证方式

**请求需要包含有效的身份认证信息：**


 - 在请求头中必须包含有效的 Authorization Token
 - Token格式：Bearer <token>

**Token格式：Bearer <token>**

```
Bearer <token>
```

### 请求参数

**此接口不需要请求参数。**

### 响应格式

#### 成功响应

```json
{
  "success": true,
  "message": "领取个人KOL奖励成功",
  "data": {
    // 奖励领取结果信息
  }
}
```

#### 错误响应

```json
{
  "success": false,
  "message": "错误信息",
  "error": "详细错误说明"
}
```

### 错误码说明

| HTTP状态码 | 说明  |
| ------- | --- |
| 400 | 请求参数错误 |
| 401 | 未授权（Token无效或已过期） |
| 500 | 服务器内部错误 |

### 注意事项


 1. 请确保在请求时携带有效的认证Token
 2. 每个用户每周只能领取一次奖励

**接口状态**

> 开发中

**接口URL**

> api/bull-king/personal-kol/claim

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Accept-Language | en | string | 是 | - |

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Accept-Language | en | string | 是 | - |

**Query**

# 领取团队KOL奖励

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-01 01:20:48

> 更新时间: 2025-03-03 17:55:14

## 领取团队KOL奖励接口

### 接口概述

**领取团队KOL奖励的接口，用户可以通过此接口领取其作为团队KOL获得的奖励。**


 - 接口路径: /api/bull-king/team-kol/claim
 - 请求方法: POST
 - 需要认证: 是

**需要认证: 是**

### 认证要求

**请求头中必须包含有效的认证令牌：**

```
Authorization: Bearer <token>
```

### 请求参数

**此接口不需要请求参数。**

### 响应格式

#### 成功响应

```json
{
  "success": true,
  "message": "领取团队KOL奖励成功",
  "data": {
    // 奖励领取结果
  }
}
```

#### 错误响应

```json
{
  "success": false,
  "message": "错误信息",
  "error": "详细错误说明"
}
```

### 注意事项


 1. 确保在请求时已正确配置认证信息
 2. 每个用户每周只能领取一次奖励
 3. 领取前请确保有可领取的奖励

**接口状态**

> 开发中

**接口URL**

> api/bull-king/team-kol/claim

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取团队KOL排行榜

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-01 01:20:54

> 更新时间: 2025-03-03 18:01:05

## 团队KOL排行榜查询接口

### 接口概述

**获取团队KOL排行榜数据，包括排行榜信息、奖金池信息和用户个人信息。**


 - 接口路径: /api/bull-king/team-kol
 - 请求方法: GET
 - 需要认证: 是

**需要认证: 是**

### 请求参数

#### 查询参数

| 参数名 | 类型  | 必填  | 默认值 | 描述  |
| --- | --- | --- | --- | --- |
| limit | Number | 否 | 100 | 限制返回的记录数量 |

### 响应格式

#### 成功响应

```json
{
    "ok": true,
    "data": {
        "leaderboard": [],
        "poolInfo": {
            "totalAmount": 0,
            "distributionAmount": 0
        },
        "userInfo": {
            "rank": 1,
            "reward": null,
            "gameVolume": 0
        }
    }
}
```

#### 错误响应

```json
{
  "success": false,
  "error": "错误信息",
  "details": "详细错误信息"
}
```

### 错误码

| 状态码 | 说明  |
| --- | --- |
| 400 | 参数验证失败 |
| 401 | 未授权访问 |
| 500 | 服务器内部错误 |

### 注意事项


 1. 请确保在请求头中包含有效的认证token
 2. limit参数必须为正整数
 3. 返回的数据按照排名顺序排序
 4. 接口返回的数据包含用户在团队KOL排行榜中的具体排名信息

**接口状态**

> 开发中

**接口URL**

> api/bull-king/team-kol

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 查询游戏结果

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-01 08:24:39

> 更新时间: 2025-03-03 18:16:28

## 查询游戏结果接口

### 接口说明

**该接口用于查询指定场次和回合的房间详细信息，包括房间内的所有玩家信息和游戏状态。**

### 请求参数

| 参数名 | 类型  | 必填  | 说明  |
| --- | --- | --- | --- |
| sessionId | number | 是 | 场次ID |
| roundIndex | number | 是 | 回合索引 |

### 响应格式

```json
{
  "ok": true,
  "data": [
    {
      "roomId": 1,
      "sessionId": 4,
      "roundIndex": 1,
      "playerCount": 1,
      "winnerIndex": null,
      "seqno": null,
      "lotteryProcessed": true,
      "gameStatus": "refunded_insufficient_players",
      "players": [
        {
          "index": 0,
          "walletId": 1,
          "userId": 1,
          "walletAddress": "0QDioPTSTodOvpJe5czY963Jry4QilH3tMBzZn-1vFbhNmLO",
          "username": "Zoyonlian",
          "photoUrl": null,
          "status": "refunded",
          "isWinner": false,
          "gameStatus": "done",
          "payoutStatus": "refunded",
          "payout": 100,
          "isMoof": false
        }
      ]
    }
  ],
  "message": "Room details retrieved successfully"
}
```

#### 响应字段说明

##### 顶层字段

| 字段名 | 类型  | 说明  |
| --- | --- | --- |
| ok | boolean | 请求是否成功 |
| data | array | 房间信息数组 |
| message | string | 响应消息 |

##### 房间信息字段

| 字段名 | 类型  | 说明  |
| --- | --- | --- |
| roomId | number | 房间ID |
| sessionId | number | 场次ID |
| roundIndex | number | 回合索引 |
| playerCount | number | 房间内玩家数量 |
| winnerIndex | number\|null | 获胜者在玩家列表中的索引，null表示没有获胜者 |
| seqno | number\|null | 区块号 |
| lotteryProcessed | boolean | 抽奖是否已处理 |
| gameStatus | string | 房间游戏状态 |
| players | array | 玩家信息数组 |

##### 游戏状态(gameStatus)说明

| 状态值 | 说明  |
| --- | --- |
| pending | 游戏进行中 |
| done | 游戏已完成 |
| refunded_insufficient_players | 因玩家不足已退款 |

##### 玩家信息字段

| 字段名 | 类型  | 说明  |
| --- | --- | --- |
| index | number | 玩家在房间中的索引 |
| walletId | number | 钱包ID |
| userId | number | 用户ID |
| walletAddress | string | 钱包地址 |
| username | string | 用户名 |
| photoUrl | string\|null | 用户头像URL |
| status | string | 玩家状态 |
| isWinner | boolean | 是否为获胜者 |
| gameStatus | string | 玩家游戏状态 |
| payoutStatus | string | 支付状态 |
| payout | number | 支付金额 |

##### 玩家游戏状态(gameStatus)说明

| 状态值 | 说明  |
| --- | --- |
| pending | 游戏进行中 |
| done | 游戏已完成 |

##### 支付状态(payoutStatus)说明

| 状态值 | 说明  |
| --- | --- |
| pending | 等待支付 |
| done | 已支付 |
| refunded | 已退款 |

#### 错误响应

**当请求失败时，将返回以下格式：**

```json
{
  "ok": false,
  "message": "错误信息",
  "error": "详细错误信息"
}
```

**接口状态**

> 开发中

**接口URL**

> api/game/room-details?sessionId=4&roundIndex=1

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| sessionId | 4 | string | 是 | - |
| roundIndex | 1 | string | 是 | - |

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取个人KOL等级进度

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-01 11:23:29

> 更新时间: 2025-03-04 17:02:35

## 个人KOL等级进度查询接口

### 接口概述

**获取用户的个人KOL等级进度信息。**


 - 接口路径: /api/kol-progress/personal
 - 请求方法: GET
 - 需要认证: 是

**需要认证: 是**

### 认证要求

**该接口需要在请求头中包含有效的钱包认证token：**

```
Authorization: Bearer <your_token>
```

### 请求参数

**无需请求参数。**

### 响应格式

#### 成功响应

```json
{
  "success": true,
  "message": "获取个人KOL等级进度成功",
  "data": {
    // 具体的进度数据结构
  }
}
```

#### 错误响应

```json
{
  "success": false,
  "message": "获取个人KOL等级进度失败",
  "error": "错误详细信息"
}
```

### 错误码说明


 - 500: 服务器内部错误

```
500
```

### 使用示例

#### cURL

```bash
curl -X GET \
  'http://your-domain/api/kol-progress/personal' \
  -H 'Authorization: Bearer your_token'
```

### 注意事项


 1. 请确保在请求头中包含有效的认证token
 2. 接口返回的进度数据可能会根据用户的KOL等级有所不同
 3. 建议实现适当的错误处理机制

**接口状态**

> 开发中

**接口URL**

> api/kol-progress/personal

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Accept-Language | zh | string | 是 | - |

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Accept-Language | zh | string | 是 | - |

**Query**

# 获取团队KOL等级进度

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-01 11:27:06

> 更新时间: 2025-03-03 16:45:38

## 团队KOL等级进度查询接口

### 接口概述

**获取用户的团队KOL等级进度信息。**


 - 接口路径: /api/kol-progress/team
 - 请求方法: GET
 - 需要认证: 是

**需要认证: 是**

### 请求头

| 参数名 | 必填  | 说明  |
| --- | --- | --- |
| Authorization | 是 | 用户认证token |

### 请求参数

**无需请求参数**

### 响应格式

#### 成功响应

```json
{
  "success": true,
  "message": "获取团队KOL等级进度成功",
  "data": {
    // 团队KOL等级进度数据
  }
}
```

#### 错误响应

```json
{
  "success": false,
  "message": "获取团队KOL等级进度失败",
  "error": "错误详细信息"
}
```

### 注意事项


 1. 请确保在请求头中包含有效的认证token
 2. 接口返回的进度数据可能会根据用户的团队KOL等级有所不同
 3. 建议实现适当的错误处理机制

### 错误码说明

| 状态码 | 说明  |
| --- | --- |
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权或token无效 |
| 500 | 服务器内部错误 |

### 示例

#### 请求示例

```bash
curl -X GET \
  'http://api.example.com/api/kol-progress/team' \
  -H 'Authorization: Bearer your-token-here'
```

#### 响应示例

```json
{
  "success": true,
  "message": "获取团队KOL等级进度成功",
  "data": {
    "currentLevel": "silver",
    "teamGameAmount": 50000,
    "nextLevelRequirement": 100000,
    "progress": 50
  }
}
```

**接口状态**

> 开发中

**接口URL**

> api/kol-progress/team

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 今日和总推广tree-view

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-01 11:28:04

> 更新时间: 2025-03-11 15:26:17

## 每日推广进度接口

### 接口信息


 - 接口路径: /api/kol-progress/daily-promotion
 - 请求方法: GET
 - 认证要求: 需要钱包认证 (walletAuthMiddleware)

**认证要求: 需要钱包认证 (walletAuthMiddleware)**

### 接口描述

**获取用户的每日推广数据，包括今日游戏次数、投注金额以及总投注金额等信息。支持查看指定钱包ID的推广数据。**

### 请求参数

#### Query 参数

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| walletId | number | 否 | 指定要查询的钱包ID，不传则使用当前用户的钱包ID |

### 响应数据

#### 成功响应

```json
{
    "ok": true,
    "data": {
        "todayGameCount": 3, //当前登录账户今日游戏次数
        "totalGameCount": 3, //当前登录账户总有游戏次数
        "todayBetAmount": 300, //当地登录账户今日游戏量
        "totalBetAmount": 300,//当地登录账户总游戏量
                "reffStats": {
            "earnings": {
                "totalReferralEarnings": 0,//累计收益
                "estimatedTodayEarnings": 0 //预估今日奖励
            }
        },
        "referralUsers": [
            {
                "userId": 3,
                "username": "zoyonlian",
                "photoUrl": "",
                "registerTime": "2025-03-02 08:22:03",
                "walletId": 3,
                "walletAddress": "UQCD55QhtRuqNBYsxEMsh80ZIrvJ6GridII1gyBqI-NV4Rlr",
                "totalGameCount": 3,
                "totalBetAmount": 300,
                "todayGameCount": 3,
                "todayBetAmount": 300
            }
        ]
    },
    "message": "Daily promotion progress retrieved successfully"
}
```

### 注意事项


 1. 接口需要钱包认证，请确保在请求头中包含有效的认证信息
 2. 所有金额相关的数值都使用浮点数表示
 3. 如果查询的钱包ID不存在或无权限访问，将返回相应的错误信息

**接口状态**

> 开发中

**接口URL**

> api/kol-progress/daily-promotion

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| walletId | 2 | string | 是 | - |

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 个人KOL推广统计数据

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-01 16:16:57

> 更新时间: 2025-03-04 18:18:01

## 个人KOL统计数据查询接口

### 接口概述

**获取用户的个人KOL推广统计数据，包括推广业绩、奖励等信息。**


 - 接口路径: /api/kol/personal-stats
 - 请求方法: GET
 - 需要认证: 是

**需要认证: 是**

### 认证要求

**该接口需要在请求头中包含有效的身份认证信息：**

```
Authorization: Bearer <token>
```

### 请求参数

**无需请求参数**

### 响应格式

#### 成功响应

```json
{
  "ok": true,
  "data": {
    "directReferralCount": 0,
    "directGameCount": 0,
    "directBetAmount": 0,
    "directReferralBullCount":0,
    "todayDirectReferralCount": 0,
    "todayDirectGameCount": 0,
    "todayDirectBetAmount": 0,
    "kolLevel": "未达标",
    "kolProgress": 0,
    "nextKolLevel": "一星KOL",
    "remainingAmount": 20000
  },
  "message": "Personal KOL statistics retrieved successfully"
}
```

#### 响应字段说明

##### data 对象

| 字段名 | 类型  | 说明  | 示例值 |
| --- | --- | --- | --- |
| directReferralCount | number | 累计直推用户总数 | 10 |
| directGameCount | number | 累计直推用户游戏总局数 | 100 |
| directBetAmount | number | 累计直推用户投注总额 | 50000 |
| directReferralBullCount | number | 累计直推金牛次数 | 10 |
| todayDirectReferralCount | number | 今日直推用户数 | 2 |
| todayDirectGameCount | number | 今日直推用户游戏局数 | 20 |
| todayDirectBetAmount | number | 今日直推用户投注额 | 5000 |
| kolLevel | string | 当前KOL等级 | "未达标", "一星KOL", "二星KOL", "三星KOL" |
| kolProgress | number | 当前等级进度(百分比) | 75 |
| nextKolLevel | string | 下一个KOL等级 | "一星KOL", "二星KOL", "三星KOL" |
| remainingAmount | number | 距离下一等级所需投注额 | 20000 |

#### 错误响应

```json
{
  "ok": false,
  "message": "错误信息"
}
```

### 错误码

| 状态码 | 说明  |
| --- | --- |
| 400 | 请求参数错误 |
| 401 | 未授权或Token无效 |
| 500 | 服务器内部错误 |

### 重要说明


 1. KOL等级说明：
 2. 
 3. 未达标：直推用户投注额 < 20,000
 4. 一星KOL：直推用户投注额 ≥ 20,000
 5. 二星KOL：直推用户投注额 ≥ 50,000
 6. 三星KOL：直推用户投注额 ≥ 100,000
 7. 
 8. 
 9. 所有金额相关的数值单位均为USDT
 10. 
 11. 数据统计说明：
 12. 
 13. 累计数据统计从用户注册开始
 14. 今日数据统计从每日00:00:00开始
 15. 数据更新可能存在几分钟的延迟

**接口状态**

> 开发中

**接口URL**

> api/kol/personal-stats

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 团队KOL推广统计数据

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-01 16:22:19

> 更新时间: 2025-03-04 18:16:54

## 团队KOL统计数据查询接口

### 接口概述

**获取用户的团队KOL推广统计数据，包括团队成员数量、投注金额、KOL等级等信息。**


 - 接口路径: /api/kol/team-stats
 - 请求方法: GET
 - 需要认证: 是

**需要认证: 是**

### 请求参数

**无需请求参数**

### 响应格式

#### 成功响应

```json
{
  "ok": true,
  "data": {
    "teamMemberCount": 0,           // 团队总成员数
    "teamBetAmount": 0,            // 团队总投注金额
    "teamBullCount":0,             //团队金牛次数
    "todayTeamMemberCount": 0,     // 今日团队活跃成员数
    "todayTeamBetAmount": 0,       // 今日团队投注金额
    "weekTeamMemberCount": 0,      // 本周团队活跃成员数
    "weekTeamBetAmount": 0,        // 本周团队投注金额
    "teamLevelDistribution": [],    // 团队等级分布
    "kolLevel": "未达标",          // 当前KOL等级
    "kolProgress": 0,              // KOL等级进度
    "nextKolLevel": "银牌KOL",     // 下一个KOL等级
    "remainingAmount": 100000      // 达到下一等级所需剩余金额
  },
  "message": "Team KOL statistics retrieved successfully"
}
```

#### 错误响应

```json
{
  "ok": false,
  "error": "错误信息"
}
```

### 错误码说明

| 状态码 | 说明  |
| --- | --- |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 500 | 服务器内部错误 |

### 注意事项


 1. 接口需要通过钱包认证中间件验证，请确保在请求头中包含有效的认证信息
 2. 所有金额相关的数值单位均为最小单位
 3. KOL等级包括：未达标、银牌KOL、金牌KOL

**接口状态**

> 开发中

**接口URL**

> api/kol/team-stats

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取每日推荐返利详情

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-01 22:00:17

> 更新时间: 2025-03-05 21:49:35

## 每日推广返利历史接口

### 接口说明

**获取用户每日推广返利历史详情，包括每天是否已领取返利、领取金额、待领取返利以及下线推广统计信息。**

### 请求信息

#### 请求地址


 - URL: /api/rebate/daily-details
 - 方法: GET
 - 认证: 需要钱包认证 (walletAuthMiddleware)

**认证: 需要钱包认证 (walletAuthMiddleware)**

#### 请求参数

##### 查询参数 (Query Parameters)

| 参数名 | 类型  | 必填  | 默认值 | 描述  |
| --- | --- | --- | --- | --- |
| page | string | 否 | 1 | 页 |
| limit | string | 否 | 10 | 条数 |

### 响应数据

#### 成功响应

```json
{
    "ok": true,
    "data": {
        "dailyRecords": [
            {
                "date": "2025-03-05",
                "totalAmount": 0.3600000000000001,
                "isPending": true,
                "canClaim": true
            }
        ],
        "pagination": {
            "total": 1,
            "currentPage": 7,
            "pageSize": 10,
            "totalPages": 0
        }
    },
    "message": "Daily rebate details retrieved successfully"
}
```

### 注意事项


 1. 接口需要用户完成身份认证，请在请求头中包含有效的认证token
 2. 用户必须拥有有效的钱包地址
 3. 返利领取需满足每日游戏轮数要求（通常为3轮）
 4. 返回的数据按时间倒序排列

**接口状态**

> 开发中

**接口URL**

> api/rebate/daily-details?page=1&limit=100

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| page | 1 | string | 是 | - |
| limit | 100 | string | 是 | - |

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 推荐宝箱要求列表

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-01 22:35:06

> 更新时间: 2025-04-06 14:12:00

## 推荐状态 API

#### 获取用户推荐状态

**接口路径：GET /api/referral/status**

```
GET /api/referral/status
```

**接口描述：获取用户的推荐状态信息，包括当前推荐人数、各等级状态和今日是否已领取奖励。,请求方法：GET,认证要求：需要钱包认证（walletAuthMiddleware）,请求参数：无需请求参数,响应结果：**

```json
{
    "ok": true,
    "data": {
        "currentReferrals": 6,
        "status": 1,
        "levelsStatus": [
            {
                "requiredReferrals": 5,
                "chestsReward": 1,
                "isAvailable": true,
                "progress": 1,
                "name": "每日领取1个神秘宝箱",
                "status": 1
            },
            {
                "requiredReferrals": 10,
                "chestsReward": 2,
                "isAvailable": false,
                "progress": 0.6,
                "name": "每日领取2个神秘宝箱",
                "status": 0
            },
            {
                "requiredReferrals": 30,
                "chestsReward": 5,
                "isAvailable": false,
                "progress": 0.2,
                "name": "每日领取5个神秘宝箱",
                "status": 0
            },
            {
                "requiredReferrals": 100,
                "chestsReward": 15,
                "isAvailable": false,
                "progress": 0.06,
                "name": "每日领取15个神秘宝箱",
                "status": 0
            }
        ]
    },
    "message": "获取推荐状态成功"
}
```

**响应字段说明：**

| 字段名 | 类型  | 描述  |
| --- | --- | --- |
| currentReferrals | Number | 当前推荐人数 |
| status | int | 整体状态，0: "未达成条件"、 2:"可领取"、1: "已领取" |
| levelsStatus | Array | 推荐等级状态数组 |
| levelsStatus[].requiredReferrals | Number | 该等级需要的推荐人数 |
| levelsStatus[].chestsReward | Number | 该等级对应的宝箱奖励数量 |
| levelsStatus[].isAvailable | Boolean | 是否已达到该等级 |
| levelsStatus[].progress | Number | 该等级的完成进度(0-1) |
| levelsStatus[].name | String | 该等级的名称 |
| levelsStatus[].status | String | 该等级的状态，可能值："未达成条件"、"可领取"、"已领取" |

**错误码：**

| 状态码 | 错误信息 | 描述  |
| --- | ---- | --- |
| 401 | 未授权 | 当用户未登录或token无效时返回 |
| 400 | 用户不存在 | 当用户ID无效时返回 |

**业务逻辑说明：**


 1. 统计数据基于用户的推荐关系
 2. 今日领取状态基于UserDailyClaim表记录
 3. 推荐等级分为4档：5人、10人、30人、100人
 4. 每个等级的status字段表示当前状态：
 5. 未达成条件：当前推荐人数小于该等级要求
 6. 可领取：已达到该等级要求且今日未领取
 7. 已领取：已达到该等级要求且今日已领取

**已领取：已达到该等级要求且今日已领取**

**接口状态**

> 开发中

**接口URL**

> api/referral/status

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Accept-Language | zh | string | 是 | - |

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
	"ok": true,
	"data": {
		"currentReferrals": 6,
		"status": 1,
		"levelsStatus": [
			{
				"requiredReferrals": 5,
				"chestsReward": 1,
				"isAvailable": true,
				"progress": 1,
				"name": "每日领取1个神秘宝箱",
				"status": 1
			},
			{
				"requiredReferrals": 10,
				"chestsReward": 2,
				"isAvailable": false,
				"progress": 0.6,
				"name": "每日领取2个神秘宝箱",
				"status": 0
			},
			{
				"requiredReferrals": 30,
				"chestsReward": 5,
				"isAvailable": false,
				"progress": 0.2,
				"name": "每日领取5个神秘宝箱",
				"status": 0
			},
			{
				"requiredReferrals": 100,
				"chestsReward": 15,
				"isAvailable": false,
				"progress": 0.06,
				"name": "每日领取15个神秘宝箱",
				"status": 0
			}
		]
	},
	"message": "获取推荐状态成功"
}
```

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Accept-Language | zh | string | 是 | - |

**Query**

# 获取用户全球个人KOL历史记录

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-01 22:58:39

> 更新时间: 2025-03-03 22:26:36

## 获取用户全球个人KOL历史记录

### 接口说明

**获取用户的个人KOL历史记录，包括所有个人KOL相关的奖励记录。支持分页查询，结果按时间倒序排列。**

### 接口信息


 - 接口路径: /api/kol/personal-history
 - 请求方式: GET
 - 认证要求: 需要钱包认证（walletAuthMiddleware）

**认证要求: 需要钱包认证（walletAuthMiddleware）**

### 请求参数

#### 查询参数（Query Parameters）

| 参数名 | 类型  | 必填  | 说明  | 示例  |
| --- | --- | --- | --- | --- |
| page | string | 否 | 页码，默认为1 | "1" |
| limit | string | 否 | 每页记录数，默认为20 | "20" |

### 响应结果

#### 成功响应

```json
{
  "ok": true,
  "message": "获取个人KOL历史记录成功",
  "data": {
    "records": [
      {
        "id": 1,
        "userId": "123",
        "walletId": "wallet123",
        "subPool": "personal_kol_silver",
        "amount": 100,
        "claimed": false,
        "createdAt": "2024-01-20T08:00:00Z",
        "updatedAt": "2024-01-20T08:00:00Z"
      }
    ]
  },
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 20,
    "totalPages": 5
  }
}
```

#### 响应字段说明

##### data.records 字段

| 字段名 | 类型  | 说明  |
| --- | --- | --- |
| id | number | 记录ID |
| userId | string | 用户ID |
| walletId | string | 钱包ID |
| subPool | string | KOL奖励子池类型 |
| amount | number | 奖励金额 |
| claimed | boolean | 是否已领取 |
| createdAt | string | 创建时间 |
| updatedAt | string | 更新时间 |

##### pagination 字段

| 字段名 | 类型  | 说明  |
| --- | --- | --- |
| total | number | 总记录数 |
| page | number | 当前页码 |
| limit | number | 每页记录数 |
| totalPages | number | 总页数 |

#### 错误响应

```json
{
  "success": false,
  "message": "错误信息",
  "errors": ["具体错误描述"]
}
```

### 可能的错误情况

| 错误情况 | HTTP状态码 | 说明  |
| ---- | ------- | --- |
| 参数验证失败 | 400 | 分页参数格式不正确 |
| 缺少用户或钱包ID | 400 | 用户未正确认证 |
| 分页参数无效 | 400 | page或limit参数值无效 |
| 服务器错误 | 400 | 其他服务器处理错误 |

### 示例

#### 请求示例

```
GET /api/kol/personal-history?page=1&limit=20
Authorization: Bearer <token>
```

#### 注意事项


 1. 分页参数必须是正整数
 2. 需要正确设置Authorization请求头
 3. 响应结果按创建时间倒序排列
 4. 默认每页显示20条记录

**接口状态**

> 开发中

**接口URL**

> api/kol/personal-history

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取用户全球团队KOL历史记录

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-01 23:00:58

> 更新时间: 2025-03-03 22:30:38

## 获取用户全球团队KOL历史记录

### 接口信息


 - 接口路径: /api/kol/team-history
 - 请求方式: GET
 - 认证要求: 需要钱包认证（walletAuthMiddleware）

**认证要求: 需要钱包认证（walletAuthMiddleware）**

### 请求参数

#### 查询参数（Query Parameters）

| 参数名 | 类型  | 必填  | 默认值 | 说明  |
| --- | --- | --- | --- | --- |
| page | string | 否 | "1" | 页码，必须为正整数 |
| limit | string | 否 | "20" | 每页记录数，必须为正整数 |

### 响应结果

#### 成功响应

```json
{
  "success": true,
  "message": "获取团队KOL历史记录成功",
  "data": {
    "records": [
      {
        "id": "number",
        "userId": "string",
        "walletId": "string",
        "subPool": "string",
        "amount": "number",
        "claimed": "boolean",
        "claimTime": "string",
        "createdAt": "string",
        "updatedAt": "string"
      }
    ]
  },
  "pagination": {
    "total": "number",
    "page": "number",
    "limit": "number",
    "totalPages": "number"
  }
}
```

#### 响应字段说明

##### data.records 字段

| 字段名 | 类型  | 说明  |
| --- | --- | --- |
| id | number | 记录ID |
| userId | string | 用户ID |
| walletId | string | 钱包ID |
| subPool | string | 子池名称，以 'team_kol_' 开头 |
| amount | number | 奖励金额 |
| claimed | boolean | 是否已领取 |
| claimTime | string | 领取时间 |
| createdAt | string | 创建时间 |
| updatedAt | string | 更新时间 |

##### pagination 字段

| 字段名 | 类型  | 说明  |
| --- | --- | --- |
| total | number | 总记录数 |
| page | number | 当前页码 |
| limit | number | 每页记录数 |
| totalPages | number | 总页数 |

#### 错误响应

```json
{
  "success": false,
  "message": "错误信息",
  "errors": ["详细错误说明"]
}
```

### 可能的错误情况

| 错误情况 | HTTP状态码 | 说明  |
| ---- | ------- | --- |
| 参数验证失败 | 400 | 分页参数格式不正确 |
| 缺少用户ID或钱包ID | 400 | 用户未正确认证 |
| 分页参数无效 | 400 | page或limit参数小于等于0 |
| 服务器内部错误 | 400 | 其他服务器处理错误 |

### 示例

#### 请求示例

```
GET /api/kol/team-history?page=1&limit=20
Authorization: Bearer <token>
```

#### 成功响应示例

```json
{
  "success": true,
  "message": "获取团队KOL历史记录成功",
  "data": {
    "records": [
      {
        "id": 1,
        "userId": "user123",
        "walletId": "wallet123",
        "subPool": "team_kol_gold",
        "amount": 100.50,
        "claimed": true,
        "claimTime": "2024-01-20T08:30:00Z",
        "createdAt": "2024-01-20T08:00:00Z",
        "updatedAt": "2024-01-20T08:30:00Z"
      }
    ]
  },
  "pagination": {
    "total": 50,
    "page": 1,
    "limit": 20,
    "totalPages": 3
  }
}
```

#### 错误响应示例

```json
{
  "success": false,
  "message": "参数验证失败",
  "errors": ["页码必须为正整数"]
}
```

### 注意事项


 1. 接口需要进行钱包认证，请确保在请求头中包含有效的认证信息
 2. 响应结果按创建时间降序排列（最新记录在前）

**接口状态**

> 开发中

**接口URL**

> api/kol/team-history

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取用户的 MOOF 解锁历史记录列表

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-01 23:16:34

> 更新时间: 2025-03-03 22:33:37

## MOOF 解锁历史记录

**获取用户的 MOOF 解锁历史记录列表，支持分页查询。**

### 接口信息


 - 接口路径: /api/bull-unlock/history
 - 请求方式: GET
 - 认证要求: 需要钱包认证（Bearer Token）

**认证要求: 需要钱包认证（Bearer Token）**

### 请求参数

#### 查询参数（Query Parameters）

| 参数名 | 类型  | 必填  | 说明  | 示例  |
| --- | --- | --- | --- | --- |
| page | string | 否 | 页码，默认为 1 | "1" |
| limit | string | 否 | 每页记录数，默认为 20 | "20" |

#### 请求头（Headers）

| 参数名 | 必填  | 说明  |
| --- | --- | --- |
| Authorization | 是 | Bearer Token，用于用户认证 |

### 响应数据

#### 成功响应

```json
{
    "ok": true,
    "data": {
        "records": [],
        "pagination": {
            "total": 0,
            "page": 1,
            "limit": 20,
            "totalPages": 0
        }
    },
    "message": "MOOF unlock history retrieved successfully"
}
```

#### 错误响应

```json
{
  "success": false,
  "message": "错误信息",
  "errors": ["详细错误说明"]
}
```

### 错误码说明

| 状态码 | 说明  |
| --- | --- |
| 400 | 参数验证失败 |
| 400 | 分页参数无效 |
| 401 | 未授权或 Token 无效 |

### 注意事项


 1. 分页参数 page 和 limit 必须是正整数
 2. 需要在请求头中携带有效的认证 Token
 3. 返回的数据按时间倒序排列（最新记录在前）

**需要在请求头中携带有效的认证 Token**

**返回的数据按时间倒序排列（最新记录在前）**

**接口状态**

> 开发中

**接口URL**

> api/bull-unlock/history

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 发送绑定邮箱验证码的路由

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-01 23:54:58

> 更新时间: 2025-03-03 22:37:30

## 发送邮箱验证码

### 接口说明

**发送验证码到指定邮箱，用于邮箱绑定验证。验证码有效期为5分钟。**

### 请求信息

#### 请求地址

```
POST /api/user/send-email-code
```

#### 请求头

| 参数名 | 参数值 | 是否必须 | 示例  | 备注  |
| --- | --- | ---- | --- | --- |
| Authorization | Bearer | 是 | Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... | JWT认证token |
| Content-Type | application/json | 是 | application/json | 请求体格式 |

#### 请求参数

##### 请求体

```json
{
  "email": "<EMAIL>"
}
```

| 参数名 | 类型  | 是否必须 | 说明  | 示例  |
| --- | --- | ---- | --- | --- |
| email | string | 是 | 需要验证的邮箱地址 | <EMAIL> |

### 响应信息

#### 成功响应

```json
{
    "ok": true,
    "data": null,
    "message": "Email verification code sent successfully"
}
```

### 注意事项


 1. 验证码有效期为5分钟
 2. 同一用户对同一邮箱的验证码请求，新的验证码会覆盖旧的验证码
 3. 验证码为6位数字

**接口状态**

> 开发中

**接口URL**

> api/user/send-email-code

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "email":"<EMAIL>"
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 绑定邮箱

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-02 00:08:22

> 更新时间: 2025-03-03 22:42:35

## 绑定邮箱接口

### 接口说明

**绑定用户邮箱的接口，需要先获取邮箱验证码，然后使用验证码进行邮箱绑定。**

### 请求信息


 - 路径: /api/user/bind-email
 - 方法: POST
 - 需要认证: 是（需要在请求头中包含有效的 JWT Token）

```
POST
```

**需要认证: 是（需要在请求头中包含有效的 JWT Token）**

### 请求参数

#### 请求体 (JSON)

| 参数名 | 类型  | 必填  | 说明  |
| --- | --- | --- | --- |
| email | string | 是 | 需要绑定的邮箱地址，必须是有效的邮箱格式 |
| verificationCode | string | 是 | 6位数字验证码，通过 /api/user/send-email-code 接口获取 |

```
/api/user/send-email-code
```

#### 请求示例

```json
{
  "email": "<EMAIL>",
  "verificationCode": "123456"
}
```

### 响应信息

#### 成功响应

```json
{
    "ok": true,
    "data": {
        "success": true,
        "message": "邮箱绑定成功",
        "user": {
            "id": 1,
            "email": "<EMAIL>"
        }
    },
    "message": "Email bound successfully"
}
```

### 注意事项


 1. 验证码有效期为5分钟
 2. 验证码使用后会立即失效
 3. 同一个邮箱可以被多个用户绑定
 4. 绑定成功后，原有的验证码会被清除

### 相关接口

**发送绑定邮箱验证码的路由**

**接口状态**

> 开发中

**接口URL**

> api/user/bind-email

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "email":"<EMAIL>",
    "verificationCode":"631895"
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 发送转账验证码

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-02 00:34:17

> 更新时间: 2025-03-03 22:50:33

## 发送转账验证码

### 接口说明

**该接口用于在用户进行USD转账操作时，向用户的绑定邮箱发送验证码。**

### 请求信息


 - 请求路径：/api/user/send-transfer-code
 - 请求方法：POST
 - 需要认证：是（需要在请求头中包含有效的JWT Token）

```
POST
```

**需要认证：是（需要在请求头中包含有效的JWT Token）**

### 请求参数

#### Headers

| 参数名 | 必选  | 类型  | 说明  |
| --- | --- | --- | --- |
| Authorization | 是 | string | Bearer Token，格式：Bearer <token> |

```
Bearer <token>
```

#### Body

**请求体格式：application/json**

```
application/json
```

| 参数名 | 必选  | 类型  | 说明  |
| --- | --- | --- | --- |
| toWalletAddress | 是 | string | 接收方钱包地址 |
| amount | 是 | number | 转账金额，最小值：0.000001 |

### 响应信息

#### 成功响应

```json
{
  "code": 0,
  "message": "验证码已发送",
  "data": null
}
```

### 业务规则


 1. 验证码有效期为5分钟
 2. 验证码为6位随机数字
 3. 转账金额必须大于0.000001
 4. 用户必须已绑定邮箱才能使用此功能
 5. 用户钱包余额必须大于等于转账金额

#### 成功响应示例

```json
{
    "ok": true,
    "data": null,
    "message": "Transfer verification code sent successfully"
}
```

### 注意事项


 1. 请确保请求头中包含正确的Authorization Token
 2. 验证码发送后，需要在5分钟内完成转账操作
 3. 同一用户的同一笔转账验证码会覆盖之前的验证码
 4. 请确保接收方钱包地址格式正确
 5. 建议在调用此接口前先检查用户是否已绑定邮箱

**接口状态**

> 开发中

**接口URL**

> api/user/send-transfer-code

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "toWalletAddress":"UQBhQxkk_fkznrqzwOAwrNJlHdp1el2f-AJoa1UBMUz-ERGb",
    "amount":1
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 转账usd

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-02 00:36:13

> 更新时间: 2025-03-03 22:54:14

## USD 转账接口

### 接口说明

**该接口用于执行 USD 转账操作，需要先获取转账验证码，验证通过后才能执行转账。**

### 请求信息


 - 请求路径：/api/user/transfer-usd
 - 请求方法：POST
 - 需要认证：是（需要在请求头中包含有效的JWT Token）

```
POST
```

**需要认证：是（需要在请求头中包含有效的JWT Token）**

### 请求参数

#### Body

**请求体格式：application/json**

```
application/json
```

| 参数名 | 必选  | 类型  | 说明  |
| --- | --- | --- | --- |
| verificationCode | 是 | string | 转账验证码，6位数字 |

### 响应数据

#### 成功响应

```json
{
    "ok": true,
    "data": {
        "success": true,
        "message": "转账成功",
        "data": {
            "fromWalletId": 1,
            "toWalletAddress": "UQBhQxkk_fkznrqzwOAwrNJlHdp1el2f-AJoa1UBMUz-ERGb",
            "amount": 1,
            "timestamp": "2025-03-03T14:53:28.331Z"
        }
    },
    "message": "Transfer completed successfully"
}
```

### 注意事项


 1. 转账前需要先调用 /api/user/send-transfer-code 接口获取验证码
 2. 验证码有效期为5分钟

**验证码有效期为5分钟**

**接口状态**

> 开发中

**接口URL**

> api/user/transfer-usd

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "verificationCode":"348663"
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取提现设置

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-02 08:14:58

> 更新时间: 2025-03-03 22:59:55

## 获取提现设置

### 接口描述

**获取系统当前的提现相关设置，包括最小提现金额、手续费、每日提现次数限制等配置信息。**

### 请求方法

```
GET /api/withdrawal/settings
```

### 请求参数

**无**

### 响应参数

#### 成功响应

```json
{
  "ok": true,
  "data": {
    "usd": {
      "minAmount": 10,           // USD最小提现金额
      "fee": 1,                 // USD提现手续费（固定金额）
      "dailyLimit": 3,          // USD每日提现次数限制
      "manualApproveThreshold": 1000  // USD提现需要人工审核的阈值
    },
    "moof": {
      "minAmount": 21,          // MOOF最小提现数量
      "fee": 11,                // MOOF提现手续费（固定数量）
      "dailyLimit": 3           // MOOF每日提现次数限制
    }
  },
  "message": "Withdrawal settings retrieved successfully"
}
```

#### 错误响应

```json
{
  "ok": false,
  "message": "获取提现设置失败",
  "error": "具体错误信息"
}
```

### 注意事项


 1. 该接口不需要身份验证
 2. USD和MOOF的手续费均为固定金额/数量，而不是百分比
 3. 所有金额相关的数值都是数字类型
 4. 当USD提现金额大于等于manualApproveThreshold时，需要人工审核
 5. 每个币种都有独立的每日提现次数限制

**接口状态**

> 开发中

**接口URL**

> api/withdrawal/settings

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> 无需认证

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 提现USD

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-02 08:17:21

> 更新时间: 2025-03-03 23:06:20

## USD提现

### 接口描述

**提现指定金额的USD到用户的钱包地址。该接口会验证提现金额、用户余额、每日提现次数等条件，并根据提现金额决定是否需要人工审核。**

### 请求方法

```
POST /api/withdrawal/usd
```

### 认证要求

**需要钱包认证（walletAuthMiddleware）**

### 请求参数

#### 请求体

```json
{
  "amount": 10  // 提现金额，必须大于10
}
```

### 响应参数

#### 成功响应

```json
{
  "ok": true,
  "data": {
    "success": true,
    "amount": 10,           // 提现金额
    "fee": 1,              // 手续费
    "status": "approved",   // 状态：approved-已批准，pending-待审核
    "needManualApprove": false  // 是否需要人工审核
  },
  "message": "USD withdrawn successfully"
}
```

### 注意事项


 1. 提现金额必须大于系统设定的最小提现金额
 2. 实际扣除金额 = 提现金额 + 手续费
 3. 每个用户每日有提现次数限制
 4. 当提现金额大于等于系统设定的阈值时，需要人工审核
 5. 提现地址默认使用用户钱包地址
 6. 所有金额相关的数值都是数字类型

**接口状态**

> 开发中

**接口URL**

> api/withdrawal/usd

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "amount":10
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 提现MOOF

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-02 08:35:22

> 更新时间: 2025-03-03 23:12:55

## MOOF提现接口

### 接口信息


 - 接口路径: /api/withdrawal/moof
 - 请求方式: POST
 - 接口描述: 用户提现MOOF代币

**接口描述: 用户提现MOOF代币**

### 认证要求

**需要在请求头中携带有效的认证令牌：**

```
Authorization: Bearer <token>
```

### 请求参数

#### 请求体参数

| 参数名 | 类型  | 必填  | 说明  | 示例值 |
| --- | --- | --- | --- | --- |
| amount | number | 是 | 提现金额，最小值为0.000001 | 100 |

#### 请求示例

```json
{
  "amount": 100
}
```

### 响应数据

#### 响应参数说明

| 参数名 | 类型  | 说明  | 示例值 |
| --- | --- | --- | --- |
| ok | boolean | 请求是否成功 | true |
| data.success | boolean | 提现操作是否成功 | true |
| data.amount | number | 提现金额 | 100 |
| data.fee | number | 手续费 | 11 |
| data.status | string | 提现状态 | "approved" |
| message | string | 响应消息 | "MOOF withdrawn successfully" |

#### 成功响应示例

```json
{
  "ok": true,
  "data": {
    "success": true,
    "amount": 100,
    "fee": 10,
    "status": "approved"
  },
  "message": "MOOF withdrawn successfully"
}
```

### 注意事项


 1. 提现金额必须大于等于21 MOOF
 2. 用户必须已绑定钱包地址
 3. 提现将收取一定比例的手续费

**接口状态**

> 开发中

**接口URL**

> api/withdrawal/moof

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "amount":21
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 用户登录

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-03 11:37:03

> 更新时间: 2025-03-11 15:26:22

## TON钱包登录接口文档

### 接口说明

**该接口用于验证TON钱包并进行用户登录，仅验证钱包而不会注册新用户。**

### 接口信息


 - 接口路径: /api/ton-proof/login
 - 请求方法: POST
 - 速率限制: 15分钟内最多100次请求

**速率限制: 15分钟内最多100次请求**

### 请求参数

```json
{
  "proof": {
    "payload": "string",      // payload 接口返回的数据
    "timestamp": "number",    // 时间戳
    "state_init": "string",  // Base64格式的state_init
    "signature": "string",   // Base64格式的签名
    "domain": {
      "lengthBytes": "number", // domain值的字节长度
      "value": "string"       // domain值
    }
  },
  "address": "string",      // TON钱包地址
  "network": "string",      // 网络类型 (MAINNET 或 TESTNET)
  "public_key": "string"   // 16进制格式的公钥
}
```

### 参数验证

**接口会对以下内容进行验证：**


 1. 请求参数格式和必填项
 2. Payload的有效性和签名
 3. Domain长度
 4. 钱包地址是否已注册

### 响应格式

#### 成功响应

```json
{
  "ok": true,
  "data": {
    "address": "string",           // 原始钱包地址
    "userFriendlyAddress": "string", // 用户友好格式的钱包地址
    "network": "string"            // 网络类型
  },
  "token": "string"               // JWT令牌
}
```

#### 错误响应

```json
{
  "ok": false,
  "message": "string",    // 错误信息
  "error": "any"         // 详细错误信息（可选）
}
```

### 错误码说明


 - 400: 参数验证失败
 - 404: 钱包未注册或用户不存在
 - 429: 请求频率超限

### 注意事项


 1. JWT令牌有效期为60天
 2. 请求头中可以通过accept-language指定响应的语言
 3. 钱包地址必须已经通过/check-proof接口注册
 4. 所有时间戳使用Unix时间戳（秒）

```
/check-proof
```

**所有时间戳使用Unix时间戳（秒）**

### 示例

#### 请求示例

```json
{
  "proof": {
    "payload": "7b226164647265737373223a22...",
    "timestamp": 1677649421,
    "state_init": "te6ccgECFAEAA...",
    "signature": "AQABAAAA...",
    "domain": {
      "lengthBytes": 17,
      "value": "ton-connect.github.io"
    }
  },
  "address": "0:1234567...",
  "network": "MAINNET",
  "public_key": "1234567..."
}
```

#### 成功响应示例

```json
{
  "ok": true,
  "data": {
    "address": "0:1234567...",
    "userFriendlyAddress": "EQA1234567...",
    "network": "MAINNET"
  },
  "token": "eyJhbGciOiJIUzI1..."
}
```

#### 错误响应示例

```json
{
  "ok": false,
  "message": "Wallet not registered"
}
```

**接口状态**

> 开发中

**接口URL**

> api/ton-proof/login

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Accept-Language | zh | string | 是 | - |

**请求Body参数**

```javascript
{
  "proof": {
    "payload": "f1420caff04255200000000067c67f69e2e060255bb1c4cf432323fe7cc8cbd5",
    "timestamp": 1741685774,
    "state_init": "te6cckECFgEAArEAAgE0AgEAUYAAAAA///+IlNB8fZp2k9j3HnrkxHSRpVuX+lBKZnXMcTdJZkD5zCGgART/APSkE/S88sgLAwIBIAYEAQLyBQEeINcLH4IQc2lnbrry4Ip/EQIBSBAHAgEgCQgAGb5fD2omhAgKDrkPoCwCASANCgIBSAwLABGyYvtRNDXCgCAAF7Ml+1E0HHXIdcLH4AIBbg8OABmvHfaiaEAQ65DrhY/AABmtznaiaEAg65Drhf/AAtzQINdJwSCRW49jINcLHyCCEGV4dG69IYIQc2ludL2wkl8D4IIQZXh0brqOtIAg1yEB0HTXIfpAMPpE+Cj6RDBYvZFb4O1E0IEBQdch9AWDB/QOb6ExkTDhgEDXIXB/2zzgMSDXSYECgLmRMOBw4hIRAeaO8O2i7fshgwjXIgKDCNcjIIAg1yHTH9Mf0x/tRNDSANMfINMf0//XCgAK+QFAzPkQmiiUXwrbMeHywIffArNQB7Dy0IRRJbry4IVQNrry4Ib4I7vy0IgikvgA3gGkf8jKAMsfAc8Wye1UIJL4D95w2zzYEgP27aLt+wL0BCFukmwhjkwCIdc5MHCUIccAs44tAdcoIHYeQ2wg10nACPLgkyDXSsAC8uCTINcdBscSwgBSMLDy0InXTNc5MAGk6GwShAe78uCT10rAAPLgk+1V4tIAAcAAkVvg69csCBQgkXCWAdcsCBwS4lIQseMPINdKFRQTABCTW9sx4ddM0AByMNcsCCSOLSHy4JLSAO1E0NIAURO68tCPVFAwkTGcAYEBQNch1woA8uCO4sjKAFjPFsntVJPywI3iAJYB+kAB+kT4KPpEMFi68uCR7UTQgQFB1xj0BQSdf8jKAEAEgwf0U/Lgi44UA4MH9Fvy4Iwi1woAIW4Bs7Dy0JDiyFADzxYS9ADJ7VQSB1mW",
    "signature": "6pADU6jgieg0QuRncYOf5OhZzrlRV9nE9iZQjoXma/AXiOiOPAIvHfIPOTAfdM/0a7IMSxLojtpJRuN83vp7DA==",
    "domain": {
      "lengthBytes": 17,
      "value": "cmsolution.online"
    }
  },
  "address": "0:a64727edf756c644be28b4134ca9afd02130499b44e430f7a5ef5545ab936e80",
  "network": "-239",
  "public_key": "29a0f8fb34ed27b1ee3cf5c988e9234ab72ff4a094cceb98e26e92cc81f39843"
}
```

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Accept-Language | zh | string | 是 | - |

**Query**

# 我的宝箱奖励

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-04 15:58:24

> 更新时间: 2025-03-04 16:45:46

## 获取用户的宝箱奖励信息

### 接口说明

**获取用户的宝箱奖励相关信息，包括可用宝箱数量、已开启宝箱数量、总宝箱数量、奖励汇总以及最近开启的宝箱详情。**

### 请求URL

```
GET /api/rewards/chest-rewards
```

### 请求方式


 - GET

### 认证要求

**需要在请求头中包含有效的JWT Token，用于验证用户身份。**

### 请求参数

**无**

### 响应参数

#### 成功响应

```json
{
  "ok": true,
  "data": {
    "availableChests": 2,        // 可用（未开启）的宝箱数量
    "openedChests": 3,          // 已开启的宝箱数量
    "totalChests": 5,          // 总宝箱数量
    "rewardSummary": {          // 奖励汇总
      "ticket": 0,           // 累计获得的ticket数量
      "ton": 0.7,              // 累计获得的ton数量
      "gem": 64              // 累计获得的gem数量
    },
    "recentOpenedChests": [     // 最近开启的宝箱列表（最多10个）
      {
        "id": 2,            // 宝箱ID
        "openedAt": "2025-03-04 12:13:47",  // 开启时间
        "rewards": [          // 宝箱包含的奖励
          {
            "type": "ton",  // 奖励类型
            "amount": "0.700000"      // 奖励数量
          }
        ]
      },
      {
        "id": 3,            // 宝箱ID
        "openedAt": "2025-03-04 12:14:16",  // 开启时间
        "rewards": [          // 宝箱包含的奖励
          {
            "type": "gem",  // 奖励类型
            "amount": "64.000000"      // 奖励数量
          }
        ]
      }
    ]
  },
  "message": "Get chest rewards successfully"
}
```

### 注意事项


 1. 接口返回的最近开启宝箱列表最多包含10条记录
 2. 奖励类型包括：ticket、ton、gem三种
 3. 历史记录按照时间倒序排列
 4. 累计奖励统计仅统计最近500条记录

**接口状态**

> 开发中

**接口URL**

> api/rewards/chest-rewards

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取宝箱倒计时状态

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-17 16:44:03

> 更新时间: 2025-03-25 17:11:19

## 宝箱倒计时状态 API

#### 获取宝箱倒计时状态

**接口路径：GET /api/jackpot-chest/countdown**

```
GET /api/jackpot-chest/countdown
```

**接口描述：获取用户的宝箱倒计时状态，包括是否可以领取宝箱、下次可领取时间、剩余时间等信息。用户需要等待倒计时结束后才能领取宝箱。,请求方法：GET,认证要求：需要钱包认证（walletAuthMiddleware）,请求参数：无需请求参数，接口会自动根据用户ID和钱包ID获取倒计时状态。,响应结果：**

```json
{
  "ok": true,
  "data": {
    "canCollect": true,
    "autoCollect": false,
    "nextAvailableTime": "2023-01-01T00:00:00.000Z",
    "remainingTime": {
      "total": 0,
      "hours": 0,
      "minutes": 0,
      "seconds": 0
    }
  }
}
```

**错误响应：**

```json
{
  "ok": false,
  "message": "服务器内部错误"
}
```

**响应字段说明：**

| 字段名 | 类型  | 描述  |
| --- | --- | --- |
| canCollect | Boolean | 是否可以领取宝箱，true表示可以领取，false表示需要等待 |
| autoCollect | Boolean | 是否开启自动领取功能 |
| nextAvailableTime | String | 下次可领取宝箱的时间（ISO格式） |
| remainingTime | Object | 剩余时间信息 |
| remainingTime.total | Number | 剩余总毫秒数 |
| remainingTime.hours | Number | 剩余小时数 |
| remainingTime.minutes | Number | 剩余分钟数 |
| remainingTime.seconds | Number | 剩余秒数 |

**错误代码：**

| 错误消息 | 描述  |
| ---- | --- |
| errors.serverError | 服务器内部错误 |

**注意事项：**


 1. 当canCollect为true时，用户可以调用/api/jackpot-chest/collect接口领取宝箱。
 2. 如果开启了自动领取功能（autoCollect为true），系统会在倒计时结束后自动领取宝箱。
 3. 新用户首次查询时会自动创建倒计时记录，并且可以立即领取宝箱。
 4. 领取宝箱后，系统会重置倒计时，新的倒计时时长为配置的小时数（默认24小时）。

**如果开启了自动领取功能（autoCollect为true），系统会在倒计时结束后自动领取宝箱。**

```
undefined
```

**新用户首次查询时会自动创建倒计时记录，并且可以立即领取宝箱。**

**领取宝箱后，系统会重置倒计时，新的倒计时时长为配置的小时数（默认24小时）。**

**接口状态**

> 开发中

**接口URL**

> api/jackpot-chest/countdown

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
	"ok": true,
	"data": {
		"canCollect": false,
		"autoCollect": false,
		"nextAvailableTime": "2025-03-18 17:22:24",
		"remainingTime": {
			"total": 86384393,
			"hours": 23,
			"minutes": 59,
			"seconds": 44
		}
	}
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 领取倒计时宝箱

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-17 17:20:00

> 更新时间: 2025-04-06 14:01:04

## 领取倒计时宝箱 API

#### 领取倒计时宝箱

**接口路径：POST /api/jackpot-chest/collect**

```
POST /api/jackpot-chest/collect
```

**接口描述：领取用户的倒计时宝箱，并立即打开获得奖励。用户需要等待倒计时结束后才能领取宝箱，领取后会重新开始倒计时。,请求方法：POST,认证要求：需要钱包认证（walletAuthMiddleware）,请求参数：无需请求参数，接口会自动根据用户ID和钱包ID领取宝箱。,响应结果：**

```json
{
    "ok": true,
    "data": {
        "chest": {
            "id": 11,
            "userId": 1,
            "walletId": 1,
            "isOpened": false,
            "type": "countdown",
            "updatedAt": "2025-04-06T04:45:30.227Z",
            "createdAt": "2025-04-06T04:45:30.227Z"
        },
        "result": {
            "openedCount": 1,
            "chestIds": [
                11
            ],
            "rewards": [
                {
                    "level": 2,
                    "items": [
                        {
                            "type": "fragment_green",
                            "amount": 9
                        },
                        {
                            "type": "fragment_blue",
                            "amount": 4
                        },
                        {
                            "type": "gem",
                            "amount": 14337
                        }
                    ]
                }
            ],
            "summary": {
                "ticket": 0,
                "fragment_green": 9,
                "fragment_blue": 4,
                "fragment_purple": 0,
                "fragment_gold": 0,
                "ton": 0,
                "gem": 14337
            },
            "levelSummary": {
                "level1": 0,
                "level2": 1,
                "level3": 0,
                "level4": 0
            },
            "shareLinks": [],
            "jackpotWinner": {
                            "level": 1,  //获奖池子等级
                            "amount": 10,
                            "userId": 1,
                            "walletId": 1,
                            "poolId": 1,
                            "winTime": "2025-04-06T05:57:42.861Z"
                    }
        }
    }
}
```

**错误响应：**

```json
{
  "ok": false,
  "message": "宝箱尚未可领取"
}
```

**响应字段说明：**

| 字段名 | 类型  | 描述  |
| --- | --- | --- |
| chest | Object | 宝箱对象信息 |
| chest.id | Number | 宝箱ID |
| chest.userId | Number | 用户ID |
| chest.walletId | Number | 钱包ID |
| chest.isOpened | Boolean | 是否已打开 |
| chest.type | String | 宝箱类型，"countdown"表示倒计时宝箱 |
| chest.createdAt | String | 创建时间 |
| chest.updatedAt | String | 更新时间 |
| result | Object | 宝箱开启结果 |
| result.rewards | Array | 奖励列表 |
| result.rewards[].type | String | 奖励类型，可能值：gem(宝石)、ticket_fragment(票据碎片)等 |
| result.rewards[].amount | Number | 奖励数量 |
| result.shareLink | Object | 分享链接信息，可能不存在 |
| result.shareLink.code | String | 分享链接代码 |
| result.shareLink.expiresAt | String | 分享链接过期时间 |

**错误代码：**

| 错误消息 | 描述  |
| ---- | --- |
| errors.countdownNotFound | 未找到用户的倒计时状态 |
| errors.chestNotAvailableYet | 宝箱尚未可领取，需等待倒计时结束 |
| errors.serverError | 服务器内部错误 |

**注意事项：**


 1. 领取宝箱后，系统会自动重置倒计时，新的倒计时时长为配置的小时数。
 2. 如果开启了自动领取功能，系统会在倒计时结束后自动领取宝箱。
 3. 宝箱奖励是随机的，根据配置的奖励池决定。
 4. 某些宝箱可能会生成分享链接，用户可以分享给好友获得额外加速。

**接口状态**

> 开发中

**接口URL**

> api/jackpot-chest/collect

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
	"ok": true,
	"data": {
		"chest": {
			"id": 11,
			"userId": 1,
			"walletId": 1,
			"isOpened": false,
			"type": "countdown",
			"updatedAt": "2025-04-06T04:45:30.227Z",
			"createdAt": "2025-04-06T04:45:30.227Z"
		},
		"result": {
			"openedCount": 1,
			"chestIds": [
				11
			],
			"rewards": [
				{
					"level": 2,
					"items": [
						{
							"type": "fragment_green",
							"amount": 9
						},
						{
							"type": "fragment_blue",
							"amount": 4
						},
						{
							"type": "gem",
							"amount": 14337
						}
					]
				}
			],
			"summary": {
				"ticket": 0,
				"fragment_green": 9,
				"fragment_blue": 4,
				"fragment_purple": 0,
				"fragment_gold": 0,
				"ton": 0,
				"gem": 14337
			},
			"levelSummary": {
				"level1": 0,
				"level2": 1,
				"level3": 0,
				"level4": 0
			},
			"shareLinks": [],
			"jackpotWinner": null
		}
	}
}
```

* 失败(400)

```javascript
{
	"ok": false,
	"message": "Chest is not available for collection yet"
}
```

**Query**

# 获取Jackpot奖池状态

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-18 11:12:19

> 更新时间: 2025-04-07 14:40:51

```
# Jackpot奖池状态 API

### 获取Jackpot奖池状态

**接口路径**：`GET /api/jackpot-chest/pool-status`

**接口描述**：获取当前Jackpot奖池的状态信息，包括所有奖池级别的详细数据、当前活跃奖池信息以及最近获奖者列表。此接口可用于展示奖池进度、贡献来源和获奖历史等信息。

**请求方法**：GET

**认证要求**：需要钱包认证

**请求参数**：无

**响应结果**：

```json
{
  "ok": true,
  "data": {
    "pools": [
      {
        "level": 1,
        "currentAmount": 5.5,
        "targetAmount": 10,
        "newUserAmount": 2.5,
        "chestOpenAmount": 3,
        "newUserProgress": 25,
        "chestOpenProgress": 30,
        "totalProgress": 55,
        "isActive": true
      },
      {
        "level": 2,
        "currentAmount": 0,
        "targetAmount": 20,
        "newUserAmount": 0,
        "chestOpenAmount": 0,
        "newUserProgress": 0,
        "chestOpenProgress": 0,
        "totalProgress": 0,
        "isActive": false
      }
      // 可能有更多奖池...
    ],
    "activePool": {
      "level": 1,
      "currentAmount": 5.5,
      "targetAmount": 10,
      "newUserAmount": 2.5,
      "chestOpenAmount": 3,
      "newUserProgress": 25,
      "chestOpenProgress": 30,
      "totalProgress": 55
    },
    "recentWinners": [
      {
        "level": 3,
        "userId": 123,
        "walletId": 456,
        "username": "user123",
        "photoUrl": "https://example.com/photo.jpg",
        "walletAddress": "EQA...",
        "rewardAmount": 10,
        "winTime": "2023-05-20T08:30:00.000Z"
      }
      // 可能有更多获奖者...
    ]
  }
}
```

**响应字段说明：**

| 字段名 | 类型  | 描述  |
| --- | --- | --- |
| pools | Array | 所有奖池的列表 |
| pools[].level | Number | 奖池等级，从1开始 |
| pools[].currentAmount | Number | 当前奖池累积的总金额 |
| pools[].targetAmount | Number | 奖池目标金额，达到后触发奖励 |
| pools[].newUserAmount | Number | 新用户贡献的累积金额 |
| pools[].chestOpenAmount | Number | 开宝箱贡献的累积金额 |
| pools[].newUserProgress | Number | 新用户贡献进度百分比 |
| pools[].chestOpenProgress | Number | 开宝箱贡献进度百分比 |
| pools[].totalProgress | Number | 总进度百分比 |
| pools[].isActive | Boolean | 是否为当前活跃奖池 |
| activePool | Object | 当前活跃奖池信息，字段与pools[]中的单个奖池相同 |
| recentWinners | Array | 最近获奖者列表 |
| recentWinners[].level | Number | 获奖的奖池等级 |
| recentWinners[].userId | Number | 获奖用户ID |
| recentWinners[].walletId | Number | 获奖用户钱包ID |
| recentWinners[].username | String | 获奖用户名 |
| recentWinners[].photoUrl | String | 获奖用户头像URL |
| recentWinners[].walletAddress | String | 获奖用户钱包地址 |
| recentWinners[].rewardAmount | Number | 获得的奖励金额 |
| recentWinners[].winTime | String | 获奖时间，ISO格式的日期字符串 |

**错误响应：**

```json
{
  "ok": false,
  "message": "获取Jackpot奖池状态失败"
}
```

**错误码说明：**

| HTTP状态码 | 错误信息 | 描述  |
| ------- | ---- | --- |
| 500 | 服务器错误 | 服务器内部错误，无法获取奖池状态 |
| ``` |  |  |

**接口状态**

> 开发中

**接口URL**

> api/jackpot-chest/pool-status

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
	"ok": true,
	"data": {
		"pools": [
			{
				"level": 1,
				"currentAmount": 10,
				"targetAmount": 10,
				"newUserAmount": 10,
				"chestOpenAmount": 9.999,
				"newUserProgress": 100,
				"chestOpenProgress": 99,
				"totalProgress": 100,
				"isActive": false
			},
			{
				"level": 2,
				"currentAmount": 0,
				"targetAmount": 20,
				"newUserAmount": 0,
				"chestOpenAmount": 0,
				"newUserProgress": 0,
				"chestOpenProgress": 0,
				"totalProgress": 0,
				"isActive": true
			},
			{
				"level": 3,
				"currentAmount": 0,
				"targetAmount": 50,
				"newUserAmount": 0,
				"chestOpenAmount": 0,
				"newUserProgress": 0,
				"chestOpenProgress": 0,
				"totalProgress": 0,
				"isActive": false
			}
		],
		"activePool": {
			"level": 2,
			"currentAmount": 0,
			"targetAmount": 20,
			"newUserAmount": 0,
			"chestOpenAmount": 0,
			"newUserProgress": 0,
			"chestOpenProgress": 0,
			"totalProgress": 0
		},
		"recentWinners": [
			{
				"level": 1,
				"userId": 1,
				"walletId": 1,
				"username": "Zoyonlian",
				"photoUrl": "",
				"walletAddress": "0QDioPTSTodOvpJe5czY963Jry4QilH3tMBzZn-1vFbhNmLO",
				"rewardAmount": 10,
				"winTime": "2025-03-21 15:50:36"
			}
		]
	}
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取用户的分享助力链接

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-18 11:18:32

> 更新时间: 2025-03-26 15:36:41

## 分享助力链接 API

#### 获取分享助力链接

**接口路径：GET /api/jackpot-chest/share-links**

```
GET /api/jackpot-chest/share-links
```

**接口描述：获取用户的分享助力链接列表，包括链接ID、分享码、宝箱等级、当前使用次数、最大使用次数、过期时间、剩余有效时间和无法使用原因等信息。默认只返回未过期且未达到最大使用次数的链接。,请求方法：GET,认证要求：需要钱包认证（walletAuthMiddleware）,请求参数：**

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| status | String | 否 | 链接状态过滤，可选值：'all'（全部）, 'used'（已使用）, 'unused'（未使用），默认为'unused' |

**响应结果：**

```json
{
  "ok": true,
  "data": [
    {
      "id": 123,
      "code": "abc123",
      "chestLevel": 2,
      "currentUses": 3,
      "maxUses": 5,
      "expiresAt": "2023-01-01T00:00:00.000Z",
      "remainingTime": 86400000,
      "unusableReason": null
    }
    // 更多链接...
  ]
}
```

**响应字段说明：**

| 字段名 | 类型  | 描述  |
| --- | --- | --- |
| id | Number | 分享链接ID |
| code | String | 分享链接的唯一码，用于助力 |
| chestLevel | Number | 宝箱等级 |
| currentUses | Number | 当前已使用次数 |
| maxUses | Number | 最大可使用次数 |
| expiresAt | String | 过期时间，ISO格式的日期字符串 |
| remainingTime | Number | 剩余有效时间（毫秒） |
| unusableReason | String | 链接无法使用的原因，如果链接可用则为null。可能的值：链接已过期、链接已达到最大使用次数 |

**错误响应：**

```json
{
  "ok": false,
  "message": "获取用户分享助力链接失败"
}
```

**错误码：**

| HTTP状态码 | 错误信息 | 描述  |
| ------- | ---- | --- |
| 500 | 服务器错误 | 服务器内部错误 |
| 401 | 未授权 | 用户未登录或token无效 |

**接口状态**

> 开发中

**接口URL**

> api/jackpot-chest/share-links

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
	"ok": true,
	"data": [
		{
			"id": 1,
			"code": "cf8c0881-d234-49cb-9f8e-8f53491bce67",
			"chestLevel": 3,
			"currentUses": 1,
			"maxUses": 12,
			"expiresAt": "2025-03-26 16:44:25",
			"remainingTime": 4925112,
			"unusableReason": null
		},
		{
			"id": 2,
			"code": "a7983672-b299-4df8-a9e8-ad3f326fbd46",
			"chestLevel": 3,
			"currentUses": 0,
			"maxUses": 12,
			"expiresAt": "2025-03-26 16:44:25",
			"remainingTime": 4925112,
			"unusableReason": null
		},
		{
			"id": 3,
			"code": "32535a72-0445-429c-b1fc-40d225f29a5f",
			"chestLevel": 3,
			"currentUses": 0,
			"maxUses": 12,
			"expiresAt": "2025-03-26 16:44:25",
			"remainingTime": 4925112,
			"unusableReason": null
		}
	]
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 推荐可用宝箱数量

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-19 23:20:18

> 更新时间: 2025-03-25 17:08:37

## 推荐宝箱数量 API

#### 获取推荐宝箱数量

**接口路径：GET /api/referral/referral-chests/count**

```
GET /api/referral/referral-chests/count
```

**接口描述：获取用户未开启的推荐宝箱数量，包括普通推荐和高级推荐两种类型的统计。推荐宝箱是通过推荐系统获得的特殊宝箱，可以通过开启获得不同等级的奖励。,请求方法：GET,认证要求：需要钱包认证（walletAuthMiddleware）,请求参数：无需请求参数，接口会自动获取当前用户的推荐宝箱数量。,响应结果：**

```json
{
  "ok": true,
  "data": {
    "count": {
      "total": 5,
      "premium_referral": 2,
      "normal_referral": 3
    },
    "limit": 10
  }
}
```

**响应字段说明：**

| 字段名 | 类型  | 描述  |
| --- | --- | --- |
| count | Object | 推荐宝箱数量统计 |
| count.total | Number | 未开启的推荐宝箱总数 |
| count.premium_referral | Number | 高级推荐宝箱数量（由Telegram Premium用户推荐获得） |
| count.normal_referral | Number | 普通推荐宝箱数量（由普通用户推荐获得） |
| limit | Number | 系统设置的宝箱数量上限 |

**错误码：**

| 状态码 | 错误信息 | 描述  |
| --- | ---- | --- |
| 400 | 缺少用户ID或钱包ID | 当请求中缺少用户ID或钱包ID时返回 |
| 401 | 未授权 | 当用户未登录或token无效时返回 |
| 500 | 获取推荐宝箱数量失败 | 服务器内部错误 |

**示例：**

```
GET /api/referral/referral-chests/count
```

**业务逻辑说明：**


 1. 接口会查询用户所有未开启的推荐宝箱(type='referral')并按来源(source)分组统计
 2. 推荐宝箱分为两种类型：
 3. premium_referral: 由Telegram Premium用户推荐获得的高级宝箱
 4. normal_referral: 由普通用户推荐获得的普通宝箱
 5. 
 6. 
 7. 推荐宝箱可以通过/api/referral/open接口开启获取奖励
 8. 系统设置了宝箱数量上限(limit)，用于前端显示

**接口状态**

> 开发中

**接口URL**

> api/referral/referral-chests/count

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
	"ok": true,
	"data": {
		"count": {
			"total": 2,
			"premium_referral": 0,
			"normal_referral": 2
		},
		"limit": 10
	}
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 打开推荐宝箱

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-19 23:30:46

> 更新时间: 2025-04-08 23:24:36

```text
# 推荐宝箱开启 API

### 开启推荐宝箱

**接口路径**：`POST /api/referral/open`

**接口描述**：开启用户所有未开启的推荐宝箱，并获取相应的奖励。推荐宝箱是通过推荐系统获得的特殊宝箱，开启后可获得不同等级的奖励，并可能触发分享链接生成。

**请求方法**：POST

**认证要求**：需要钱包认证

**请求参数**：无需请求参数，接口会自动开启用户所有未开启的推荐宝箱。

**响应结果**：

json
{
	"ok": true,
	"data": {
		"openedCount": 1,
		"chestIds": [
			16
		],
		"rewards": [
			{
				"level": 3,
				"items": [
					{
						"type": "fragment_blue",
						"amount": 9
					},
					{
						"type": "fragment_purple",
						"amount": 3
					},
					{
						"type": "gem",
						"amount": 48349
					}
				]
			}
		],
		"summary": {
			"ticket": 0,
			"fragment_green": 0,
			"fragment_blue": 9,
			"fragment_purple": 3,
			"fragment_gold": 0,
			"ton": 0,
			"gem": 48349
		},
		"levelSummary": {
			"level1": 0,
			"level2": 0,
			"level3": 1,
			"level4": 0
		},
		"shareLinks": [
			{
				"chestId": 16,
				"chestLevel": 3,
				"shareCode": "6fbf614a-78ec-4515-9940-ec391954a768",
				"maxUses": 12,
				"currentUses": 0,
				"expiresAt": "2025-04-07 13:57:42"
			}
		],
		"jackpotWinner": {
			"level": 1,  //获奖池子等级
			"amount": 10,
			"userId": 1,
			"walletId": 1,
			"poolId": 1,
			"winTime": "2025-04-06T05:57:42.861Z"
		}
	}
}

**响应字段说明**：

| 字段名 | 类型 | 描述 |
|-------|-----|------|
| openedCount | Number | 本次开启的宝箱数量 |
| chestIds | Array | 开启的宝箱ID列表 |
| rewards | Array | 宝箱奖励列表 |
| rewards[].level | Number | 宝箱等级(1-4) |
| rewards[].items | Array | 宝箱包含的奖励项 |
| rewards[].items[].type | String | 奖励类型，可能值: gem, ticket, ticket_fragment, ton |
| rewards[].items[].amount | Number | 奖励数量 |
| summary | Object | 奖励汇总 |
| summary.ticket | Number | 获得的门票总数 |
| summary.ticket_fragment | Number | 获得的门票碎片总数 |
| summary.ton | Number | 获得的TON总数 |
| summary.gem | Number | 获得的宝石总数 |
| levelSummary | Object | 宝箱等级汇总 |
| levelSummary.level1 | Number | 1级宝箱数量 |
| levelSummary.level2 | Number | 2级宝箱数量 |
| levelSummary.level3 | Number | 3级宝箱数量 |
| levelSummary.level4 | Number | 4级宝箱数量 |
| shareLinks | Array | 高级宝箱(3级及以上)生成的分享链接 |
| shareLinks[].chestId | Number | 宝箱ID |
| shareLinks[].chestLevel | Number | 宝箱等级 |
| shareLinks[].shareCode | String | 分享码 |
| shareLinks[].maxUses | Number | 最大使用次数 |
| shareLinks[].currentUses | Number | 当前已使用次数 |
| shareLinks[].expiresAt | String | 过期时间 |
| jackpotWinner | Object/null | 如果触发Jackpot奖励，返回获奖信息，否则为null |

**错误码**：

| 状态码 | 错误信息 | 描述 |
|-------|---------|------|
| 400 | 缺少用户ID或钱包ID | 当请求中缺少用户ID或钱包ID时返回 |
| 400 | 没有可用的推荐宝箱 | 当用户没有未开启的推荐宝箱时返回 |
| 401 | 未授权 | 当用户未登录或token无效时返回 |
| 500 | 服务器内部错误 | 服务器处理请求时发生错误 |

**示例**：

POST /api/referral/open

**业务逻辑说明**：

1. 接口会查询用户所有未开启的推荐宝箱(type='referral')并一次性全部开启
2. 宝箱奖励根据概率随机生成，分为4个等级：
   
   | 宝箱类型 | 绿色碎片 | 蓝色碎片 | 紫色碎片 | 金色碎片 | 宝石 |
   |---------|---------|---------|---------|---------|------|
   | LV1 宝箱 (60%) | 16-24 (100%) | 1-4 (30%) | - | - | 1,000-5,000 (100%) |
   | LV2 宝箱 (28%) | 8-15 (100%) | 4-8 (100%) | 0-1 (20%) | - | 5,000-15,000 (100%) |
   | LV3 宝箱 (10%) | - | 6-12 (100%) | 2-4 (100%) | 0-1 (28%) | 20,000-50,000 (100%) |
   | LV4 宝箱 (2%) | - | - | 6-10 (100%) | 2-4 (100%) | 50,000-100,000 (100%) |

3. 3级及以上宝箱会自动生成分享链接，可用于邀请好友助力
4. 开启宝箱会为用户的推荐人提供加速奖励
5. 每次开启宝箱会向Jackpot奖池贡献0.0001 TON，有机会触发Jackpot奖励
```

**接口状态**

> 开发中

**接口URL**

> api/referral/open

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
	"ok": true,
	"data": {
		"openedCount": 1,
		"chestIds": [
			16
		],
		"rewards": [
			{
				"level": 3,
				"items": [
					{
						"type": "fragment_blue",
						"amount": 9
					},
					{
						"type": "fragment_purple",
						"amount": 3
					},
					{
						"type": "gem",
						"amount": 48349
					}
				]
			}
		],
		"summary": {
			"ticket": 0,
			"fragment_green": 0,
			"fragment_blue": 9,
			"fragment_purple": 3,
			"fragment_gold": 0,
			"ton": 0,
			"gem": 48349
		},
		"levelSummary": {
			"level1": 0,
			"level2": 0,
			"level3": 1,
			"level4": 0
		},
		"shareLinks": [
			{
				"chestId": 16,
				"chestLevel": 3,
				"shareCode": "6fbf614a-78ec-4515-9940-ec391954a768",
				"maxUses": 12,
				"currentUses": 0,
				"expiresAt": "2025-04-07 13:57:42"
			}
		],
		"jackpotWinner": {
			"level": 1,
			"amount": 10,
			"userId": 1,
			"walletId": 1,
			"poolId": 1,
			"winTime": "2025-04-06T05:57:42.861Z"
		}
	}
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取用户收到的助力历史

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-21 22:42:58

> 更新时间: 2025-03-25 17:05:48

## 收到的助力历史 API

#### 获取收到的助力历史

**接口路径：GET /api/jackpot-chest/incoming-boost-history**

```
GET /api/jackpot-chest/incoming-boost-history
```

**接口描述：获取用户收到的助力历史记录。支持分页查询，并返回助力记录的详细信息，包括助力用户信息、助力时间和助力分钟数等。,请求方法：GET,认证要求：需要钱包认证（walletAuthMiddleware）,请求参数：**

| 参数名 | 类型  | 必填  | 默认值 | 描述  |
| --- | --- | --- | --- | --- |
| page | Number | 否 | 1 | 页码，从1开始 |
| pageSize | Number | 否 | 10 | 每页记录数 |

**响应结果：**

```json
{
  "ok": true,
  "data": {
    "items": [
      {
        "id": 1,
        "type": "referral",
        "direction": "incoming",
        "chestLevel": 1,
        "boostMinutes": 10,
        "sourceLevel": 1,
        "gemAmount": 0,
        "createdAt": "2025-03-23 21:52:19",
        "sourceUser": {
          "userId": 2,
          "username": "",
          "photoUrl": "",
          "walletAddress": "UQBhQxkk_fkznrqzwOAwrNJlHdp1el2f-AJoa1UBMUz-ERGb"
        }
      },
      {
        "id": 2,
        "type": "referral",
        "direction": "incoming",
        "chestLevel": 1,
        "boostMinutes": 10,
        "sourceLevel": 1,
        "gemAmount": 0,
        "createdAt": "2025-03-23 21:52:19",
        "sourceUser": {
          "userId": 2,
          "username": "",
          "photoUrl": "",
          "walletAddress": "UQBhQxkk_fkznrqzwOAwrNJlHdp1el2f-AJoa1UBMUz-ERGb"
        }
      }
    ],
    "total": 2,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  }
}
```

**响应字段说明：**

| 字段名 | 类型  | 描述  |
| --- | --- | --- |
| items | Array | 助力记录列表 |
| items[].id | Number | 助力记录ID |
| items[].type | String | 助力类型，例如"referral"表示推荐助力 |
| items[].direction | String | 助力方向，固定为"incoming"表示收到的助力 |
| items[].chestLevel | Number | 宝箱等级 |
| items[].sourceLevel | Number | 来源次代等级 |
| items[].boostMinutes | Number | 助力分钟数 |
| items[].gemAmount | Number | 宝石奖励数量 |
| items[].createdAt | String | 助力创建时间 |
| items[].sourceUser | Object | 助力来源用户信息 |
| items[].sourceUser.userId | Number | 助力来源用户ID |
| items[].sourceUser.username | String | 助力来源用户名 |
| items[].sourceUser.photoUrl | String | 助力来源用户头像URL |
| items[].sourceUser.walletAddress | String | 助力来源用户钱包地址 |
| total | Number | 总记录数 |
| page | Number | 当前页码 |
| pageSize | Number | 每页记录数 |
| totalPages | Number | 总页数 |

**错误码：**

| 状态码 | 错误信息 | 描述  |
| --- | ---- | --- |
| 401 | 未授权 | 当用户未登录或token无效时返回 |
| 500 | 获取用户收到的助力历史失败 | 服务器内部错误 |

**示例：**

```
GET /api/jackpot-chest/incoming-boost-history?page=1&pageSize=10
```

**业务逻辑说明：**


 1. 该接口返回用户作为助力目标(targetUserId)的记录
 2. 记录按创建时间(createdAt)降序排序
 3. 每条记录包含助力相关的详细信息，如宝箱等级、助力时间和宝石奖励等
 4. 响应中包含sourceUser信息，表示助力的来源用户

**接口状态**

> 开发中

**接口URL**

> api/jackpot-chest/incoming-boost-history?page=1&pageSize=10

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| page | 1 | string | 是 | - |
| pageSize | 10 | string | 是 | - |

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
	"ok": true,
	"data": {
		"items": [
			{
				"id": 1,
				"type": "referral",
				"direction": "incoming",
				"chestLevel": 1,
				"boostMinutes": 10,
				"sourceLevel": 1,
				"gemAmount": 0,
				"createdAt": "2025-03-23 21:52:19",
				"sourceUser": {
					"userId": 2,
					"username": "",
					"photoUrl": "",
					"walletAddress": "UQBhQxkk_fkznrqzwOAwrNJlHdp1el2f-AJoa1UBMUz-ERGb"
				}
			},
			{
				"id": 2,
				"type": "referral",
				"direction": "incoming",
				"chestLevel": 1,
				"boostMinutes": 10,
				"sourceLevel": 1,
				"gemAmount": 0,
				"createdAt": "2025-03-23 21:52:19",
				"sourceUser": {
					"userId": 2,
					"username": "",
					"photoUrl": "",
					"walletAddress": "UQBhQxkk_fkznrqzwOAwrNJlHdp1el2f-AJoa1UBMUz-ERGb"
				}
			}
		],
		"total": 2,
		"page": 1,
		"pageSize": 10,
		"totalPages": 1
	}
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取用户的分享助力历史

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-21 23:18:27

> 更新时间: 2025-03-25 16:58:30

## 分享助力历史 API

#### 获取分享助力历史这个只是分享助力记录，不是推荐的

**接口路径：GET /api/jackpot-chest/share-boost-history**

```
GET /api/jackpot-chest/share-boost-history
```

**接口描述：获取用户的分享助力历史记录，可查询发出的助力(outgoing)或收到的助力(incoming)。支持分页查询，并返回助力记录的详细信息，包括助力用户信息、助力时间和助力分钟数等。,请求方法：GET,认证要求：需要钱包认证（walletAuthMiddleware）,请求参数：**

| 参数名 | 类型  | 必填  | 默认值 | 描述  |
| --- | --- | --- | --- | --- |
| direction | String | 否 | outgoing | 查询方向，outgoing表示发出的助力，incoming表示收到的助力 |
| page | Number | 否 | 1 | 页码，从1开始 |
| pageSize | Number | 否 | 10 | 每页记录数 |

**响应结果：**

```json
{
  "ok": true,
  "data": {
    "items": [
      {
        "id": 123,
        "type": "share",
        "direction": "outgoing",
        "chestLevel": 2,
        "sourceLevel": 1,
        "boostMinutes": 10,
        "gemAmount": 5,
        "createdAt": "2023-01-01 00:00:00",
        "targetUser": {
          "userId": 456,
          "username": "user456",
          "photoUrl": "https://example.com/photo.jpg",
          "walletAddress": "EQA..."
        }
      }
      // 更多记录...
    ],
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10
  }
}
```

**当direction为incoming时，响应中包含sourceUser而非targetUser：**

```json
{
  "ok": true,
  "data": {
    "items": [
      {
        "id": 123,
        "type": "share",
        "direction": "incoming",
        "chestLevel": 2,
        "sourceLevel": 1,
        "boostMinutes": 10,
        "gemAmount": 5,
        "createdAt": "2023-01-01 00:00:00",
        "sourceUser": {
          "userId": 456,
          "username": "user456",
          "photoUrl": "https://example.com/photo.jpg",
          "walletAddress": "EQA..."
        }
      }
      // 更多记录...
    ],
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10
  }
}
```

**响应字段说明：**

| 字段名 | 类型  | 描述  |
| --- | --- | --- |
| items | Array | 助力记录列表 |
| items[].id | Number | 助力记录ID |
| items[].type | String | 助力类型，固定为"share" |
| items[].direction | String | 助力方向，outgoing表示发出的助力，incoming表示收到的助力 |
| items[].chestLevel | Number | 宝箱等级 |
| items[].sourceLevel | Number | 来源次代等级 |
| items[].boostMinutes | Number | 助力时间(分钟) |
| items[].gemAmount | Number | 奖励的宝石数量 |
| items[].createdAt | String | 助力记录创建时间，格式为'YYYY-MM-DD HH:MM:SS' |
| items[].targetUser | Object | 接收助力的用户信息(仅在direction=outgoing时返回) |
| items[].targetUser.userId | Number | 用户ID |
| items[].targetUser.username | String | 用户名 |
| items[].targetUser.photoUrl | String | 用户头像URL |
| items[].targetUser.walletAddress | String | 钱包地址 |
| items[].sourceUser | Object | 提供助力的用户信息(仅在direction=incoming时返回) |
| items[].sourceUser.userId | Number | 用户ID |
| items[].sourceUser.username | String | 用户名 |
| items[].sourceUser.photoUrl | String | 用户头像URL |
| items[].sourceUser.walletAddress | String | 钱包地址 |
| total | Number | 总记录数 |
| page | Number | 当前页码 |
| pageSize | Number | 每页记录数 |
| totalPages | Number | 总页数 |

**错误码：**

| 状态码 | 错误信息 | 描述  |
| --- | ---- | --- |
| 401 | 未授权 | 当用户未登录或token无效时返回 |
| 500 | 获取用户分享助力历史失败 | 服务器内部错误 |

**示例：**

```
GET /api/jackpot-chest/share-boost-history?direction=incoming&page=1&pageSize=10
```

**业务逻辑说明：**


 1. 该接口只返回助力类型(boostType)为'share'的记录
 2. 当direction=outgoing时，返回用户作为助力源(sourceUserId)的记录
 3. 当direction=incoming时，返回用户作为助力目标(targetUserId)的记录
 4. 记录按创建时间(createdAt)降序排序
 5. 每条记录包含助力相关的详细信息，如宝箱等级、助力时间和宝石奖励等

**接口状态**

> 开发中

**接口URL**

> api/jackpot-chest/share-boost-history?direction=incoming

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| direction | incoming | string | 是 | - |

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
	"ok": true,
	"data": {
		"items": [
			{
				"id": 3,
				"type": "share",
				"direction": "incoming",
				"chestLevel": 1,
				"sourceLevel": 1,
				"boostMinutes": 10,
				"gemAmount": 0,
				"createdAt": "2025-03-23 21:52:19",
				"sourceUser": {
					"userId": 2,
					"username": "",
					"photoUrl": "",
					"walletAddress": "UQBhQxkk_fkznrqzwOAwrNJlHdp1el2f-AJoa1UBMUz-ERGb"
				}
			}
		],
		"total": 1,
		"page": 1,
		"pageSize": 10,
		"totalPages": 1
	}
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取下线列表

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-23 20:06:28

> 更新时间: 2025-04-15 15:48:49

### 推荐系统 API

#### 获取下线列表

**接口路径：GET /api/referral/downline**

```
GET /api/referral/downline
```

**接口描述：获取用户的下线列表，可选择查看第一层或第二层下线。支持分页查询，并返回下线用户的详细信息，包括游戏数据、宝箱数据和加速时间等统计信息。,请求方法：GET,认证要求：需要钱包认证（walletAuthMiddleware）,请求参数：**

| 参数名 | 类型  | 必填  | 默认值 | 描述  |
| --- | --- | --- | --- | --- |
| level | Number | 否 | 1 | 下线层级，1表示第一层下线，2表示第二层下线 |
| page | Number | 否 | 1 | 页码，从1开始 |
| pageSize | Number | 否 | 20 | 每页记录数 |

**响应结果：**

```json
{
  "ok": true,
  "data": {
    "users": [
      {
        "userId": 2,
        "username": "",
        "photoUrl": "",
        "registerTime": "2025-03-23 21:43:56",
        "walletId": 2,
        "walletAddress": "UQBhQxkk_fkznrqzwOAwrNJlHdp1el2f-AJoa1UBMUz-ERGb",
        "gameCount": 0,
        "totalBetAmount": 0,
        "todayChestCount": 0,
        "weekChestCount": 2,
        "todayBoostMinutes": "0",
        "weekBoostMinutes": "20"
      },
      {
        "userId": 3,
        "username": "zoyonlian",
        "photoUrl": "",
        "registerTime": "2025-03-23 21:43:57",
        "walletId": 3,
        "walletAddress": "UQCD55QhtRuqNBYsxEMsh80ZIrvJ6GridII1gyBqI-NV4Rlr",
        "gameCount": 0,
        "totalBetAmount": 0,
        "todayChestCount": 0,
        "weekChestCount": 1,
        "todayBoostMinutes": "0",
        "weekBoostMinutes": "0"
      }
    ],
    "total": 2,
    "page": 1,
    "pageSize": 20,
    "level": 1
  }
}
```

**响应字段说明：**

| 字段名 | 类型  | 描述  |
| --- | --- | --- |
| users | Array | 下线用户列表 |
| users[].userId | Number | 用户ID |
| users[].username | String | 用户名 |
| users[].photoUrl | String | 用户头像URL |
| users[].registerTime | String | 注册时间 |
| users[].walletId | Number | 钱包ID |
| users[].walletAddress | String | 钱包地址 |
| users[].gameCount | Number | 游戏次数 |
| users[].totalBetAmount | Number | 总下注金额 |
| users[].todayChestCount | Number | 今日开启宝箱数量 |
| users[].weekChestCount | Number | 本周开启宝箱数量 |
| users[].todayBoostMinutes | Number | 今日提供的加速时间(分钟) |
| users[].weekBoostMinutes | Number | 本周提供的加速时间(分钟) |
| users[].parentId | Number | 上级用户ID (仅在level=2时返回) |
| users[].parentUsername | String | 上级用户名 (仅在level=2时返回) |
| total | Number | 总记录数 |
| page | Number | 当前页码 |
| pageSize | Number | 每页记录数 |
| level | Number | 当前查询的层级 |

**错误码：**

| 状态码 | 错误信息 | 描述  |
| --- | ---- | --- |
| 400 | 层级参数无效，只能是1或2 | 当level参数不是1或2时返回 |
| 401 | 未授权 | 当用户未登录或token无效时返回 |
| 500 | 获取下线列表失败 | 服务器内部错误 |

**示例：**

```
GET /api/referral/downline?level=1&page=1&pageSize=10
```

**业务逻辑说明：**


 1. 第一层下线(level=1)：直接推荐的用户
 2. 第二层下线(level=2)：由第一层下线推荐的用户
 3. 结果默认按照本周提供的加速时间(weekBoostMinutes)降序排序，其次按总下注金额(totalBetAmount)降序排序
 4. 统计数据包括：
 5. 游戏相关：游戏次数、总下注金额
 6. 宝箱相关：今日和本周开启的宝箱数量
 7. 加速相关：今日和本周提供给当前用户的加速时间

**加速相关：今日和本周提供给当前用户的加速时间**

**接口状态**

> 开发中

**接口URL**

> api/referral/downline?level=2

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| level | 2 | string | 是 | - |

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取自己获得推荐加速信息

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-23 22:04:32

> 更新时间: 2025-03-25 16:35:01

#### 获取用户加速信息

**接口路径：GET /api/jackpot-chest/boost-info**

```
GET /api/jackpot-chest/boost-info
```

**接口描述：获取用户的加速信息，包括今日和本周的加速时间统计以及最近的加速记录列表。支持分页查询。,请求方法：GET,认证要求：需要钱包认证（walletAuthMiddleware）,请求参数：**

| 参数名 | 类型  | 必填  | 默认值 | 描述  |
| --- | --- | --- | --- | --- |
| page | Number | 否 | 1 | 页码，从1开始 |
| pageSize | Number | 否 | 10 | 每页记录数 |

**响应结果：**

```json
{
    "ok": true,
    "data": {
        "boostInfo": {
            "todayBoostMinutes": "0",
            "weekBoostMinutes": "20",
            "todayBoostCount": 0,
            "weekBoostCount": 2
        },
        "recentBoosts": {
            "items": [
                {
                    "id": 1,
                    "sourceUserId": 2,
                    "sourceWalletId": 2,
                    "sourceLevel": 1,
                    "targetUserId": 1,
                    "targetWalletId": 1,
                    "boostType": "referral",
                    "boostMinutes": 10,
                    "chestLevel": 1,
                    "gemAmount": 0,
                    "isProcessed": true,
                    "expiresAt": "2025-03-24 21:52:19",
                    "createdAt": "2025-03-23 21:52:19",
                    "updatedAt": "2025-03-23 21:52:19",
                    "sourceUser": {
                        "id": 2,
                        "username": "",
                        "photoUrl": ""
                    }
                }
            ],
            "total": 2,
            "page": 1,
            "pageSize": 10,
            "totalPages": 1
        }
    }
}
```

**响应字段说明：**

| 字段名 | 类型  | 描述  |
| --- | --- | --- |
| boostInfo | Object | 加速统计信息 |
| boostInfo.todayBoostMinutes | Number | 今日获得的加速时间(分钟) |
| boostInfo.weekBoostMinutes | Number | 本周获得的加速时间(分钟) |
| boostInfo.todayBoostCount | Number | 今日获得的加速次数 |
| boostInfo.weekBoostCount | Number | 本周获得的加速次数 |
| recentBoosts | Object | 最近的加速记录分页信息 |
| recentBoosts.items | Array | 加速记录列表 |
| recentBoosts.items[].id | Number | 加速记录ID |
| recentBoosts.items[].sourceUserId | Number | 提供加速的用户ID |
| recentBoosts.items[].sourceWalletId | Number | 提供加速的钱包ID |
| recentBoosts.items[].sourceLevel | Number | 提供加速的用户等级 |
| recentBoosts.items[].targetUserId | Number | 接收加速的用户ID |
| recentBoosts.items[].targetWalletId | Number | 接收加速的钱包ID |
| recentBoosts.items[].boostMinutes | Number | 加速时间(分钟) |
| recentBoosts.items[].boostType | String | 加速类型，固定为"referral" |
| recentBoosts.items[].chestLevel | Number | 宝箱等级 |
| recentBoosts.items[].gemAmount | Number | 宝石数量 |
| recentBoosts.items[].isProcessed | Boolean | 是否已处理 |
| recentBoosts.items[].expiresAt | String | 加速过期时间 |
| recentBoosts.items[].createdAt | String | 加速记录创建时间 |
| recentBoosts.items[].updatedAt | String | 加速记录更新时间 |
| recentBoosts.items[].sourceUser | Object | 提供加速的用户信息 |
| recentBoosts.items[].sourceUser.id | Number | 用户ID |
| recentBoosts.items[].sourceUser.username | String | 用户名 |
| recentBoosts.items[].sourceUser.photoUrl | String | 用户头像URL |
| recentBoosts.total | Number | 总记录数 |
| recentBoosts.page | Number | 当前页码 |
| recentBoosts.pageSize | Number | 每页记录数 |
| recentBoosts.totalPages | Number | 总页数 |

**错误码：**

| 状态码 | 错误信息 | 描述  |
| --- | ---- | --- |
| 401 | 未授权 | 当用户未登录或token无效时返回 |
| 500 | 获取用户加速信息失败 | 服务器内部错误 |

**示例：**

```
GET /api/jackpot-chest/boost-info?page=1&pageSize=10
```

**业务逻辑说明：**


 1. 统计数据只包括推荐类型(referral)的加速记录
 2. 今日统计从当天0点开始计算
 3. 本周统计从7天前的0点开始计算
 4. 加速记录按创建时间降序排序

**接口状态**

> 开发中

**接口URL**

> api/jackpot-chest/boost-info?page=1&pageSize=10

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| page | 1 | string | 是 | - |
| pageSize | 10 | string | 是 | - |

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
	"ok": true,
	"data": {
		"boostInfo": {
			"todayBoostMinutes": "20",
			"weekBoostMinutes": "20",
			"todayBoostCount": 2,
			"weekBoostCount": 2
		},
		"recentBoosts": {
			"items": [
				{
					"id": 1,
					"sourceUserId": 2,
					"sourceWalletId": 2,
					"sourceLevel": 1,
					"targetUserId": 1,
					"targetWalletId": 1,
					"boostType": "referral",
					"boostMinutes": 10,
					"chestLevel": 1,
					"gemAmount": 0,
					"isProcessed": true,
					"expiresAt": "2025-03-24 21:52:19",
					"createdAt": "2025-03-23 21:52:19",
					"updatedAt": "2025-03-23 21:52:19",
					"sourceUser": {
						"id": 2,
						"username": "",
						"photoUrl": ""
					}
				},
				{
					"id": 2,
					"sourceUserId": 2,
					"sourceWalletId": 2,
					"sourceLevel": 1,
					"targetUserId": 1,
					"targetWalletId": 1,
					"boostType": "referral",
					"boostMinutes": 10,
					"chestLevel": 1,
					"gemAmount": 0,
					"isProcessed": true,
					"expiresAt": "2025-03-24 21:52:19",
					"createdAt": "2025-03-23 21:52:19",
					"updatedAt": "2025-03-23 21:52:19",
					"sourceUser": {
						"id": 2,
						"username": "",
						"photoUrl": ""
					}
				}
			],
			"total": 2,
			"page": 1,
			"pageSize": 10,
			"totalPages": 1
		}
	}
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 使用Telegram分享助力链接

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-03-25 17:14:37

> 更新时间: 2025-03-27 18:22:44

## Telegram分享助力接口 API 文档

### 接口概述

**接口路径：POST /api/telegram/boost**

```
POST /api/telegram/boost
```

**接口描述：该接口用于Telegram用户使用分享助力链接，为分享链接的创建者提供宝箱倒计时加速和宝石奖励。与普通的分享助力接口不同，此接口只需要Telegram认证，不需要钱包认证，因此未注册的Telegram用户也可以使用。,请求方法：POST,认证要求：需要Telegram认证（telegramAuthMiddleware）**

### 认证方式

**该接口使用Telegram WebApp的认证机制，前端需要在请求体中包含Telegram WebApp的initData。**

```javascript
// 前端示例代码
const initData = window.Telegram.WebApp.initData;

fetch('/api/telegram/boost', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    initData: initData,
    code: 'abc123' // 分享链接的唯一码
  })
});
```

### 请求参数

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| initData | String | 是 | Telegram WebApp的初始化数据，用于验证用户身份 |
| code | String | 是 | 分享链接的唯一码，用于识别要使用的分享链接 |

### 响应结果

#### 成功响应

```json
{
  "ok": true,
  "data": {
    "boostMinutes": 30,
    "gemAmount": 50,
  }
}
```

#### 响应字段说明

| 字段名 | 类型  | 描述  |
| --- | --- | --- |
| boostMinutes | Number | 为分享链接创建者提供的宝箱倒计时加速时间（分钟） |
| gemAmount | Number | 为分享链接创建者提供的宝石奖励数量 |

#### 错误响应

```json
{
  "ok": false,
  "message": "错误信息"
}
```

#### 可能的错误信息

| 错误码 | 错误信息 | 描述  |
| --- | ---- | --- |
| 400 | errors.missingShareCode | 缺少分享码参数 |
| 400 | errors.shareLinkNotFound | 分享链接不存在 |
| 400 | errors.shareLinkExpired | 分享链接已过期 |
| 400 | errors.shareLinkMaxUsesReached | 分享链接已达到最大使用次数 |
| 400 | errors.sourceUserNotFound | 分享链接的创建者不存在 |
| 400 | errors.cannotBoostYourself | 不能给自己助力 |
| 400 | errors.alreadyUsedShareLink | 已经使用过该分享链接 |
| 401 | errors.missingInitData | 缺少Telegram初始化数据 |
| 401 | errors.invalidTelegramData | 无效的Telegram数据 |
| 401 | errors.userDataNotFound | 未找到用户数据 |
| 401 | errors.invalidTelegramUserId | 无效的Telegram用户ID |
| 401 | errors.authenticationFailed | 认证失败 |
| 500 | errors.serverError | 服务器内部错误 |

### 业务逻辑说明


 1. 用户认证：接口通过Telegram WebApp的initData验证用户身份，不需要钱包认证。
 2. 
 3. 用户类型：
 4. 
 5. 已注册用户：系统会检查用户是否已经使用过该链接。
 6. 未注册用户：系统会基于Telegram ID检查是否已经使用过该链接。
 7. 
 8. 
 9. 奖励机制：
 10. 
 11. 根据分享链接对应的宝箱等级提供不同的奖励：
 12. 3级宝箱：提供基础的时间加速和宝石奖励
 13. 4级宝箱：提供更高的时间加速和宝石奖励
 14. 
 15. 
 16. 
 17. 
 18. 奖励发放：
 19. 
 20. 为分享链接创建者的宝箱倒计时提供加速
 21. 为分享链接创建者增加宝石奖励
 22. 记录助力关系和钱包历史

**每个Telegram用户只能使用同一个分享链接一次。**

**分享链接有使用次数限制和过期时间。**

**不能给自己助力。**

**接口状态**

> 开发中

**接口URL**

> api/telegram-share/boost

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "code":"719bdd4e-714f-45bb-997b-ca5cb3f23ba3",
    "initData":"user=%7B%22id%22%3A7339521783%2C%22first_name%22%3A%22joe%22%2C%22last_name%22%3A%22yongliang%22%2C%22language_code%22%3A%22zh-hans%22%7D&chat_instance=7093061138461021662&chat_type=sender&auth_date=1730952500&hash=fb892f3377db10445fb1a1660dcef6cbff48f101d3fbc87ee63cf95523f90a44"
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
	"ok": true,
	"data": {
		"boostMinutes": 60,
		"gemAmount": 50,
		"isPremium": false
	}
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 创建账单链接

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-04-01 15:23:41

> 更新时间: 2025-04-01 15:56:24

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> api/telegram-payment/create-star-invoice

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 我的宝箱记录

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-04-05 11:17:24

> 更新时间: 2025-04-05 11:23:24

## 宝箱记录 API

#### 获取用户宝箱记录

**接口路径：GET /api/chest/my-chest-records**

```
GET /api/chest/my-chest-records
```

**接口描述：获取用户的宝箱记录列表，包括宝箱ID、类型、来源、开启时间、奖励信息等。支持分页查询。,请求方法：GET,认证要求：需要钱包认证,请求参数：**

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| page | Number | 否 | 页码，默认为1 |
| limit | Number | 否 | 每页记录数，默认为20 |

**响应结果：**

```json
{
  "ok": true,
  "data": {
    "records": [
      {
        "id": 9,
        "type": "countdown",
        "source": null,
        "isOpened": true,
        "openedAt": "2025-04-05 11:06:41",
        "rewardInfo": {
          "items": [
            {
              "type": "fragment_green",
              "amount": 17
            },
            {
              "type": "fragment_blue",
              "amount": 1
            },
            {
              "type": "gem",
              "amount": 1949
            }
          ],
          "level": 1
        },
        "createdAt": "2025-04-05 11:06:41",
        "updatedAt": "2025-04-05 11:06:41"
      },
      {
        "id": 7,
        "type": "countdown",
        "source": null,
        "isOpened": true,
        "openedAt": "2025-03-27 17:50:07",
        "rewardInfo": null,
        "createdAt": "2025-03-27 17:50:07",
        "updatedAt": "2025-03-27 17:50:07"
      },
      {
        "id": 1,
        "type": "referral",
        "source": "normal_referral",
        "isOpened": true,
        "openedAt": "2025-03-27 17:49:49",
        "rewardInfo": null,
        "createdAt": "2025-03-27 17:46:04",
        "updatedAt": "2025-03-27 17:49:49"
      }
      // 更多记录...
    ],
    "total": 4,
    "page": 1,
    "limit": 20,
    "totalPages": 1
  },
  "message": "success.getChestRecords"
}
```

**响应字段说明：**

| 字段名 | 类型  | 描述  |
| --- | --- | --- |
| records | Array | 宝箱记录列表 |
| records[].id | Number | 宝箱记录ID |
| records[].type | String | 宝箱类型 |
| records[].source | String | 宝箱来源 |
| records[].isOpened | Boolean | 是否已开启 |
| records[].openedAt | String | 开启时间，日期字符串格式 |
| records[].rewardInfo | Object/null | 奖励信息，未开启或无奖励时为null |
| records[].rewardInfo.items | Array | 奖励物品列表 |
| records[].rewardInfo.items[].type | String | 奖励物品类型，如fragment_green、fragment_blue、gem等 |
| records[].rewardInfo.items[].amount | Number | 奖励物品数量 |
| records[].rewardInfo.level | Number | 奖励等级 |
| records[].createdAt | String | 创建时间，日期字符串格式 |
| records[].updatedAt | String | 更新时间，日期字符串格式 |
| total | Number | 记录总数 |
| page | Number | 当前页码 |
| limit | Number | 每页记录数 |
| totalPages | Number | 总页数 |

**错误响应：**

```json
{
  "ok": false,
  "message": "获取宝箱记录失败"
}
```

**错误码：**

| HTTP状态码 | 错误信息 | 描述  |
| ------- | ---- | --- |
| 400 | 获取宝箱记录失败 | 获取宝箱记录时发生错误 |
| 400 | 钱包ID不存在 | 用户钱包ID不存在 |
| 401 | 未授权 | 用户未登录或token无效 |

**接口状态**

> 开发中

**接口URL**

> api/chest/my-chest-records

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
	"ok": true,
	"data": {
		"records": [
			{
				"id": 9,
				"type": "countdown",
				"source": null,
				"isOpened": true,
				"openedAt": "2025-04-05 11:06:41",
				"rewardInfo": {
					"items": [
						{
							"type": "fragment_green",
							"amount": 17
						},
						{
							"type": "fragment_blue",
							"amount": 1
						},
						{
							"type": "gem",
							"amount": 1949
						}
					],
					"level": 1
				},
				"createdAt": "2025-04-05 11:06:41",
				"updatedAt": "2025-04-05 11:06:41"
			},
			{
				"id": 7,
				"type": "countdown",
				"source": null,
				"isOpened": true,
				"openedAt": "2025-03-27 17:50:07",
				"rewardInfo": null,
				"createdAt": "2025-03-27 17:50:07",
				"updatedAt": "2025-03-27 17:50:07"
			},
			{
				"id": 1,
				"type": "referral",
				"source": "normal_referral",
				"isOpened": true,
				"openedAt": "2025-03-27 17:49:49",
				"rewardInfo": null,
				"createdAt": "2025-03-27 17:46:04",
				"updatedAt": "2025-03-27 17:49:49"
			},
			{
				"id": 2,
				"type": "referral",
				"source": "normal_referral",
				"isOpened": true,
				"openedAt": "2025-03-27 17:49:49",
				"rewardInfo": null,
				"createdAt": "2025-03-27 17:46:08",
				"updatedAt": "2025-03-27 17:49:49"
			}
		],
		"total": 4,
		"page": 1,
		"limit": 20,
		"totalPages": 1
	},
	"message": "success.getChestRecords"
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 碎片制作门票

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-04-05 17:14:19

> 更新时间: 2025-04-06 15:16:56

## 碎片制作门票 API

### 接口信息


 - 路径: /api/fragment/craft-ticket
 - 方法: POST
 - 描述: 使用不同类型的碎片制作门票
 - 认证: 需要钱包认证，请求头中必须包含 Authorization: Bearer <token>

**认证: 需要钱包认证，请求头中必须包含 Authorization: Bearer <token>**

```
Authorization: Bearer <token>
```

### 请求参数

#### 请求头

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| Authorization | string | 是 | Bearer 认证令牌，格式为 Bearer <token> |

```
Bearer <token>
```

#### 请求体

```json
{
  "fragmentType": "fragment_green",
  "quantity": 1
}
```

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| fragmentType | string | 是 | 碎片类型，可选值：fragment_green, fragment_blue, fragment_purple, fragment_gold |
| quantity | integer | 是 | 要合成的数量或次数，必须大于等于1 |

```
undefined
```

### 碎片兑换比例

**不同类型的碎片可以兑换的门票数量不同：**

| 碎片类型 | 每组所需碎片数量 | 可兑换门票数量 |
| ---- | -------- | ------- |
| fragment_green | 160 | 1 |
| fragment_blue | 80 | 3 |
| fragment_purple | 40 | 15 |
| fragment_gold | 20 | 60 |

### 响应结果

#### 成功响应

```json
{
  "code": 0,
  "message": "成功使用碎片制作门票",
  "data": {
    "balance": {
      "ticket": 15,
      "fragment_green": 320,
      "fragment_blue": 160,
      "fragment_purple": 80,
      "fragment_gold": 40
    }
  }
}
```

| 字段  | 类型  | 描述  |
| --- | --- | --- |
| code | integer | 状态码，0表示成功 |
| message | string | 响应消息 |
| data.balance | object | 更新后的钱包余额信息 |
| data.balance.ticket | integer | 更新后的门票余额 |
| data.balance.fragment_green | integer | 更新后的绿色碎片余额 |
| data.balance.fragment_blue | integer | 更新后的蓝色碎片余额 |
| data.balance.fragment_purple | integer | 更新后的紫色碎片余额 |
| data.balance.fragment_gold | integer | 更新后的金色碎片余额 |

| 错误消息 | 描述  |
| ---- | --- |
| 参数验证失败 | 请求参数不符合要求 |
| 无效的碎片类型 | 提供的碎片类型不在支持范围内 |
| 数量必须为正数 | quantity参数必须大于0 |
| 碎片数量不足 | 用户钱包中没有足够的碎片进行兑换 |

### 示例

#### 请求示例

```bash
curl -X POST \
  https://api.example.com/api/fragment/craft-ticket \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "fragmentType": "fragment_purple",
    "quantity": 2
  }'
```

#### 响应示例

```json
{
  "code": 0,
  "message": "成功使用碎片制作门票",
  "data": {
    "balance": {
      "ticket": 30,
      "fragment_green": 320,
      "fragment_blue": 160,
      "fragment_purple": 0,
      "fragment_gold": 40
    }
  }
}
```

**接口状态**

> 开发中

**接口URL**

> api/fragment/craft-ticket

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "fragmentType": "fragment_green",
    "quantity": 1
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 转账免费门票

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-04-07 23:23:01

> 更新时间: 2025-04-07 23:42:08

## 免费门票转账 API

### 转账免费门票

#### 接口信息


 - 路径: /api/free-ticket/transfer
 - 方法: POST
 - 描述: 将免费门票从当前用户钱包转账给指定钱包地址
 - 认证: 需要钱包认证，请求头中必须包含 Authorization: Bearer <token>

**认证: 需要钱包认证，请求头中必须包含 Authorization: Bearer <token>**

```
Authorization: Bearer <token>
```

#### 请求参数

##### 请求头

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| Authorization | string | 是 | Bearer 认证token |

##### 请求体

```json
{
  "toWalletAddress": "EQA...",  // 接收方钱包地址
  "amount": 10                  // 转账数量
}
```

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| toWalletAddress | string | 是 | 接收方钱包地址 |
| amount | integer | 是 | 转账数量 |

#### 响应参数

##### 成功响应

```json
{
    "ok": true,
    "data": {
        "fromWalletId": 1,
        "toWalletAddress": "UQBhQxkk_fkznrqzwOAwrNJlHdp1el2f-AJoa1UBMUz-ERGb",
        "amount": 1,
        "timestamp": "2025-04-07T15:24:10.426Z"
    },
    "message": "Free tickets transferred successfully"
}
```

| 参数名 | 类型  | 描述  |
| --- | --- | --- |
| fromWalletId | integer | 发送方钱包ID |
| toWalletAddress | string | 接收方钱包地址 |
| amount | integer | 转账数量 |
| timestamp | string | 转账时间戳 |

**接口状态**

> 开发中

**接口URL**

> api/free-ticket/transfer

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "toWalletAddress":"UQBhQxkk_fkznrqzwOAwrNJlHdp1el2f-AJoa1UBMUz-ERGb",
    "amount":100000
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取剩余转账限额

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-04-07 23:26:00

> 更新时间: 2025-04-07 23:27:45

### 获取剩余转账限额

#### 接口信息


 - 路径: /api/free-ticket/remaining-limit
 - 方法: GET
 - 描述: 获取当前用户今日剩余可转账的免费门票数量
 - 认证: 需要钱包认证，请求头中必须包含 Authorization: Bearer <token>

**认证: 需要钱包认证，请求头中必须包含 Authorization: Bearer <token>**

```
Authorization: Bearer <token>
```

#### 请求参数

##### 请求头

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| Authorization | string | 是 | Bearer 认证token |

#### 响应参数

##### 成功响应

```json
{
    "ok": true,
    "data": {
        "remainingLimit": 5
    },
    "message": "Remaining transfer limit retrieved successfully"
}
```

| 参数名 | 类型  | 描述  |
| --- | --- | --- |
| remainingLimit | integer | 今日剩余可转账的免费门票数量 |

**接口状态**

> 开发中

**接口URL**

> api/free-ticket/remaining-limit

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 测试-重置宝箱倒计时

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-04-14 14:49:03

> 更新时间: 2025-04-15 16:17:54

#### 重置宝箱倒计时

**该接口用于将用户的宝箱倒计时重置为当前时间，使宝箱立即可领取，便于测试宝箱领取功能。,请求方法: POST,路径: /api/test-chest/reset-chest-countdown**

```
/api/test-chest/reset-chest-countdown
```

**请求头:**

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| Authorization | String | 是 | 用户认证令牌，格式为 Bearer {token} |

```
Bearer {token}
```

**请求参数: 无需额外参数,请求示例:**

```http
POST /api/test-chest/reset-chest-countdown HTTP/1.1
Host: api.example.com
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

**响应示例:**

```json
{
  "ok": true,
  "message": "宝箱倒计时已重置，现在可以立即领取",
  "data": {
    "userId": 123,
    "walletId": 456,
    "nextAvailableTime": "2023-06-15T08:30:00.000Z"
  }
}
```

**错误响应:**

```json
{
  "ok": false,
  "message": "未找到倒计时记录"
}
```

**接口状态**

> 开发中

**接口URL**

> /api/test-chest/reset-chest-countdown

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 测试-增加测试宝箱

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-04-14 14:50:54

> 更新时间: 2025-04-15 16:17:48

#### 2. 创建测试宝箱

**为当前用户创建指定数量和类型的测试宝箱。,请求方法: POST,路径: /api/test-chest/create-test-chests**

```
/api/test-chest/create-test-chests
```

**请求参数:**

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| count | Number | 否 | 要创建的宝箱数量，默认为1，最大为10 |
| type | String | 否 | 宝箱类型，比如推荐宝箱的类型是 referral |

**请求示例:**

```json
{
  "count": 3,
  "type": "referral"
}
```

**响应示例:**

```json
{
  "ok": true,
  "message": "已创建3个referral宝箱",
  "data": {
    "chests": [
      { "id": 101, "type": "referral" },
      { "id": 102, "type": "referral" },
      { "id": 103, "type": "referral" }
    ]
  }
}
```

**接口状态**

> 开发中

**接口URL**

> api/test-chest/create-test-chests

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "type":"referral"
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 测试-重置每日推荐宝箱

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-04-14 15:09:54

> 更新时间: 2025-04-15 17:33:04

#### 1. 重置每日推荐宝箱领取状态

**删除用户的UserDailyClaim记录，使用户可以再次领取每日推荐宝箱。,请求方法: POST,路径: /api/test-chest/reset-daily-chest-claim**

```
/api/test-chest/reset-daily-chest-claim
```

**请求参数:**

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| date | String | 否 | 要重置的特定日期，格式为YYYY-MM-DD，不提供则默认为今天 |

**请求示例:**

```json
{
  "date": "2023-06-15"
}
```

**或不指定日期（重置今天的记录）:**

```json
{}
```

**响应示例:**

```json
{
  "ok": true,
  "message": "每日推荐宝箱领取状态已重置，现在可以再次领取",
  "data": {
    "userId": 123,
    "walletId": 456,
    "deletedRecords": 1
  }
}
```

**接口状态**

> 开发中

**接口URL**

> api/test-chest/reset-daily-chest-claim

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 测试-手动修改推荐人数

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-04-14 15:14:31

> 更新时间: 2025-04-15 16:17:36

#### 2. 直接设置用户referralCount字段值

**用于测试不同邀请数量下的奖励机制，直接设置用户的referralCount值。,请求方法: POST,路径: /api/test-chest/reset-referral-count**

```
/api/test-chest/reset-referral-count
```

**请求参数:**

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| value | Number | 否 | 要设置的邀请数量值，默认为0 |

**请求示例:**

```json
{
  "value": 5
}
```

**响应示例:**

```json
{
  "ok": true,
  "message": "成功设置邀请数量为5",
  "data": {
    "userId": 123,
    "previousCount": 10,
    "newCount": 5
  }
}
```

**接口状态**

> 开发中

**接口URL**

> /api/test-chest/reset-referral-count

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "value":10    
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 测试-重置任务完成状态

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-04-14 15:46:58

> 更新时间: 2025-04-15 16:17:28

#### 重置任务完成状态

**该接口用于删除用户的任务完成记录，使用户可以再次完成已完成的任务，便于测试任务完成和奖励机制。,请求方法: POST,路径: /api/test-chest/reset-task-complete**

```
/api/test-chest/reset-task-complete
```

**请求头:**

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| Authorization | String | 是 | 用户认证令牌，格式为 Bearer {token} |

```
Bearer {token}
```

**请求参数:**

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| taskId | Number | 否 | 要重置的特定任务ID。如不提供，将重置所有任务的完成状态 |

**请求示例:**

```http
POST /api/test-chest/reset-task-complete HTTP/1.1
Host: api.example.com
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "taskId": 3
}
```

**重置所有任务示例:**

```http
POST /api/test-chest/reset-task-complete HTTP/1.1
Host: api.example.com
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

**响应示例 (重置特定任务):**

```json
{
  "ok": true,
  "message": "任务#3完成状态已重置",
  "data": {
    "userId": 123,
    "walletId": 456,
    "taskId": 3,
    "deletedRecords": 1
  }
}
```

**响应示例 (重置所有任务):**

```json
{
  "ok": true,
  "message": "所有任务完成状态已重置",
  "data": {
    "userId": 123,
    "walletId": 456,
    "taskId": "all",
    "deletedRecords": 5
  }
}
```

**错误响应:**

```json
{
  "ok": false,
  "message": "服务器内部错误"
}
```

**接口状态**

> 开发中

**接口URL**

> /api/test-chest/reset-task-complete

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 一次性领取4个宝箱

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-04-15 11:58:25

> 更新时间: 2025-04-15 12:20:06

#### 一次性领取4个宝箱

**该接口允许用户一次性领取4个宝箱，每个用户只能使用一次。系统会检查用户是否已经领取过这些宝箱，如果已经领取过，则会返回错误信息。,请求方法: POST,路径: /api/jackpot-chest/collect-four-chests**

```
/api/jackpot-chest/collect-four-chests
```

**请求头:**

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| Authorization | String | 是 | 用户认证令牌，格式为 Bearer {token} |

```
Bearer {token}
```

**请求参数: 无需额外参数,响应示例:,成功响应:**

```json
{
  "ok": true,
  "data": {
    "result": {
      "openedCount": 4,
      "chestIds": [40, 41, 42, 43],
      "rewards": [
        {
          "level": 1,
          "items": [
            {
              "type": "fragment_green",
              "amount": 18
            },
            {
              "type": "gem",
              "amount": 4376
            }
          ]
        },
        {
          "level": 1,
          "items": [
            {
              "type": "fragment_green",
              "amount": 24
            },
            {
              "type": "gem",
              "amount": 4363
            }
          ]
        },
        {
          "level": 1,
          "items": [
            {
              "type": "fragment_green",
              "amount": 18
            },
            {
              "type": "gem",
              "amount": 4996
            }
          ]
        },
        {
          "level": 1,
          "items": [
            {
              "type": "fragment_green",
              "amount": 21
            },
            {
              "type": "gem",
              "amount": 1356
            }
          ]
        }
      ],
      "summary": {
        "ticket": 0,
        "fragment_green": 81,
        "fragment_blue": 0,
        "fragment_purple": 0,
        "fragment_gold": 0,
        "ton": 0,
        "gem": 15091
      },
      "levelSummary": {
        "level1": 4,
        "level2": 0,
        "level3": 0,
        "level4": 0
      },
      "shareLinks": [],
      "jackpotWinner": null
    }
  }
}
```

**接口状态**

> 开发中

**接口URL**

> /api/jackpot-chest/collect-four-chests

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取四个宝箱领取状态

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-04-15 12:21:16

> 更新时间: 2025-04-15 12:22:48

## 获取四个宝箱领取状态 API

### 接口说明

**该接口用于获取用户是否已经领取过四个宝箱的状态。**

### 请求信息


 - 接口路径: /api/jackpot-chest/four-chests-status
 - 请求方法: GET
 - 认证要求: 需要钱包认证

**认证要求: 需要钱包认证**

### 请求参数

**无需请求参数**

### 响应数据

#### 成功响应

```json
{
  "ok": true,
  "data": {
    "hasCollected": true  // 布尔值，表示用户是否已经领取过四个宝箱
  }
}
```

#### 响应字段说明

| 字段名 | 类型  | 描述  |
| --- | --- | --- |
| hasCollected | boolean | 用户是否已经领取过四个宝箱 |

**接口状态**

> 开发中

**接口URL**

> /api/jackpot-chest/four-chests-status

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 测试-重置一次性4个宝箱领取状态

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-04-15 12:25:02

> 更新时间: 2025-04-15 16:17:19

## 重置一次性4个宝箱领取状态 API

### 接口说明

**该接口用于重置用户的一次性4个宝箱领取状态，主要用于测试目的。重置后，用户可以重新领取一次性4个宝箱奖励。**

### 接口信息


 - 接口路径: /api/test-chest/reset-four-chests-collect
 - 请求方法: POST
 - 需要认证: 是（需要钱包认证中间件）

**需要认证: 是（需要钱包认证中间件）**

### 请求头

| 参数名 | 必选  | 类型  | 说明  |
| --- | --- | --- | --- |
| Authorization | 是 | string | 钱包认证token |

### 请求参数

**无需请求参数**

### 响应参数

#### 成功响应

```json
{
  "ok": true,
  "message": "一次性4个宝箱领取状态已重置"
}
```

**接口状态**

> 开发中

**接口URL**

> /api/test-chest/reset-four-chests-collect

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 测试-设置奖池金额

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-04-15 16:11:12

> 更新时间: 2025-04-15 16:42:45

### 设置奖池金额

#### 接口说明


 - 接口路径：/api/test-chest/set-jackpot-pool-amount
 - 请求方式：POST
 - 功能描述：设置指定等级奖池的金额，用于测试奖池相关功能

**功能描述：设置指定等级奖池的金额，用于测试奖池相关功能**

#### 请求参数

```json
{
  "level": "number",  // 奖池等级，1-3
  "amount": "number"  // 要设置的奖池金额
}
```

#### 响应格式

```json
{
    "ok": true,
    "message": "1级奖池金额已设置为2",
    "data": {
        "level": 1,
        "currentAmount": 2,
        "targetAmount": 10,
        "newUserAmount": 2,
        "lastWinnerId": null,
        "lastWinnerWalletId": null,
        "lastWinTime": null
    }
}
```

**接口状态**

> 开发中

**接口URL**

> /api/test-chest/set-jackpot-pool-amount

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "level":1,
    "amount":2
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 测试-重置所有奖池金额为0

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-04-15 16:16:34

> 更新时间: 2025-04-15 16:43:09

### 重置所有奖池金额

#### 接口说明


 - 接口路径：/api/test-chest/reset-all-jackpot-pools
 - 请求方式：POST
 - 功能描述：将所有等级的奖池金额重置为0，用于测试场景重置

**功能描述：将所有等级的奖池金额重置为0，用于测试场景重置**

#### 请求参数

**无需请求参数**

#### 响应格式

```json
{
    "ok": true,
    "message": "已重置所有奖池金额为0",
    "data": {
        "resetResults": [
            {
                "level": 1,
                "newUserAmount": 0,
                "currentAmount": 0,
                "lastWinnerId": null,
                "lastWinnerWalletId": null,
                "lastWinTime": null
            },
            {
                "level": 2,
                "currentAmount": 0,
                "newUserAmount": 0,
                "chestOpenAmount": 0,
                "lastWinnerId": null,
                "lastWinnerWalletId": null,
                "lastWinTime": null
            },
            {
                "level": 3,
                "currentAmount": 0,
                "newUserAmount": 0,
                "chestOpenAmount": 0,
                "lastWinnerId": null,
                "lastWinnerWalletId": null,
                "lastWinTime": null
            }
        ]
    }
}
```

**接口状态**

> 开发中

**接口URL**

> /api/test-chest/reset-all-jackpot-pools

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 提现ton

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-04-21 14:19:43

> 更新时间: 2025-04-21 14:47:57

## TON提现 API

### 提现TON代币

#### 接口信息


 - 路径: /api/withdrawal/ton
 - 方法: POST
 - 描述: 将用户钱包中的TON代币提现到自己的钱包地址
 - 认证: 需要钱包认证，请求头中必须包含 Authorization: Bearer <token>

**认证: 需要钱包认证，请求头中必须包含 Authorization: Bearer <token>**

```
Authorization: Bearer <token>
```

#### 请求参数

##### 请求头

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| Authorization | string | 是 | Bearer 认证token |

##### 请求体

```json
{
  "amount": 1.5  // 提现金额，最小值为1
}
```

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| amount | number | 是 | 提现金额，必须大于等于1 |

#### 响应参数

##### 成功响应

```json
{
  "ok": true,
  "data": {
    "amount": 1,
    "fee": 0.1,
    "status": "approved",
    "needManualApprove": false
  },
  "message": "success.withdrawTONSuccess"
}
```

| 参数名 | 类型  | 描述  |
| --- | --- | --- |
| amount | number | 提现金额 |
| fee | number | 提现手续费 |
| status | string | 提现状态，可能值："pending"(处理中)、"approved"(已批准)、"failed"(失败) |
| needManualApprove | boolean | 是否需要人工审核 |

**接口状态**

> 开发中

**接口URL**

> /api/withdrawal/ton

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "amount":1
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# gem 排行榜

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-04-22 10:52:58

> 更新时间: 2025-04-22 12:09:10

## Gem排行榜 API

#### 获取Gem排行榜数据

**接口路径：GET /api/gem-leaderboard**

```
GET /api/gem-leaderboard
```

**接口描述：获取基于gem数量的用户排行榜数据，包括排名、用户信息和gem数量，同时返回当前用户的排名信息。,请求方法：GET,认证要求：需要钱包认证,请求参数：**

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| limit | Number | 否 | 限制返回的记录数量，默认值为100 |
| offset | Number | 否 | 分页偏移量，默认值为0 |

**响应结果：**

```json
{
    "ok": true,
    "data": {
        "leaderboard": [
            {
                "userId": 1,
                "walletId": 1,
                "walletAddress": "0QDioPTSTodOvpJe5czY963Jry4QilH3tMBzZn-1vFbhNmLO",
                "username": "Zoyonlian",
                "photoUrl": "",
                "gemAmount": 486062,
                "rank": 1
            },
            {
                "userId": 8,
                "walletId": 8,
                "walletAddress": "UQBww7-y-oc8yLVN49_TxmHE8rcWPYryoiyEBJx2y0rTKqq5",
                "username": "felerford",
                "photoUrl": "",
                "gemAmount": 8,
                "rank": 2
            }
        ],
        "pagination": {
            "total": 11,
            "limit": 2,
            "offset": 0,
            "hasMore": true
        },
        "userInfo": {
            "rank": 5,
            "gemAmount": 6
        }
    }
}
```

**响应字段说明：**

| 字段名 | 类型  | 描述  |
| --- | --- | --- |
| leaderboard | Array | 排行榜数据数组 |
| leaderboard[].userId | Number | 用户ID |
| leaderboard[].walletId | Number | 钱包ID |
| leaderboard[].walletAddress | String | 钱包地址 |
| leaderboard[].username | String | 用户名 |
| leaderboard[].photoUrl | String | 用户头像URL |
| leaderboard[].gemAmount | Number | 用户拥有的gem数量 |
| leaderboard[].rank | Number | 用户在排行榜中的排名 |
| pagination | Object | 分页信息 |
| pagination.total | Number | 总记录数 |
| pagination.limit | Number | 每页记录数 |
| pagination.offset | Number | 当前偏移量 |
| pagination.hasMore | Boolean | 是否还有更多数据 |
| userInfo | Object | 当前用户信息 |
| userInfo.rank | Number | 当前用户的排名 |
| userInfo.gemAmount | Number | 当前用户的gem数量 |

**业务逻辑说明：**


 1. 排行榜按照用户gem数量降序排列
 2. 只有gem数量大于0的用户才会出现在排行榜中
 3. 分页参数limit和offset用于控制返回的数据量，适合移动端分页加载
 4. 返回的userInfo包含当前登录用户的排名和gem数量，方便前端展示用户在排行榜中的位置
 5. 排名从1开始，数值越小表示排名越高

**接口状态**

> 开发中

**接口URL**

> /api/gem-leaderboard/?limit=2&offset=0

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| limit | 2 | string | 是 | - |
| offset | 0 | string | 是 | - |

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
	"ok": true,
	"data": {
		"leaderboard": [
			{
				"userId": 1,
				"walletId": 1,
				"walletAddress": "0QDioPTSTodOvpJe5czY963Jry4QilH3tMBzZn-1vFbhNmLO",
				"username": "Zoyonlian",
				"photoUrl": "",
				"gemAmount": 486062,
				"rank": 1
			},
			{
				"userId": 8,
				"walletId": 8,
				"walletAddress": "UQBww7-y-oc8yLVN49_TxmHE8rcWPYryoiyEBJx2y0rTKqq5",
				"username": "felerford",
				"photoUrl": "",
				"gemAmount": 8,
				"rank": 2
			}
		],
		"pagination": {
			"total": 11,
			"limit": 2,
			"offset": 0,
			"hasMore": true
		},
		"userInfo": {
			"rank": 5,
			"gemAmount": 6
		}
	}
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 加速宝箱倒计时

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-04-24 07:43:06

> 更新时间: 2025-04-24 08:25:16

## 宝箱加速 API 文档

### 概述

**该API允许用户手动加速24小时宝箱的倒计时。用户可以通过此功能减少等待时间，更快地领取宝箱奖励。**

### 使用限制


 - 单次加速最多10秒
 - 每天累计最多加速2小时（7200秒）
 - 需要用户登录并通过钱包认证

### API 端点

#### 1. 加速宝箱倒计时

**请求**

```
POST /api/jackpot-chest/accelerate
```

**请求头**

```
Authorization: Bearer {token}
```

**请求体**

```json
{
  "seconds": 10  // 要加速的秒数，最大值为10
}
```

**响应**

```json
{
  "ok": true,
  "message": "宝箱倒计时已加速",
  "data": {
    "acceleratedSeconds": 10,  // 实际加速的秒数
    "newNextAvailableTime": "2023-05-15T10:30:00Z",  // 新的可领取时间
    "totalAcceleratedToday": 120,  // 今日已使用的总加速秒数
    "remainingAccelerationSeconds": 7080  // 今日剩余可用加速秒数
  }
}
```

#### 2. 获取用户的宝箱加速状态

**请求**

```
GET /api/jackpot-chest/acceleration-status
```

**请求头**

```
Authorization: Bearer {token}
```

**响应**

```json
{
  "ok": true,
  "data": {
    "usedAccelerationSeconds": 120,  // 今日已使用的加速秒数
    "remainingAccelerationSeconds": 7080,  // 今日剩余可用加速秒数
    "maxAccelerationPerDay": 7200,  // 每天最大加速秒数
    "maxAccelerationPerTime": 10  // 单次最大加速秒数
  }
}
```

### 注意事项


 - 加速操作会立即生效，减少宝箱的倒计时
 - 加速限制会在每天0点重置

**接口状态**

> 开发中

**接口URL**

> api/jackpot-chest/accelerate

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "seconds":10
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
	"ok": true,
	"message": "Chest countdown accelerated successfully",
	"data": {
		"acceleratedSeconds": 10,
		"newNextAvailableTime": "2025-04-25T00:08:26.000Z",
		"totalAcceleratedToday": 51,
		"remainingAccelerationSeconds": 7149
	}
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 生成签名消息

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-05-07 14:33:03

> 更新时间: 2025-05-14 15:06:32

## Web3钱包获取Nonce接口

### 接口说明

**获取用于Web3钱包登录的nonce值，该nonce将用于生成签名消息。**

### 请求信息


 - 路径: /api/web3-auth/nonce
 - 方法: POST
 - Content-Type: application/json

**Content-Type: application/json**

### 请求参数

#### Body参数

```json
{
  "walletAddress": "string" // 钱包地址（必填）
}
```

### 响应结果

#### 成功响应

```json
{
  "ok": true,
  "data": {
    "nonce": "string", // 随机生成的nonce值
    "message": "string" // 用于签名的消息内容
  }
}
```

### 注意事项


 1. nonce值有效期为5分钟
 2. 每次请求都会生成新的nonce值
 3. 获取到的message需要使用钱包进行签名，用于后续的登录请求

### 示例

#### 请求示例

```bash
curl -X POST http://your-domain/api/web3-auth/nonce \
  -H "Content-Type: application/json" \
  -d '{"walletAddress": "0x1234...5678"}'
```

#### 响应示例

```json
{
  "ok": true,
  "data": {
    "nonce": "0ef99695-daaf-4f59-87d6-f5f2c0170423",
    "message": "Welcome to MooFun!\n\nPlease sign to verify your wallet address, after which you can begin your wonderful journey.\n\nSecurity code: 0ef99695-daaf-4f59-87d6-f5f2c0170423\nTimestamp: 1746599675551"
  }
}
```

**接口状态**

> 开发中

**接口URL**

> api/web3-auth/nonce

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "walletAddress":"******************************************"
}
```

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# kaia登录注册

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-05-07 14:38:59

> 更新时间: 2025-05-20 14:53:32

## Web3钱包登录接口

### 接口信息


 - 路径: /api/web3-auth/login
 - 方法: POST
 - 认证要求: 无需认证

**认证要求: 无需认证**

### 请求参数

#### 请求体 (JSON)

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| walletAddress | string | 是 | 用户的钱包地址 |
| signature | string | 是 | 签名数据 |
| message | string | 是 | 签名消息（从/api/web3-auth/nonce接口获取） |
| referralCode | string | 否 | 推荐码 |

#### 请求体示例

```json
{
  "walletAddress": "0x1234...5678",
  "signature": "0xabcd...efgh", 
  "message": "Sign this message for authentication: 123e4567-e89b-12d3-a456-426614174000",
  "referralCode": "USER123"
}
```

### 响应结果

#### 成功响应


 - 状态码: 200 OK
 - 响应体:

```json
{
  "ok": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIs...",
    "user": {
      "id": 12,
      "username": "wallet_0xa1ca25",
      "walletAddress": "******************************************"
    }
  }
}
```

#### 响应字段说明

| 字段名 | 类型  | 描述  |
| --- | --- | --- |
| token | string | JWT认证令牌 |
| user.id | number | 用户ID |
| user.username | string | 用户名 |
| user.walletAddress | string | 用户钱包地址 |

### 注意事项


 1. 在调用此接口前，需要先调用 /api/web3-auth/nonce 接口获取签名消息
 2. 签名消息的有效期为5分钟
 3. 登录成功后返回的token需要在后续请求中通过Authorization头部传递，格式为：Bearer {token}

**登录成功后返回的token需要在后续请求中通过Authorization头部传递，格式为：Bearer {token}**

```
Bearer {token}
```

**接口状态**

> 开发中

**接口URL**

> api/web3-auth/login

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "walletAddress": "******************************************",
    "signature": "{{signature}}",
    "message":"{{message}}"
}
```

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取用户的所有牧场区

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-05-10 07:48:58

> 更新时间: 2025-05-14 16:25:03

## 牧场区列表获取 API 文档

**本文档描述了获取用户牧场区列表的API接口。通过该接口，客户端可以获取用户所有牧场区的详细信息，包括解锁状态、等级、牛舍数量、产量等数据。**

### 基本信息


 - 基础路径: /api/farm/farm-plots
 - 所有接口都需要用户身份验证（钱包认证）

**所有接口都需要用户身份验证（钱包认证）**

### API 接口

#### 获取用户的牧场区列表

**获取当前用户拥有的所有牧场区信息，包括已解锁和未解锁的牧场区。,请求方法: GET,路径: /api/farm/farm-plots**

```
/api/farm/farm-plots
```

**请求参数: 无需额外参数,认证要求: 需要钱包认证,响应字段说明:**

| 字段名 | 类型  | 描述  |
| --- | --- | --- |
| id | Number | 牧场区记录ID |
| walletId | Number | 用户钱包ID |
| plotNumber | Number | 牧场区编号（1-20） |
| level | Number | 牧场区等级（1-20） |
| barnCount | Number | 牛舍数量 |
| milkProduction | Number | 每次产出获得的牛奶量 |
| productionSpeed | Number | 每次产出所需时间（秒） |
| unlockCost | Number | 解锁费用 |
| upgradeCost | Number | 升级费用 |
| lastProductionTime | Date | 上次产出时间 |
| isUnlocked | Boolean | 是否已解锁 |
| accumulatedMilk | Number | 累积的牛奶量 |
| createdAt | Date | 记录创建时间 |
| updatedAt | Date | 记录更新时间 |
| baseProduction | Number | 计算的基础产量 |
| singleBarnProductionPerSecond | Number | 单个牛舍每秒产量 |
| totalProductionPerSecond | Number | 总产量（每秒） |
| speedGrowthPercentage | Number | 速度增长百分比 |

**计算字段说明:**


 - baseProduction: 基础产量 = 1 × (1.7)^(编号-1) × (1.5)^(等级-1)
 - singleBarnProductionPerSecond: 单个牛舍每秒产量 = 基础产量 / 产出时间
 - totalProductionPerSecond: 总产量（每秒）= 单个牛舍每秒产量 × 牛舍数量
 - speedGrowthPercentage: 速度增长百分比 = 100 + (等级-1) × 5，每升一级提升5%速度

**注意事项:**


 1. 接口会自动初始化新用户的牧场区（如果用户没有牧场区记录）
 2. 对于未解锁的牧场区，只返回基本信息，不包含计算字段

**响应示例:**

```json
{
    "ok": true,
    "data": {
        "farmPlots": [
            {
                "id": 61,
                "walletId": 24,
                "plotNumber": 1,
                "level": 1,
                "barnCount": 1,
                "milkProduction": 1,
                "productionSpeed": 5,
                "unlockCost": 0,
                "upgradeCost": 300,
                "lastProductionTime": "2025-05-14T08:18:42.306Z",
                "isUnlocked": true,
                "accumulatedMilk": 0,
                "createdAt": "2025-05-14 16:18:42",
                "updatedAt": "2025-05-14T08:18:42.307Z",
                "baseProduction": 1,
                "singleBarnProductionPerSecond": 0.2,
                "totalProductionPerSecond": 0.2,
                "speedGrowthPercentage": 100
            },
            {
                "id": 62,
                "walletId": 24,
                "plotNumber": 2,
                "level": 0,
                "barnCount": 0,
                "milkProduction": 0,
                "productionSpeed": 0,
                "unlockCost": 200,
                "upgradeCost": 0,
                "lastProductionTime": "2025-05-14 16:18:42",
                "isUnlocked": false,
                "accumulatedMilk": 0,
                "createdAt": "2025-05-14 16:18:42",
                "updatedAt": "2025-05-14 16:18:42"
            },
            {
                "id": 63,
                "walletId": 24,
                "plotNumber": 3,
                "level": 0,
                "barnCount": 0,
                "milkProduction": 0,
                "productionSpeed": 0,
                "unlockCost": 400,
                "upgradeCost": 0,
                "lastProductionTime": "2025-05-14 16:18:42",
                "isUnlocked": false,
                "accumulatedMilk": 0,
                "createdAt": "2025-05-14 16:18:42",
                "updatedAt": "2025-05-14 16:18:42"
            },
            {
                "id": 64,
                "walletId": 24,
                "plotNumber": 4,
                "level": 0,
                "barnCount": 0,
                "milkProduction": 0,
                "productionSpeed": 0,
                "unlockCost": 800,
                "upgradeCost": 0,
                "lastProductionTime": "2025-05-14 16:18:42",
                "isUnlocked": false,
                "accumulatedMilk": 0,
                "createdAt": "2025-05-14 16:18:42",
                "updatedAt": "2025-05-14 16:18:42"
            },
            {
                "id": 65,
                "walletId": 24,
                "plotNumber": 5,
                "level": 0,
                "barnCount": 0,
                "milkProduction": 0,
                "productionSpeed": 0,
                "unlockCost": 1600,
                "upgradeCost": 0,
                "lastProductionTime": "2025-05-14 16:18:42",
                "isUnlocked": false,
                "accumulatedMilk": 0,
                "createdAt": "2025-05-14 16:18:42",
                "updatedAt": "2025-05-14 16:18:42"
            },
            {
                "id": 66,
                "walletId": 24,
                "plotNumber": 6,
                "level": 0,
                "barnCount": 0,
                "milkProduction": 0,
                "productionSpeed": 0,
                "unlockCost": 3200,
                "upgradeCost": 0,
                "lastProductionTime": "2025-05-14 16:18:42",
                "isUnlocked": false,
                "accumulatedMilk": 0,
                "createdAt": "2025-05-14 16:18:42",
                "updatedAt": "2025-05-14 16:18:42"
            },
            {
                "id": 67,
                "walletId": 24,
                "plotNumber": 7,
                "level": 0,
                "barnCount": 0,
                "milkProduction": 0,
                "productionSpeed": 0,
                "unlockCost": 6400,
                "upgradeCost": 0,
                "lastProductionTime": "2025-05-14 16:18:42",
                "isUnlocked": false,
                "accumulatedMilk": 0,
                "createdAt": "2025-05-14 16:18:42",
                "updatedAt": "2025-05-14 16:18:42"
            },
            {
                "id": 68,
                "walletId": 24,
                "plotNumber": 8,
                "level": 0,
                "barnCount": 0,
                "milkProduction": 0,
                "productionSpeed": 0,
                "unlockCost": 12800,
                "upgradeCost": 0,
                "lastProductionTime": "2025-05-14 16:18:42",
                "isUnlocked": false,
                "accumulatedMilk": 0,
                "createdAt": "2025-05-14 16:18:42",
                "updatedAt": "2025-05-14 16:18:42"
            },
            {
                "id": 69,
                "walletId": 24,
                "plotNumber": 9,
                "level": 0,
                "barnCount": 0,
                "milkProduction": 0,
                "productionSpeed": 0,
                "unlockCost": 25600,
                "upgradeCost": 0,
                "lastProductionTime": "2025-05-14 16:18:42",
                "isUnlocked": false,
                "accumulatedMilk": 0,
                "createdAt": "2025-05-14 16:18:42",
                "updatedAt": "2025-05-14 16:18:42"
            },
            {
                "id": 70,
                "walletId": 24,
                "plotNumber": 10,
                "level": 0,
                "barnCount": 0,
                "milkProduction": 0,
                "productionSpeed": 0,
                "unlockCost": 51200,
                "upgradeCost": 0,
                "lastProductionTime": "2025-05-14 16:18:42",
                "isUnlocked": false,
                "accumulatedMilk": 0,
                "createdAt": "2025-05-14 16:18:42",
                "updatedAt": "2025-05-14 16:18:42"
            },
            {
                "id": 71,
                "walletId": 24,
                "plotNumber": 11,
                "level": 0,
                "barnCount": 0,
                "milkProduction": 0,
                "productionSpeed": 0,
                "unlockCost": 102400,
                "upgradeCost": 0,
                "lastProductionTime": "2025-05-14 16:18:42",
                "isUnlocked": false,
                "accumulatedMilk": 0,
                "createdAt": "2025-05-14 16:18:42",
                "updatedAt": "2025-05-14 16:18:42"
            },
            {
                "id": 72,
                "walletId": 24,
                "plotNumber": 12,
                "level": 0,
                "barnCount": 0,
                "milkProduction": 0,
                "productionSpeed": 0,
                "unlockCost": 204800,
                "upgradeCost": 0,
                "lastProductionTime": "2025-05-14 16:18:42",
                "isUnlocked": false,
                "accumulatedMilk": 0,
                "createdAt": "2025-05-14 16:18:42",
                "updatedAt": "2025-05-14 16:18:42"
            },
            {
                "id": 73,
                "walletId": 24,
                "plotNumber": 13,
                "level": 0,
                "barnCount": 0,
                "milkProduction": 0,
                "productionSpeed": 0,
                "unlockCost": 409600,
                "upgradeCost": 0,
                "lastProductionTime": "2025-05-14 16:18:42",
                "isUnlocked": false,
                "accumulatedMilk": 0,
                "createdAt": "2025-05-14 16:18:42",
                "updatedAt": "2025-05-14 16:18:42"
            },
            {
                "id": 74,
                "walletId": 24,
                "plotNumber": 14,
                "level": 0,
                "barnCount": 0,
                "milkProduction": 0,
                "productionSpeed": 0,
                "unlockCost": 819200,
                "upgradeCost": 0,
                "lastProductionTime": "2025-05-14 16:18:42",
                "isUnlocked": false,
                "accumulatedMilk": 0,
                "createdAt": "2025-05-14 16:18:42",
                "updatedAt": "2025-05-14 16:18:42"
            },
            {
                "id": 75,
                "walletId": 24,
                "plotNumber": 15,
                "level": 0,
                "barnCount": 0,
                "milkProduction": 0,
                "productionSpeed": 0,
                "unlockCost": 1638400,
                "upgradeCost": 0,
                "lastProductionTime": "2025-05-14 16:18:42",
                "isUnlocked": false,
                "accumulatedMilk": 0,
                "createdAt": "2025-05-14 16:18:42",
                "updatedAt": "2025-05-14 16:18:42"
            },
            {
                "id": 76,
                "walletId": 24,
                "plotNumber": 16,
                "level": 0,
                "barnCount": 0,
                "milkProduction": 0,
                "productionSpeed": 0,
                "unlockCost": 3276800,
                "upgradeCost": 0,
                "lastProductionTime": "2025-05-14 16:18:42",
                "isUnlocked": false,
                "accumulatedMilk": 0,
                "createdAt": "2025-05-14 16:18:42",
                "updatedAt": "2025-05-14 16:18:42"
            },
            {
                "id": 77,
                "walletId": 24,
                "plotNumber": 17,
                "level": 0,
                "barnCount": 0,
                "milkProduction": 0,
                "productionSpeed": 0,
                "unlockCost": 6553600,
                "upgradeCost": 0,
                "lastProductionTime": "2025-05-14 16:18:42",
                "isUnlocked": false,
                "accumulatedMilk": 0,
                "createdAt": "2025-05-14 16:18:42",
                "updatedAt": "2025-05-14 16:18:42"
            },
            {
                "id": 78,
                "walletId": 24,
                "plotNumber": 18,
                "level": 0,
                "barnCount": 0,
                "milkProduction": 0,
                "productionSpeed": 0,
                "unlockCost": 13107200,
                "upgradeCost": 0,
                "lastProductionTime": "2025-05-14 16:18:42",
                "isUnlocked": false,
                "accumulatedMilk": 0,
                "createdAt": "2025-05-14 16:18:42",
                "updatedAt": "2025-05-14 16:18:42"
            },
            {
                "id": 79,
                "walletId": 24,
                "plotNumber": 19,
                "level": 0,
                "barnCount": 0,
                "milkProduction": 0,
                "productionSpeed": 0,
                "unlockCost": 26214400,
                "upgradeCost": 0,
                "lastProductionTime": "2025-05-14 16:18:42",
                "isUnlocked": false,
                "accumulatedMilk": 0,
                "createdAt": "2025-05-14 16:18:42",
                "updatedAt": "2025-05-14 16:18:42"
            },
            {
                "id": 80,
                "walletId": 24,
                "plotNumber": 20,
                "level": 0,
                "barnCount": 0,
                "milkProduction": 0,
                "productionSpeed": 0,
                "unlockCost": 52428800,
                "upgradeCost": 0,
                "lastProductionTime": "2025-05-14 16:18:42",
                "isUnlocked": false,
                "accumulatedMilk": 0,
                "createdAt": "2025-05-14 16:18:42",
                "updatedAt": "2025-05-14 16:18:42"
            }
        ]
    }
}
```

**接口状态**

> 开发中

**接口URL**

> api/farm/farm-plots

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 升级牧场区

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-05-10 15:50:47

> 更新时间: 2025-05-14 16:31:17

## 接口文档：升级牧场区

### 1. 概述

**该接口用于升级用户指定的牧场区。升级会消耗用户的 GEM，并提升牧场区的等级、牛舍数量、牛奶产量等属性。**

### 2. 请求


 - URL: /api/farm-plots/upgrade
 - 方法: POST
 - 认证: 需要用户认证

```
POST
```

**认证: 需要用户认证**

### 3. 请求体 (Request Body)


 - Content-Type: application/json

```
application/json
```

| 参数名 | 类型  | 是否必需 | 描述  | 示例  |
| --- | --- | ---- | --- | --- |
| plotNumber | number | 是 | 要升级的牧场区编号 | 2 |

```
undefined
```

**示例请求体:**

```json
{
  "plotNumber": 2
}
```

### 4. 响应 (Response)

#### 4.1. 成功响应


 - 状态码: 200 OK
 - Content-Type: application/json

**Content-Type: application/json**

```
application/json
```

**响应体结构:**

```json
{
  "success": true,
  "data": {
    "farmPlot": {
      "id": "integer",
      "walletId": "integer",
      "plotNumber": "integer",
      "level": "integer",
      "barnCount": "integer",
      "milkProduction": "number", // 单次产奶量
      "productionSpeed": "number", // 生产速度 (秒/次)
      "unlockCost": "number",
      "upgradeCost": "number", // 下一级升级所需GEM
      "lastProductionTime": "string(date-time)",
      "isUnlocked": "boolean",
      "createdAt": "string(date-time)",
      "updatedAt": "string(date-time)",
      "baseProduction": "number", // 单个牛舍的基础产量 (每次生产)
      "singleBarnProductionPerSecond": "number", // 单个牛舍每秒产量
      "totalProductionPerSecond": "number", // 该牧场区总每秒产量
      "speedGrowthPercentage": "number", // 速度增长百分比 (相对于初始速度)
      "productionGrowthPercentage": "number" // 产量增长百分比 (相对于初始产量)
    }
  },
  "message": "请求成功"
}
```

**farmPlot 对象字段说明:**

```
farmPlot
```


 - id: 牧场区记录的唯一ID。
 - walletId: 用户钱包ID。
 - plotNumber: 牧场区编号。
 - level: 升级后的牧场区等级。
 - barnCount: 升级后的牛舍数量。
 - milkProduction: 升级后，牧场区单次生产的牛奶量。
 - productionSpeed: 升级后，牧场区的生产速度（单位：秒/次）。
 - unlockCost: 解锁该牧场区所需的GEM (对于已解锁的牧场区，此值可能表示历史成本或固定值)。
 - upgradeCost: 升级到下一等级所需的GEM数量。
 - lastProductionTime: 上次产奶的时间戳。
 - isUnlocked: 牧场区是否已解锁 (升级操作时应为 true)。
 - createdAt: 牧场区记录创建时间。
 - updatedAt: 牧场区记录最后更新时间。
 - baseProduction: 单个牛舍的基础产量（每次生产周期）。
 - singleBarnProductionPerSecond: 单个牛舍每秒的牛奶产量。
 - totalProductionPerSecond: 该牧场区所有牛舍每秒的总牛奶产量。
 - speedGrowthPercentage: 当前生产速度相对于初始速度的增长百分比。
 - productionGrowthPercentage: 当前产量相对于初始产量的增长百分比。

**lastProductionTime: 上次产奶的时间戳。**

```
lastProductionTime
```

**isUnlocked: 牧场区是否已解锁 (升级操作时应为 true)。**

```
undefined
```

**createdAt: 牧场区记录创建时间。**

```
createdAt
```

**updatedAt: 牧场区记录最后更新时间。**

```
updatedAt
```

**baseProduction: 单个牛舍的基础产量（每次生产周期）。**

```
baseProduction
```

**singleBarnProductionPerSecond: 单个牛舍每秒的牛奶产量。**

```
singleBarnProductionPerSecond
```

**totalProductionPerSecond: 该牧场区所有牛舍每秒的总牛奶产量。**

```
totalProductionPerSecond
```

**speedGrowthPercentage: 当前生产速度相对于初始速度的增长百分比。**

```
speedGrowthPercentage
```

**productionGrowthPercentage: 当前产量相对于初始产量的增长百分比。**

```
productionGrowthPercentage
```

**示例成功响应:**

```json
{
  "success": true,
  "data": {
    "farmPlot": {
      "id": 2,
      "walletId": 1,
      "plotNumber": 2,
      "level": 2,
      "barnCount": 2,
      "milkProduction": 25.5,
      "productionSpeed": 4.5,
      "unlockCost": 200,
      "upgradeCost": 450,
      "lastProductionTime": "2024-07-30T10:00:00.000Z",
      "isUnlocked": true,
      "accumulatedMilk": 0,
      "createdAt": "2024-07-29T08:00:00.000Z",
      "updatedAt": "2024-07-30T10:05:00.000Z",
      "baseProduction": 12.75,
      "singleBarnProductionPerSecond": 2.833,
      "totalProductionPerSecond": 5.666,
      "speedGrowthPercentage": 10,
      "productionGrowthPercentage": 70
    }
  },
  "message": "请求成功"
}
```

### 5. 注意事项


 - 升级牧场区会消耗用户钱包中的 GEM 货币。
 - 牧场区有最高等级限制（当前为20级）。
 - 只有已解锁的牧场区才能进行升级。
 - 升级后，upgradeCost 字段会更新为升到下一级的费用。

**升级后，upgradeCost 字段会更新为升到下一级的费用。**

```
upgradeCost
```

**接口状态**

> 开发中

**接口URL**

> api/farm/farm-plots/upgrade

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "plotNumber":1
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
	"ok": true,
	"data": {
		"farmPlot": {
			"id": 1,
			"walletId": 1,
			"plotNumber": 1,
			"level": 2,
			"barnCount": 2,
			"milkProduction": 1.5,
			"productionSpeed": 4.761904761904762,
			"unlockCost": 1000,
			"upgradeCost": 300,
			"lastProductionTime": "2025-05-10 08:32:50",
			"isUnlocked": true,
			"accumulatedMilk": 0,
			"createdAt": "2025-05-10 08:22:12",
			"updatedAt": "2025-05-10T07:56:21.412Z"
		}
	}
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 解锁牧场区

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-05-10 16:07:06

> 更新时间: 2025-05-14 16:35:34

## 接口文档：解锁牧场区

### 1. 概述

**该接口用于解锁用户指定的牧场区。解锁会消耗用户的 GEM，并使牧场区变为可用状态，同时设置其初始等级、牛舍数量、产量等属性。**

### 2. 请求


 - URL: /api/farm-plots/unlock
 - 方法: POST
 - 认证: 需要用户认证

```
POST
```

**认证: 需要用户认证**

### 3. 请求体 (Request Body)


 - Content-Type: application/json

```
application/json
```

| 参数名 | 类型  | 是否必需 | 描述  | 示例  |
| --- | --- | ---- | --- | --- |
| plotNumber | number | 是 | 要解锁的牧场区编号 | 3 |

```
undefined
```

**示例请求体:**

```json
{
  "plotNumber": 3
}
```

### 4. 响应 (Response)

#### 4.1. 成功响应


 - 状态码: 200 OK
 - Content-Type: application/json

**Content-Type: application/json**

```
application/json
```

**响应体结构:**

```json
{
  "success": true,
  "data": {
    "farmPlot": {
      "id": "integer",
      "walletId": "integer",
      "plotNumber": "integer",
      "level": "integer", // 解锁后默认为 1
      "barnCount": "integer", // 解锁后默认为 1
      "milkProduction": "number", // 解锁后的初始单次产奶量
      "productionSpeed": "number", // 解锁后的初始生产速度 (秒/次)
      "unlockCost": "number", // 该牧场区的解锁费用 (解锁后此值可能不再直接适用或更新为下一个未解锁的)
      "upgradeCost": "number", // 解锁后，升到下一级所需的GEM
      "lastProductionTime": "string(date-time)",
      "isUnlocked": "boolean", // 解锁后为 true
      "accumulatedMilk": "number", // 解锁时通常为 0
      "createdAt": "string(date-time)",
      "updatedAt": "string(date-time)",
      "baseProduction": "number", // 单个牛舍的基础产量 (每次生产)
      "singleBarnProductionPerSecond": "number", // 单个牛舍每秒产量
      "totalProductionPerSecond": "number", // 该牧场区总每秒产量
      "speedGrowthPercentage": "number", // 速度增长百分比
      "productionGrowthPercentage": "number" // 产量增长百分比
    }
  },
  "message": "请求成功"
}
```

**farmPlot 对象字段说明:**

```
farmPlot
```


 - id: 牧场区记录的唯一ID。
 - walletId: 用户钱包ID。
 - plotNumber: 牧场区编号。
 - level: 解锁后的牧场区等级 (通常为1)。
 - barnCount: 解锁后的牛舍数量 (通常为1)。
 - milkProduction: 解锁后，牧场区单次生产的牛奶量。
 - productionSpeed: 解锁后，牧场区的生产速度（单位：秒/次），通常继承上一个牧场的速度。
 - unlockCost: 解锁该牧场区实际消耗的GEM。
 - upgradeCost: 解锁并初始化后，升级到下一等级所需的GEM数量。
 - lastProductionTime: 上次产奶的时间戳 (解锁时会初始化)。
 - isUnlocked: 牧场区是否已解锁 (此接口成功后为 true)。
 - accumulatedMilk: 牧场区当前累积的牛奶量 (解锁时通常为0)。
 - createdAt: 牧场区记录创建时间。
 - updatedAt: 牧场区记录最后更新时间。
 - baseProduction: 单个牛舍的基础产量（每次生产周期）。
 - singleBarnProductionPerSecond: 单个牛舍每秒的牛奶产量。
 - totalProductionPerSecond: 该牧场区所有牛舍每秒的总牛奶产量。
 - speedGrowthPercentage: 当前生产速度相对于初始速度的增长百分比。
 - productionGrowthPercentage: 当前产量相对于初始产量的增长百分比。

```
lastProductionTime
```

**isUnlocked: 牧场区是否已解锁 (此接口成功后为 true)。**

```
undefined
```

**accumulatedMilk: 牧场区当前累积的牛奶量 (解锁时通常为0)。**

```
accumulatedMilk
```

**createdAt: 牧场区记录创建时间。**

```
createdAt
```

**updatedAt: 牧场区记录最后更新时间。**

```
updatedAt
```

**baseProduction: 单个牛舍的基础产量（每次生产周期）。**

```
baseProduction
```

**singleBarnProductionPerSecond: 单个牛舍每秒的牛奶产量。**

```
singleBarnProductionPerSecond
```

**totalProductionPerSecond: 该牧场区所有牛舍每秒的总牛奶产量。**

```
totalProductionPerSecond
```

**speedGrowthPercentage: 当前生产速度相对于初始速度的增长百分比。**

```
speedGrowthPercentage
```

**productionGrowthPercentage: 当前产量相对于初始产量的增长百分比。**

```
productionGrowthPercentage
```

**示例成功响应:**

```json
{
  "success": true,
  "data": {
    "farmPlot": {
      "id": 3,
      "walletId": 1,
      "plotNumber": 3,
      "level": 1,
      "barnCount": 1,
      "milkProduction": 17.8, // 示例值
      "productionSpeed": 4.5, // 示例值，可能继承自 plotNumber 2
      "unlockCost": 400, // 示例值，实际解锁消耗
      "upgradeCost": 300, // 示例值，升到level 2的费用
      "lastProductionTime": "2024-07-30T10:15:00.000Z",
      "isUnlocked": true,
      "accumulatedMilk": 0,
      "createdAt": "2024-07-29T08:00:00.000Z", // 创建时间可能较早
      "updatedAt": "2024-07-30T10:15:00.000Z",
      "baseProduction": 17.8,
      "singleBarnProductionPerSecond": 3.955,
      "totalProductionPerSecond": 3.955,
      "speedGrowthPercentage": 0, // 初始通常为0或基于继承值
      "productionGrowthPercentage": 0 // 初始通常为0
    }
  },
  "message": "请求成功"
}
```

**接口状态**

> 开发中

**接口URL**

> api/farm/farm-plots/unlock

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "plotNumber":3
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取用户的出货线

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-05-12 11:06:11

> 更新时间: 2025-05-14 16:47:40

## 接口文档：获取用户出货线

### 1. 概述

**该接口用于获取当前认证用户的出货线信息。如果用户尚无出货线记录，系统会自动为其初始化一条新的出货线并返回。**

### 2. 请求


 - URL: /api/delivery-line
 - 方法: GET
 - 认证: 需要用户认证

```
GET
```

**认证: 需要用户认证**

### 3. 请求参数 (Query Parameters)

**无**

### 4. 响应 (Response)

#### 4.1. 成功响应


 - 状态码: 200 OK
 - Content-Type: application/json

**Content-Type: application/json**

```
application/json
```

**响应体结构:**

```json
{
  "success": true,
  "data": {
    "deliveryLine": {
      "id": "integer",
      "walletId": "integer",
      "level": "integer",
      "deliverySpeed": "number", // 出货速度 (秒/方块或秒/批次，具体单位看模型定义)
      "blockUnit": "integer", // 每单位牛奶方块包含的牛奶数量
      "blockPrice": "integer", // 每个牛奶方块的价格 (GEM)
      "upgradeCost": "number", // 升级到下一等级所需的GEM
      "lastDeliveryTime": "string(date-time)", // 上次出货时间
      "pendingMilk": "number", // 待处理的牛奶数量
      "pendingBlocks": "number", // 已转换但待出货的牛奶方块数量
      "createdAt": "string(date-time)",
      "updatedAt": "string(date-time)",
      "speedGrowthPercentage": "number" // 当前出货速度相对于初始速度的增长百分比
    }
  },
  "message": "请求成功"
}
```

**deliveryLine 对象字段说明:**

```
deliveryLine
```


 - id: 出货线记录的唯一ID。
 - walletId: 用户钱包ID。
 - level: 出货线的当前等级。
 - deliverySpeed: 出货速度，单位通常是秒/次或秒/方块。数值越小，速度越快。
 - blockUnit: 将牛奶转换为牛奶方块的单位，例如 1 表示1单位牛奶转换成1个牛奶方块。
 - blockPrice: 每个牛奶方块出售时能获得的GEM数量。
 - upgradeCost: 升级到下一等级出货线所需的GEM数量。
 - lastDeliveryTime: 上一次完成出货的时间戳。
 - pendingMilk: 已经添加到出货线但尚未转换成牛奶方块的牛奶数量。
 - createdAt: 出货线记录创建时间。
 - updatedAt: 出货线记录最后更新时间。
 - speedGrowthPercentage: 当前出货速度相对于初始速度（1级时）的增长百分比。正值表示速度变快。

```
blockPrice
```

**upgradeCost: 升级到下一等级出货线所需的GEM数量。**

```
upgradeCost
```

**lastDeliveryTime: 上一次完成出货的时间戳。**

```
lastDeliveryTime
```

**pendingMilk: 已经添加到出货线但尚未转换成牛奶方块的牛奶数量。**

```
pendingMilk
```

**createdAt: 出货线记录创建时间。**

```
createdAt
```

**updatedAt: 出货线记录最后更新时间。**

```
updatedAt
```

**speedGrowthPercentage: 当前出货速度相对于初始速度（1级时）的增长百分比。正值表示速度变快。**

```
speedGrowthPercentage
```

**示例成功响应:**

```json
{
  "success": true,
  "data": {
    "deliveryLine": {
      "id": 1,
      "walletId": 123,
      "level": 2,
      "deliverySpeed": 4.5, 
      "blockUnit": 5,
      "blockPrice": 6, 
      "upgradeCost": 750,
      "lastDeliveryTime": "2024-07-31T10:00:00.000Z",
      "pendingMilk": 2,
      "pendingBlocks": 3,
      "createdAt": "2024-07-30T08:00:00.000Z",
      "updatedAt": "2024-07-31T10:05:00.000Z",
      "speedGrowthPercentage": 10
    }
  },
  "message": "请求成功"
}
```

**接口状态**

> 开发中

**接口URL**

> /api/delivery/delivery-line

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 升级出货线

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-05-12 11:07:43

> 更新时间: 2025-05-14 16:50:40

## 接口文档：升级出货线

### 1. 概述

**该接口用于升级当前认证用户的出货线。升级会消耗用户的 GEM，并提升出货线的等级，从而可能改变出货速度、每方块牛奶量、方块价格以及下一次升级的费用。**

### 2. 请求


 - URL: /api/delivery-line/upgrade
 - 方法: POST
 - 认证: 需要用户认证

```
POST
```

**认证: 需要用户认证**

### 3. 请求体 (Request Body)

**无。此接口不需要请求体。**

### 4. 响应 (Response)

#### 4.1. 成功响应


 - 状态码: 200 OK
 - Content-Type: application/json

**Content-Type: application/json**

```
application/json
```

**响应体结构:**

```json
{
  "success": true,
  "data": {
    "deliveryLine": {
      "id": "integer",
      "walletId": "integer",
      "level": "integer", // 升级后的等级
      "deliverySpeed": "number", // 升级后的出货速度 (秒/次)
      "blockUnit": "integer", // 升级后的每单位牛奶方块包含的牛奶数量
      "blockPrice": "integer", // 升级后的每个牛奶方块的价格 (GEM)
      "upgradeCost": "number", // 升级到下一等级所需的GEM
      "lastDeliveryTime": "string(date-time)",
      "pendingMilk": "number",
      "pendingBlocks": "number",
      "createdAt": "string(date-time)",
      "updatedAt": "string(date-time)",
      "speedGrowthPercentage": "number" // 升级后当前出货速度相对于初始速度的增长百分比
    }
  },
  "message": "请求成功"
}
```

**deliveryLine 对象字段说明:**

```
deliveryLine
```


 - id: 出货线记录的唯一ID。
 - walletId: 用户钱包ID。
 - level: 升级后出货线的当前等级。
 - deliverySpeed: 升级后的出货速度。数值越小，速度越快。
 - blockUnit: 升级后，将牛奶转换为牛奶方块的单位。
 - blockPrice: 升级后，每个牛奶方块出售时能获得的GEM数量。
 - upgradeCost: 升级到下一等级出货线所需的GEM数量。
 - lastDeliveryTime: 上一次完成出货的时间戳。
 - pendingMilk: 已经添加到出货线但尚未转换成牛奶方块的牛奶数量。
 - pendingBlocks: 已经转换成牛奶方块但尚未出售（出货）的数量。
 - createdAt: 出货线记录创建时间。
 - updatedAt: 出货线记录最后更新时间（即本次升级的时间）。
 - speedGrowthPercentage: 升级后，当前出货速度相对于初始速度（1级时）的增长百分比。正值表示速度变快。

```
upgradeCost
```

**lastDeliveryTime: 上一次完成出货的时间戳。**

```
lastDeliveryTime
```

**pendingMilk: 已经添加到出货线但尚未转换成牛奶方块的牛奶数量。**

```
pendingMilk
```

**pendingBlocks: 已经转换成牛奶方块但尚未出售（出货）的数量。**

```
pendingBlocks
```

**createdAt: 出货线记录创建时间。**

```
createdAt
```

**updatedAt: 出货线记录最后更新时间（即本次升级的时间）。**

```
updatedAt
```

**speedGrowthPercentage: 升级后，当前出货速度相对于初始速度（1级时）的增长百分比。正值表示速度变快。**

```
speedGrowthPercentage
```

**升级规则（参考模型定义）:**


 - level: level + 1
 - deliverySpeed: deliverySpeed / 1.01 (速度提升1%)
 - blockUnit: blockUnit * 2.0
 - blockPrice: blockPrice * 2.0
 - upgradeCost: upgradeCost * 2.0

```
deliverySpeed / 1.01
```

**blockUnit: blockUnit * 2.0**

```
undefined
```

**blockPrice: blockPrice * 2.0**

```
undefined
```

**upgradeCost: upgradeCost * 2.0**

```
undefined
```

**示例成功响应:**

```json
{
  "success": true,
  "data": {
    "deliveryLine": {
      "id": 1,
      "walletId": 123,
      "level": 3, // 假设从2级升到3级
      "deliverySpeed": 4.455, // 假设原为 4.5 / 1.01
      "blockUnit": 2, // 假设原为 1 * 2.0
      "blockPrice": 2, // 假设原为 1 * 2.0
      "upgradeCost": 1000, // 假设原为 500 * 2.0
      "lastDeliveryTime": "2024-07-31T10:00:00.000Z",
      "pendingMilk": 2,
      "pendingBlocks": 3,
      "createdAt": "2024-07-30T08:00:00.000Z",
      "updatedAt": "2024-07-31T10:15:00.000Z",
      "speedGrowthPercentage": 19.09 // 示例百分比
    }
  },
  "message": "请求成功"
}
```

**接口状态**

> 开发中

**接口URL**

> /api/delivery/delivery-line/upgrade

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 增加待处理牛奶

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-05-13 15:46:40

> 更新时间: 2025-05-13 15:48:31

## 增加待处理牛奶 API

### 接口信息


 - URL: /api/wallet/increase-milk
 - 方法: POST
 - 描述: 增加用户钱包的待处理牛奶数量
 - 认证要求: 需要钱包认证

**认证要求: 需要钱包认证**

### 请求参数

#### 请求体 (JSON)

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| pendingMilk | Number | 是 | 要增加的待处理牛奶数量，必须是大于等于0的数字 |

### 响应结果

#### 成功响应


 - 状态码: 200 OK
 - 响应体:

```json
{
  "ok": true,
  "data": {
    "pendingMilk": 100 // 更新后的待处理牛奶总量
  },
  "message": "成功增加待处理牛奶"
}
```

### 业务逻辑说明


 1. 验证请求参数，确保 pendingMilk 是有效的数字且大于等于0
 2. 暂时没有什么增加限制

**暂时没有什么增加限制**

**接口状态**

> 开发中

**接口URL**

> /api/wallet/increase-milk

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "pendingMilk":100
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 增加宝石数量

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-05-13 15:48:51

> 更新时间: 2025-05-14 10:30:32

## 增加宝石 API

### 接口信息


 - URL: /api/wallet/increase-gem
 - 方法: POST
 - 描述: 增加用户钱包的宝石值
 - 认证要求: 需要钱包认证

**认证要求: 需要钱包认证**

### 请求参数

#### 请求体 (JSON)

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| milkAmount | Number | 是 | 要增加的宝石数量 |

### 响应结果

#### 成功响应


 - 状态码: 200 OK
 - 响应体:

```json
{
    "ok": true,
    "data": {
        "gem": 484095,
        "pendingMilk": 50,
        "milkUsed": 10,
        "gemAmount": 10
    },
    "message": "success.milkToGem"
}
```

### 业务逻辑说明


 1. 验证请求参数，确保 amount 是有效的数字且大于0
 2. 暂时没有什么增加限制
 3. 返回更新后的宝石总量

**返回更新后的宝石总量**

**接口状态**

> 开发中

**接口URL**

> /api/wallet/increase-gem

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "milkAmount":10
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
{
	"ok": true,
	"data": {
		"gem": 486045
	},
	"message": "success.increaseGem"
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 测试签名

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-05-14 15:42:03

> 更新时间: 2025-05-14 15:53:12

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> api/web3-sign-test/sign

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "message":"{{message}}"
}
```

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 更新用户名

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-05-21 06:47:49

> 更新时间: 2025-05-21 06:53:06

## Web3钱包更新用户名接口

### 接口信息


 - 路径: /api/web3-auth/update-username
 - 方法: POST
 - 认证要求: 需要钱包认证
 - Content-Type: application/json

**Content-Type: application/json**

### 功能说明

**该接口允许已登录的用户更新其用户名。系统会验证新用户名是否已被其他用户使用，并确保用户名符合长度要求。**

### 请求参数

#### 请求头

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| Authorization | string | 是 | 认证令牌，格式为：Bearer {token} |

```
Bearer {token}
```

#### 请求体 (JSON)

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| newUsername | string | 是 | 新的用户名，长度必须在3-30个字符之间 |

#### 请求体示例

```json
{
  "newUsername": "my_new_username"
}
```

### 响应结果

#### 成功响应


 - 状态码: 200 OK
 - 响应体:

```json
{
  "ok": true,
  "data": {
    "id": 12,
    "username": "my_new_username"
  }
}
```

#### 响应字段说明

| 字段名 | 类型  | 描述  |
| --- | --- | --- |
| id | number | 用户ID |
| username | string | 更新后的用户名 |

### 注意事项


 1. 用户必须先登录并获取有效的认证令牌
 2. 用户名必须是唯一的，如果新用户名已被其他用户使用，将返回错误
 3. 用户名长度必须在3-30个字符之间

**接口状态**

> 开发中

**接口URL**

> api/web3-auth/update-username

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**Mock URL**

> /web3-auth/update-username?apipost_id=3020024431e012

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "newUsername":"joe123"
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取离线奖励

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-05-26 15:07:21

> 更新时间: 2025-05-26 15:07:21

## 离线奖励API文档

### 概述

**离线奖励系统允许玩家在不活跃期间继续获得基于出货线的收益。如果用户在1分钟内没有活跃（通过调用 /api/wallet/increase-gem 接口更新 lastActiveTime），则视为离线状态。离线奖励最多累积8小时。**

```
undefined
```

### 离线状态判断


 - 在线状态: 1分钟内有活跃记录（lastActiveTime 在1分钟内）
 - 离线状态: 超过1分钟没有活跃记录，或者从未有过活跃记录
 - 最大离线奖励时间: 8小时
 - 特殊情况: 如果用户从未活跃过（lastActiveTime 为空），则不返回离线奖励

**特殊情况: 如果用户从未活跃过（lastActiveTime 为空），则不返回离线奖励**

```
lastActiveTime
```

### 离线奖励计算规则

**离线奖励基于用户的出货线等级和配置进行计算，采用简化的直接计算方式：**


 1. 出货周期数: deliveryCycles = floor(effectiveOfflineTime / deliverySpeed)
 2. 离线宝石: offlineGem = deliveryCycles × blockPrice

**离线宝石: offlineGem = deliveryCycles × blockPrice**

```
offlineGem = deliveryCycles × blockPrice
```

#### 计算说明


 - 出货线每隔 deliverySpeed 秒进行一次出货
 - 每次出货直接获得 blockPrice 数量的宝石
 - 不再计算中间的牛奶转换过程，直接计算最终宝石产出

```
blockPrice
```

**不再计算中间的牛奶转换过程，直接计算最终宝石产出**

### 1. 获取离线奖励信息

#### 请求


 - URL: /api/wallet/offline-reward
 - 方法: GET
 - 认证: 需要钱包认证 (walletAuthMiddleware)

```
GET
```

**认证: 需要钱包认证 (walletAuthMiddleware)**

#### 响应

##### 成功响应 (200 OK)

**在线状态响应:**

```json
{
  "success": true,
  "data": {
    "isOffline": false,
    "offlineTime": 0,
    "offlineReward": {
      "gem": 0
    }
  },
  "message": "Offline reward retrieved successfully"
}
```

**离线状态响应:**

```json
{
  "success": true,
  "data": {
    "isOffline": true,
    "offlineTime": 7200,
    "offlineReward": {
      "gem": 120
    }
  },
  "message": "Offline reward retrieved successfully"
}
```

**从未活跃用户响应:**

```json
{
  "success": true,
  "data": {
    "isOffline": false,
    "offlineTime": 0,
    "offlineReward": {
      "gem": 0
    }
  },
  "message": "Offline reward retrieved successfully"
}
```

##### 字段说明


 - isOffline: 是否处于离线状态
 - offlineTime: 离线时间（秒）
 - offlineReward.gem: 可获得的宝石数量

```
offlineTime
```

**offlineReward.gem: 可获得的宝石数量**

```
offlineReward.gem
```

**接口状态**

> 开发中

**接口URL**

> api/wallet/offline-reward

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**Mock URL**

> /wallet/offline-reward?apipost_id=36d013ef71e015

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 结算离线奖励

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-05-26 15:08:41

> 更新时间: 2025-05-26 15:08:41

### 2. 结算离线奖励

#### 请求


 - URL: /api/wallet/claim-offline-reward
 - 方法: POST
 - 认证: 需要钱包认证

```
POST
```

**认证: 需要钱包认证**

#### 响应

##### 成功响应 (200 OK)

```json
{
  "success": true,
  "data": {
    "offlineTime": 7200,
    "claimedReward": {
      "gem": 120
    },
    "currentGem": 520
  },
  "message": "Offline reward claimed successfully"
}
```

##### 字段说明


 - offlineTime: 离线时间（秒）
 - claimedReward.gem: 实际获得的宝石数量
 - currentGem: 结算后的宝石总量

```
claimedReward.gem
```

**currentGem: 结算后的宝石总量**

```
currentGem
```

### 使用流程


 1. 检查离线奖励: 调用 GET /api/wallet/offline-reward 检查是否有离线奖励
 2. 结算奖励: 如果有奖励，调用 POST /api/wallet/claim-offline-reward 进行结算
 3. 更新活跃时间: 每次调用 /api/wallet/increase-gem 时会自动更新最后活跃时间

```
POST /api/wallet/claim-offline-reward
```

**更新活跃时间: 每次调用 /api/wallet/increase-gem 时会自动更新最后活跃时间**

```
/api/wallet/increase-gem
```

### 注意事项


 1. 离线奖励最多累积8小时，超过8小时的部分不会计算
 2. 只有在离线状态（超过1分钟未活跃）时才能获得离线奖励
 3. 如果用户从未活跃过（lastActiveTime 为空），则不会获得离线奖励
 4. 结算离线奖励后会重置最后活跃时间为当前时间
 5. 离线奖励基于当前出货线配置计算，升级出货线可以提高离线收益

**离线奖励基于当前出货线配置计算，升级出货线可以提高离线收益**

**接口状态**

> 开发中

**接口URL**

> api/wallet/claim-offline-reward

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**Mock URL**

> /wallet/claim-offline-reward?apipost_id=36fe7335f1e03c

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取商店商品列表

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-05-27 17:26:39

> 更新时间: 2025-05-29 22:56:36

## IAP 商店商品列表 API

### 接口信息


 - 路径: /api/iap/store/products
 - 方法: GET
 - 描述: 获取IAP商店中所有可购买的商品列表，包含购买限制检查
 - 认证: 需要钱包认证，请求头中必须包含 Authorization: Bearer <token>

**认证: 需要钱包认证，请求头中必须包含 Authorization: Bearer <token>**

```
Authorization: Bearer <token>
```

### 请求参数

#### 查询参数

**无需查询参数**

### 响应参数

#### 成功响应

**状态码: 200**

```json
{
  "ok": true,
  "products": [
    {
      "id": 1,
      "productId": "speed_boost_2x_24h",
      "name": "2倍速度提升道具",
      "type": "speed_boost",
      "multiplier": 2,
      "duration": 24,
      "quantity": 1,
      "priceUsd": 4.99,
      "priceKaia": 10.5,
      "dailyLimit": 0,
      "accountLimit": 0,
      "description": "提升生产速度2倍，持续24小时",
      "isActive": true,
      "canPurchase": true,
      "reason": "",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    },
    {
      "id": 2,
      "productId": "time_warp_4h",
      "name": "4小时时间跳跃",
      "type": "time_warp",
      "multiplier": 1,
      "duration": 4,
      "quantity": 1,
      "priceUsd": 2.99,
      "priceKaia": 6.0,
      "dailyLimit": 0,
      "accountLimit": 0,
      "description": "立即获得4小时的产出",
      "isActive": true,
      "canPurchase": false,
      "reason": "今日已达到该类型商品的购买限制",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    },
    {
      "id": 3,
      "productId": "vip_membership_30d",
      "name": "VIP会员30天",
      "type": "vip_membership",
      "multiplier": null,
      "duration": null,
      "quantity": null,
      "priceUsd": 19.99,
      "priceKaia": 42.0,
      "dailyLimit": 1,
      "accountLimit": 0,
      "description": "享受30天VIP特权，包含多项加成",
      "isActive": true,
      "canPurchase": true,
      "reason": "",
      "config": {
        "durationDays": 30,
        "deliverySpeedBonus": 0.3,
        "blockPriceBonus": 0.2,
        "productionSpeedBonus": 0.3,
        "benefits": ["delivery_speed_boost", "block_price_boost", "production_speed_boost"]
      },
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    }
  ]
}
```

### 字段说明

#### products 数组字段

| 字段名 | 类型  | 描述  |
| --- | --- | --- |
| id | number | 商品ID |
| productId | string | 商品唯一标识 |
| name | string | 商品名称 |
| type | string | 商品类型，可能值: speed_boost(速度提升), time_warp(时间跳跃), vip_membership(VIP会员), special_offer(特殊套餐) |
| multiplier | number\|null | 加成倍数（对于速度提升道具） |
| duration | number\|null | 持续时间（小时） |
| quantity | number\|null | 数量 |
| priceUsd | number | 美元价格 |
| priceKaia | number | KAIA代币价格 |
| dailyLimit | number | 每日购买限制（0表示无限制） |
| accountLimit | number | 账号总购买限制（0表示无限制） |
| description | string | 商品描述（支持多语言） |
| isActive | boolean | 是否激活 |
| canPurchase | boolean | 当前是否可以购买 |
| reason | string | 不能购买的原因（当canPurchase为false时） |
| config | object\|null | 商品配置信息（对于VIP会员和特殊套餐） |
| createdAt | string | 创建时间 |
| updatedAt | string | 更新时间 |

```
undefined
```

### 业务逻辑说明

#### 购买限制检查


 1. 每日类型限制: 对于speed_boost和time_warp类型的商品，每日只能购买1次同类型商品
 2. 每日商品限制: 其他商品按照dailyLimit字段进行每日购买限制
 3. 账号限制: 按照accountLimit字段检查账号总购买次数
 4. VIP会员检查: 如果已有激活的VIP会员，则不能再购买VIP会员商品

```
dailyLimit
```

**账号限制: 按照accountLimit字段检查账号总购买次数**

```
accountLimit
```

**VIP会员检查: 如果已有激活的VIP会员，则不能再购买VIP会员商品**

#### 商品排序

**商品按照以下规则排序：**


 1. 首先按商品类型（type）升序排列
 2. 然后按美元价格（priceUsd）升序排列

#### 多语言支持

**商品描述支持多语言，系统会根据请求的语言设置返回对应的描述文本。**

**接口状态**

> 开发中

**接口URL**

> api/iap/store/products

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**Mock URL**

> /iap/store/products?apipost_id=386ab559f1e004

**请求方式**

> GET

**Content-Type**

> json

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Accept-Language | zh | string | 是 | - |

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**请求Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| Accept-Language | zh | string | 是 | - |

**Query**

# 创建支付订单

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-05-28 12:09:38

> 更新时间: 2025-05-30 17:50:23

## IAP 支付订单创建 API

### 接口信息


 - 路径: /api/iap/payment/create
 - 方法: POST
 - 描述: 创建IAP商品支付订单，支持单个商品购买
 - 认证: 需要钱包认证，请求头中必须包含 Authorization: Bearer <token>

**认证: 需要钱包认证，请求头中必须包含 Authorization: Bearer <token>**

```
Authorization: Bearer <token>
```

### 请求参数

#### 请求头

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| Authorization | string | 是 | Bearer 认证token |
| Content-Type | string | 是 | application/json |

#### 请求体

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| productId | number | 是 | 商品ID |
| imageUrl | string | 否 | 商品图片URL |
| paymentMethod | string | 是 | 支付方式，可选值："kaia"、"usd" |
| testMode | boolean | 否 | 测试模式，默认根据环境自动判断 |

#### 请求体示例

```json
{
  "productId": 1,
  "imageUrl": "https://example.com/product1.jpg",
  "paymentMethod": "kaia",
  "testMode": true
}
```

### 响应参数

#### 成功响应

**状态码: 200**

```json
{
  "ok": true,
  "paymentId": "payment_123456789",
  "amount": 100.5,
  "purchase": {
    "id": 1,
    "walletId": 123,
    "productId": 1,
    "paymentId": "payment_123456789",
    "paymentMethod": "kaia",
    "amount": 100.5,
    "currency": "KAIA",
    "status": "pending",
    "purchaseDate": "2024-01-15T10:30:00.000Z"
  },
  "product": {
    "id": 1,
    "name": "速度提升道具",
    "type": "speed_boost"
  }
}
```

##### 响应字段说明

| 参数名 | 类型  | 描述  |
| --- | --- | --- |
| ok | boolean | 请求是否成功 |
| paymentId | string | DappPortal支付订单ID |
| amount | number | 商品金额 |
| purchase | object | 购买记录 |
| product | object | 商品信息 |

### 业务逻辑说明

#### 购买限制检查


 1. 每日限制: 检查用户当日已购买该商品的次数是否超过限制
 2. 账号限制: 检查用户账号总共购买该商品的次数是否超过限制
 3. VIP会员检查: 如果购买VIP会员商品，检查是否已有激活的VIP会员

#### 支付方式


 - kaia: 使用KAIA代币支付，通过加密货币网关处理
 - usd: 使用美元支付，通过Stripe支付网关处理

#### 价格计算


 - KAIA支付: 使用商品的 priceKaia 字段
 - USD支付: 使用商品的 priceUsd 字段，并转换为最小单位（分）

**USD支付: 使用商品的 priceUsd 字段，并转换为最小单位（分）**

```
priceUsd
```

#### DappPortal集成

**接口会调用DappPortal支付服务创建支付订单，包含以下信息：**


 - 买家钱包地址
 - 支付网关类型（CRYPTO或STRIPE）
 - 货币代码和价格
 - 商品详情列表
 - 回调URL配置

### 注意事项


 1. 认证要求: 所有请求必须包含有效的Bearer token
 2. 商品状态: 只能购买状态为激活（isActive: true）的商品
 3. 购买限制: 系统会自动检查每日和账号购买限制
 4. VIP会员: 同一时间只能有一个激活的VIP会员
 5. 单商品限制: 每次只能购买一个商品，不支持批量购买
 6. 图片URL: imageUrl为可选参数，如果不提供将使用空字符串
 7. 支付状态: 创建的购买记录初始状态为"pending"，需要通过支付回调更新状态
 8. 测试模式: 在非生产环境下，DappPortal将运行在测试模式

**接口状态**

> 开发中

**接口URL**

> api/iap/payment/create

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**Mock URL**

> /iap/payment/create?apipost_id=396cafe4b1e01c

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
  "productId": 25,
  "imageUrl": "https://mobileproducts.cdn.sohu.com/q_70,c_zoom,w_1024/prod/img/aigc/simple_ai/2025/05/28/bwmFcFZPYNs.png",
  "paymentMethod": "kaia",
  "testMode": true
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取用户拥有的道具

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-05-29 14:42:38

> 更新时间: 2025-05-29 23:00:45

## IAP 用户道具查询 API

### 接口信息


 - 路径: /api/iap/boosters
 - 方法: GET
 - 描述: 获取用户拥有的道具
 - 认证: 需要钱包认证，请求头中必须包含 Authorization: Bearer <token>

**认证: 需要钱包认证，请求头中必须包含 Authorization: Bearer <token>**

```
Authorization: Bearer <token>
```

### 请求参数

#### 请求头

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| Authorization | string | 是 | Bearer 认证token |

#### 查询参数

**无需查询参数**

### 响应参数

#### 成功响应

**状态码: 200**

```json
{
  "ok": true,
  "boosters": [
    {
      "id": 1,
      "walletId": 123,
      "type": "speed_boost",
      "multiplier": 2,
      "duration": 24,
      "quantity": 3,
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z",
      "product": {
        "id": 1,
        "productId": "speed_boost_2x_24h",
        "name": "2倍速度提升道具",
        "type": "speed_boost",
        "multiplier": 2,
        "duration": 24,
        "description": "提升生产速度2倍，持续24小时"
      }
    },
    {
      "id": 2,
      "walletId": 123,
      "type": "time_warp",
      "multiplier": 1,
      "duration": 4,
      "quantity": 2,
      "createdAt": "2024-01-16T10:30:00.000Z",
      "updatedAt": "2024-01-16T10:30:00.000Z",
      "product": {
        "id": 3,
        "productId": "time_warp_4h",
        "name": "4小时时间跳跃",
        "type": "time_warp",
        "multiplier": 1,
        "duration": 4,
        "description": "立即获得4小时的产出"
      }
    }
  ]
}
```

### 字段说明

#### boosters 数组字段

| 字段名 | 类型  | 描述  |
| --- | --- | --- |
| id | number | 道具ID |
| walletId | number | 用户钱包ID |
| type | string | 道具类型，可能值: speed_boost(速度提升), time_warp(时间跳跃) |
| multiplier | number | 加成倍数，如2倍、4倍 |
| duration | number | 持续时间（小时） |
| quantity | number | 拥有数量 |
| createdAt | string | 创建时间 |
| updatedAt | string | 更新时间 |
| product | object | 关联的商品信息，可能为null |

```
undefined
```

#### product 对象字段

| 字段名 | 类型  | 描述  |
| --- | --- | --- |
| id | number | 商品ID |
| productId | string | 商品唯一标识 |
| name | string | 商品名称 |
| type | string | 商品类型 |
| multiplier | number | 加成倍数（对于速度提升道具） |
| duration | number | 持续时间（小时） |
| description | string | 商品描述 |

### 业务逻辑说明


 1. 接口会返回用户拥有的所有道具
 2. 同时返回当前激活状态的道具（结束时间大于当前时间）
 3. 每个道具会关联对应的商品信息，包含名称、描述等详细信息

**接口状态**

> 开发中

**接口URL**

> api/iap/boosters

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**Mock URL**

> /iap/boosters?apipost_id=3ad95cc9fc6006

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 使用道具

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-05-29 16:39:57

> 更新时间: 2025-05-29 21:59:48

```text
# IAP 使用道具 API
## 接口信息

- **路径**: `/api/iap/boosters/use`
- **方法**: POST
- **描述**: 使用用户拥有的道具，激活其效果
- **认证**: 需要钱包认证，请求头中必须包含 `Authorization: Bearer <token>`

## 请求参数
### 请求体

json
{
  "boosterId": 1
}

| 字段名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| boosterId | number | 是 | 要使用的道具ID |

## 响应参数
### 成功响应

**状态码**: 200

json
{
  "ok": true,
  "activeBooster": {
    "id": 1,
    "walletId": 123,
    "productId": 1,
    "type": "speed_boost",
    "multiplier": 2,
    "startTime": "2024-01-15T10:30:00.000Z",
    "endTime": "2024-01-16T10:30:00.000Z",
    "status": "active",
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  },
  "message": "speed_boost activated successfully"
}

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| ok | boolean | 操作是否成功 |
| activeBooster | object | 激活的道具信息 |
| activeBooster.id | number | 激活道具ID |
| activeBooster.walletId | number | 钱包ID |
| activeBooster.productId | number | 关联的商品ID |
| activeBooster.type | string | 道具类型，可能值: `speed_boost`(速度提升), `time_warp`(时间跳跃) |
| activeBooster.multiplier | number | 加成倍数 |
| activeBooster.startTime | string | 激活开始时间 |
| activeBooster.endTime | string | 激活结束时间 |
| activeBooster.status | string | 状态，值为 `active` |
| message | string | 成功消息 |

## 业务逻辑说明

1. **道具使用流程**:
   - 验证用户拥有指定的道具且数量大于0
   - 检查是否已有同类型的激活道具
   - 减少道具数量
   - 创建激活道具记录

2. **激活时间计算**:
   - 激活开始时间为当前时间
   - 激活结束时间根据道具的持续时间（小时）计算

3. **道具类型限制**:
   - 同一类型的道具不能同时激活多个
   - 必须等待当前激活的同类型道具过期后才能使用新的道具
```

**接口状态**

> 开发中

**接口URL**

> api/iap/boosters/use

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**Mock URL**

> /iap/boosters/use?apipost_id=3af420dbbc6026

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "boosterId":1
}
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取激活的道具

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-05-29 17:45:59

> 更新时间: 2025-05-30 06:44:07

## IAP 激活道具查询 API

### 接口信息


 - 路径: /api/iap/boosters/active
 - 方法: GET
 - 描述: 获取用户当前激活的道具列表，只返回未过期的激活道具
 - 认证: 需要钱包认证，请求头中必须包含 Authorization: Bearer <token>

**认证: 需要钱包认证，请求头中必须包含 Authorization: Bearer <token>**

```
Authorization: Bearer <token>
```

### 请求参数

#### 请求头

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| Authorization | string | 是 | Bearer 认证token |

#### 查询参数

**无需查询参数**

### 响应参数

#### 成功响应

**状态码: 200**

```json
{
  "ok": true,
  "activeBoosters": [
    {
      "id": 1,
      "walletId": 123,
      "productId": 1,
      "type": "speed_boost",
      "multiplier": 2,
      "startTime": "2024-01-15T10:30:00.000Z",
      "endTime": "2024-01-16T10:30:00.000Z",
      "status": "active",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z",
      "product": {
        "id": 1,
        "name": "2倍速度提升道具",
        "description": "提升生产速度2倍，持续24小时",
        "type": "speed_boost",
        "priceUsd": 4.99,
        "priceKaia": 100,
        "config": {
          "effectType": "production_speed",
          "stackable": false
        }
      }
    },
    {
      "id": 2,
      "walletId": 123,
      "productId": 3,
      "type": "time_warp",
      "multiplier": 1,
      "startTime": "2024-01-15T12:00:00.000Z",
      "endTime": "2024-01-15T16:00:00.000Z",
      "status": "active",
      "createdAt": "2024-01-15T12:00:00.000Z",
      "updatedAt": "2024-01-15T12:00:00.000Z",
      "product": {
        "id": 3,
        "name": "4小时时间跳跃",
        "description": "立即获得4小时的产出",
        "type": "time_warp",
        "priceUsd": 2.99,
        "priceKaia": 60,
        "config": {
          "effectType": "instant_production",
          "stackable": true
        }
      }
    }
  ]
}
```

#### 响应字段说明

| 字段名 | 类型  | 描述  |
| --- | --- | --- |
| ok | boolean | 操作是否成功 |
| activeBoosters | array | 激活道具列表 |
| activeBoosters[].id | number | 激活道具记录ID |
| activeBoosters[].walletId | number | 钱包ID |
| activeBoosters[].productId | number | 关联的商品ID |
| activeBoosters[].type | string | 道具类型，可能值: speed_boost(速度提升), time_warp(时间跳跃) |
| activeBoosters[].multiplier | number | 加成倍数 |
| activeBoosters[].startTime | string | 激活开始时间 |
| activeBoosters[].endTime | string | 激活结束时间 |
| activeBoosters[].status | string | 状态，值为 active |
| activeBoosters[].product | object | 关联的商品信息 |
| activeBoosters[].product.id | number | 商品ID |
| activeBoosters[].product.name | string | 商品名称 |
| activeBoosters[].product.description | string | 商品描述 |
| activeBoosters[].product.type | string | 商品类型 |
| activeBoosters[].product.priceUsd | number | USD价格 |
| activeBoosters[].product.priceKaia | number | KAIA价格 |
| activeBoosters[].product.config | object | 商品配置信息 |

```
undefined
```

### 业务逻辑

#### 查询条件


 1. 用户过滤: 只返回当前用户的激活道具
 2. 时间过滤: 只返回 endTime 大于当前时间的道具
 3. 排序: 按照 endTime 升序排列，即将过期的道具排在前面

**排序: 按照 endTime 升序排列，即将过期的道具排在前面**

```
endTime
```

#### 道具类型说明


 - speed_boost: 速度提升道具，提升生产速度
 - time_warp: 时间跳跃道具，立即获得一定时间的产出

#### 状态说明


 - active: 道具正在生效中
 - expired: 道具已过期（此接口不会返回）
 - used: 道具已使用完毕（此接口不会返回）

**接口状态**

> 开发中

**接口URL**

> api/iap/boosters/active

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**Mock URL**

> /iap/boosters/active?apipost_id=3b034f493c604f

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取VIP状态

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-05-30 06:44:38

> 更新时间: 2025-05-30 06:51:32

## IAP VIP状态查询 API

### 接口信息


 - 路径: /api/iap/vip/status
 - 方法: GET
 - 描述: 获取用户的VIP会员状态信息
 - 认证: 需要钱包认证，请求头中必须包含 Authorization: Bearer <token>

**认证: 需要钱包认证，请求头中必须包含 Authorization: Bearer <token>**

```
Authorization: Bearer <token>
```

### 请求参数

#### Headers

| 参数名 | 类型  | 必填  | 描述  |
| --- | --- | --- | --- |
| Authorization | string | 是 | Bearer token，用于用户认证 |

#### Query参数

**无需额外的查询参数**

### 响应参数

#### 成功响应

**状态码: 200**

##### 用户有VIP会员

```json
{
  "ok": true,
  "isVip": true,
  "membership": {
    "id": 1,
    "walletId": 123,
    "startTime": "2024-01-15T10:30:00.000Z",
    "endTime": "2024-02-15T10:30:00.000Z",
    "isActive": true,
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

##### 用户无VIP会员

```json
{
  "ok": true,
  "isVip": false,
  "membership": null
}
```

#### 响应字段说明

| 字段名 | 类型  | 描述  |
| --- | --- | --- |
| ok | boolean | 请求是否成功 |
| isVip | boolean | 用户是否为VIP会员 |
| membership | object\|null | VIP会员信息，如果用户不是VIP则为null |

##### membership对象字段说明

| 字段名 | 类型  | 描述  |
| --- | --- | --- |
| id | number | VIP会员记录ID |
| walletId | number | 用户钱包ID |
| startTime | string | VIP开始时间（ISO 8601格式） |
| endTime | string | VIP结束时间（ISO 8601格式） |
| isActive | boolean | VIP是否当前激活状态 |
| createdAt | string | 记录创建时间（ISO 8601格式） |
| updatedAt | string | 记录更新时间（ISO 8601格式） |

### 业务逻辑说明

#### VIP状态检查


 1. 自动状态更新: 接口会自动检查VIP会员的有效期，如果当前时间超出了VIP有效期，会自动将isActive状态更新为false
 2. 时间验证: 系统会比较当前时间与startTime和endTime，确保VIP状态的准确性

```
false
```

**时间验证: 系统会比较当前时间与startTime和endTime，确保VIP状态的准确性**

```
undefined
```

#### VIP权益

**根据IAP产品配置，VIP会员享有以下权益：**


 - 30%出货线速度加成
 - 20%出货线价格加成  
 - 30%牧场区生产速度加成

**接口状态**

> 开发中

**接口URL**

> api/iap/vip/status

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**Mock URL**

> /iap/vip/status?apipost_id=3bb576e3bc6004

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

# 获取购买历史

> 创建人: Joe

> 更新人: Joe

> 创建时间: 2025-05-30 06:54:05

> 更新时间: 2025-05-30 06:59:18

## IAP 购买历史查询 API

### 接口信息


 - 路径: /api/iap/purchase/history
 - 方法: GET
 - 描述: 获取用户的IAP商品购买历史记录，支持分页查询
 - 认证: 需要钱包认证，请求头中必须包含 Authorization: Bearer <token>

**认证: 需要钱包认证，请求头中必须包含 Authorization: Bearer <token>**

```
Authorization: Bearer <token>
```

### 请求参数

#### Body参数

```json
{
  "page": 1,        // 页码，默认为1（可选）
  "limit": 20       // 每页记录数，默认为20（可选）
}
```

##### 参数说明

| 参数名 | 类型  | 必填  | 默认值 | 说明  |
| --- | --- | --- | --- | --- |
| page | number | 否 | 1 | 页码，从1开始 |
| limit | number | 否 | 20 | 每页返回的记录数量 |

### 响应参数

#### 成功响应

**状态码: 200**

```json
{
  "ok": true,
  "purchases": [
    {
      "id": 1,
      "walletId": 123,
      "productId": 1,
      "paymentId": "payment_123456789",
      "status": "completed",
      "paymentMethod": "kaia",
      "amount": 8.7719,
      "currency": "KAIA",
      "purchaseDate": "2024-01-15T10:30:00.000Z",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:35:00.000Z",
      "IapProduct": {
        "name": "速度提升道具 x2 1小时",
        "type": "speed_boost",
        "description": "提升游戏速度2倍，持续1小时"
      }
    },
    {
      "id": 2,
      "walletId": 123,
      "productId": 5,
      "paymentId": "payment_987654321",
      "status": "pending",
      "paymentMethod": "stripe",
      "amount": 9.99,
      "currency": "USD",
      "purchaseDate": "2024-01-14T15:20:00.000Z",
      "createdAt": "2024-01-14T15:20:00.000Z",
      "updatedAt": "2024-01-14T15:20:00.000Z",
      "IapProduct": {
        "name": "VIP会员 30天",
        "type": "vip_membership",
        "description": "享受VIP特权30天"
      }
    }
  ],
  "pagination": {
    "total": 15,
    "page": 1,
    "limit": 20,
    "totalPages": 1
  }
}
```

##### 响应字段说明

**purchases数组字段:**

| 字段名 | 类型  | 说明  |
| --- | --- | --- |
| id | number | 购买记录ID |
| walletId | number | 用户钱包ID |
| productId | number | 商品ID |
| paymentId | string | DappPortal支付ID |
| status | string | 支付状态：pending(待支付)、completed(已完成)、failed(失败)、refunded(已退款) |
| paymentMethod | string | 支付方式：kaia(KAIA代币)、stripe(信用卡) |
| amount | number | 支付金额 |
| currency | string | 货币类型：USD、KAIA |
| purchaseDate | string | 购买时间 |
| createdAt | string | 记录创建时间 |
| updatedAt | string | 记录更新时间 |
| IapProduct | object | 商品信息 |

**IapProduct字段:**

| 字段名 | 类型  | 说明  |
| --- | --- | --- |
| name | string | 商品名称 |
| type | string | 商品类型：speed_boost、time_warp、vip_membership、special_offer |
| description | string | 商品描述 |

**pagination字段:**

| 字段名 | 类型  | 说明  |
| --- | --- | --- |
| total | number | 总记录数 |
| page | number | 当前页码 |
| limit | number | 每页记录数 |
| totalPages | number | 总页数 |

**接口状态**

> 开发中

**接口URL**

> api/iap/purchase/history

| 环境  | URL |
| --- | --- |
| 正式环境 | https://wolf.jpegonapechain.com/ |

**Mock URL**

> /iap/purchase/history?apipost_id=3bb7af007c6015

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
暂无数据
```

**认证方式**

> Bearer Token

> 在Header添加参数 Authorization，其值为在Bearer之后拼接空格和访问令牌

> Authorization: Bearer your_access_token

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**
