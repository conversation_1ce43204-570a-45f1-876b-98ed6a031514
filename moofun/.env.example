# ===========================================
# MooFun 项目环境变量配置
# ===========================================

# API 配置
# 后端 API 的基础 URL
VITE_API_BASE_URL=https://api.moofun.app

# 应用域名配置
# 生产环境的应用域名
VITE_APP_DOMAIN=https://moofun.app

# ===========================================
# 平台集成配置
# ===========================================

# Telegram Bot 配置
# Telegram 机器人的用户名
VITE_TELEGRAM_BOT_USERNAME=moofun_bot

# Line LIFF 配置
# Line LIFF 应用的 ID
VITE_LINE_LIFF_ID=your-liff-id-here

# Kaia DApp Portal SDK 配置
# DApp Portal 客户端 ID（用于钱包集成）
VITE_DAPP_CLIENT_ID=your-dapp-client-id-here

# ===========================================
# 社交媒体配置
# ===========================================

# Telegram 频道链接
VITE_TELEGRAM_CHANNEL=https://t.me/MooFun

# Twitter/X 账号链接
VITE_TWITTER_HANDLE=https://x.com/MooFunGame

# ===========================================
# 平台检测配置（可选）
# ===========================================

# 强制启用 Kaia 环境检测
VITE_KAIA_ENVIRONMENT=true

# ===========================================
# 开发环境示例配置
# ===========================================

# 开发环境 API 配置示例
# VITE_API_BASE_URL=http://localhost:8080/api

# 开发环境应用域名示例
# VITE_APP_DOMAIN=http://localhost:3000

# 测试环境 Telegram Bot
# VITE_TELEGRAM_BOT_USERNAME=moofun_test_bot

# 测试环境 LIFF ID
# VITE_LINE_LIFF_ID=test-liff-id

# 测试环境 DApp Client ID
# VITE_DAPP_CLIENT_ID=test-client-id
