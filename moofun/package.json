{"name": "moofun-project", "version": "1.2.2", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "build:gb": "vue-tsc --noEmit && vite build --mode theme-gb", "build:mf": "vue-tsc --noEmit && vite build --mode theme-mf", "build:all": "npm run build:gb && npm run build:mf", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .eslint<PERSON>ore", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,vue,css,scss}\"", "lint:check": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --ignore-path .eslintignore", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,vue,css,scss}\"", "i18n:report": "vue-i18n-extract report -v src/features/**/*.vue -l public/locales/en.json --format json"}, "dependencies": {"@aptos-connect/wallet-adapter-plugin": "^2.4.3", "@esotericsoftware/spine-phaser": "^4.2.38", "@esotericsoftware/spine-pixi-v8": "^4.2.87", "@fontsource/rammetto-one": "^5.1.1", "@line/liff": "^2.27.0", "@linenext/dapp-portal-sdk": "^1.3.5", "@reown/appkit": "^1.7.16", "@reown/appkit-adapter-ethers5": "^1.7.16", "@reown/appkit-adapter-wagmi": "^1.7.16", "@reown/appkit-wallet-button": "^1.7.16", "axios": "^1.9.0", "ethers": "^5.7.2", "phaser": "^3.87.0", "pinia": "^2.3.1", "pixi.js": "^8.11.0", "vue": "^3.5.17", "vue-i18n": "^10.0.7", "vue-router": "^4.5.1"}, "devDependencies": {"@intlify/devtools-types": "^11.1.7", "@types/node": "^24.0.3", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "cross-env": "^7.0.3", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-vue": "^10.1.0", "prettier": "^3.5.3", "terser": "^5.43.1", "vite": "^6.3.5", "vue-i18n-extract": "^2.0.7", "vue-tsc": "^2.2.10"}, "main": "vite.config.js", "keywords": [], "author": "", "license": "ISC", "description": ""}