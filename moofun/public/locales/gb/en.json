{"guide": {"step1": "Welcome to GOOSEBOX! Let me give you a quick tour of the place!", "step2": "This is the ranch area where you can raise geese to get eggs.", "step3": "This is the delivery line where our eggs are packaged and sold.", "step4": "Tap here to upgrade the ranch area to get more eggs.", "step7": "You've completed a task. Tap here to claim your reward.", "step11": "Egg production too slow? Tap here to unlock a new ranch area.", "step14": "Too many eggs are clogging the delivery line. Tap here to upgrade it.", "step17": "Now go build up your farm and become the egg tycoon!"}, "menu": {"home": "Home", "earn": "<PERSON><PERSON><PERSON>", "wheel": "Wheel", "invite": "Invite", "profile": "Profile", "task": "Task", "jackpot": "Jackpot", "referral": "Referral", "inventory": "Inventory"}, "invite": {"title": "Invite a friend", "invite": "Invite", "inviteFriend": "Invite a friend", "invitePremiumFriend": "Invite a friend with TG Premium", "infoText": "Accumulating a certain number of invitations will earn you mystery boxes daily", "dailyChestTargets": "Earn {count} chest daily", "referralDetails": "Referral Details", "user": "User"}, "profile": {"title": "Profile", "ticketTransfer": "Mystery Egg Transfer", "tonRewardBalance": "TON Reward Balance", "kaiaRewardBalance": "KAIA Reward Balance", "settings": "Settings", "items": "Settings", "leaderboard": "Leaderboard"}, "setting": {"title": "Setting", "selectLanguage": "Select Language", "sound": "Sound", "soundOn": "On", "soundOff": "Off", "username": "Username", "bindEmail": "Bind <PERSON><PERSON>", "notSet": "-", "walletHistory": "Wallet History", "gameHistory": "Game History", "vestedHistory": "Vested $Bull History", "globalIndividualPoolHistory": "Global Individual Pool History", "globalTeamPoolHistory": "Global Team Pool History"}, "leaderboard": {"title": "Leaderboard", "rank": "Rank", "user": "User", "gemAmount": "<PERSON><PERSON>"}, "chest": {"accumulated": "ACCUMULATED CHEST", "info": "Info", "odd": "Odd", "countdown": "Count down", "open": "OPEN", "collect": "Collect Chest", "waiting": "Waiting", "jackpot": "Jackpot", "icon": "Chest icon", "image": "Chest", "clickToOpen": "Click anywhere to open", "rewards": "Rewards", "shareRewards": "Share Your Rewards!", "openAnotherChest": "Open Another Chest?", "allChestsOpened": "All Chests Opened!", "nextChest": "Next Chest", "totalRewards": "Total Rewards", "unavailable": "Unavailable", "claimedChests": "Claimed {count} Daily Chest(s)!", "openReferralChests": "Open Referral Chests ({count})", "noChestsAvailable": "No Chests Available", "openedReferralChests": "Opened {count} Referral Chest(s)!", "referralChestsRewards": "Check your inventory for rewards", "failedToOpenChests": "Failed to open referral chests", "errorOpeningChests": "An error occurred while opening referral chests"}, "topup": {"title": "PHRS Topup", "amountLabel": "Topup Amount", "amountPlaceholder": "Enter topup amount", "infoText": "Enter the amount of PHRS you want to topup", "topupButton": "Confirm Topup", "minAmount": "Minimum Topup", "maxAmount": "Maximum Topup", "walletBalance": "Wallet Balance", "walletAddress": "Wallet Address", "contractPaused": "Contract is paused, cannot topup", "processing": "Processing...", "success": "Topup successful!", "failed": "Topup failed", "insufficientBalance": "Insufficient PHRS balance in account", "amountTooSmall": "Topup amount too small, minimum amount is {minAmount} PHRS", "amountTooLarge": "Topup amount too large, maximum amount is {maxAmount} PHRS", "transactionHash": "Transaction Hash", "shortHash": "Hash", "gasPrice": "Current Gas Price", "preparingTransaction": "Preparing to execute topup transaction...", "loadingContractInfo": "Loading contract info...", "failedToLoadContractInfo": "Failed to load contract info", "transactionSubmitted": "Transaction submitted", "transactionSuccess": "Topup successful!", "transactionFailed": "Transaction failed", "numberFormat": {"trillion": "T", "billion": "B", "million": "M", "thousand": "K"}}, "wallet": {"connect": "Connect", "copyAddress": "Copy Address", "addressCopied": "Address copied!", "disconnect": "Disconnect", "connectionFailed": "Connection Failed", "failedToConnect": "Failed to connect wallet", "disconnected": "Wallet disconnected", "failedToDisconnect": "Failed to disconnect wallet", "addressCopiedNotification": "Wallet address copied to clipboard", "connected": "Wallet Connected", "successfullyConnected": "Successfully connected to your wallet", "invalidTokenDisconnecting": "Invalid token but wallet connected, disconnecting", "invalidTokenDetected": "Invalid token detected", "failedToVerifyProof": "Failed to verify proof", "validTokenFound": "Valid token found on init", "reconnected": "Wallet Reconnected", "successfullyReconnected": "Successfully reconnected to your wallet", "failedToGeneratePayload": "Failed to generate TON proof payload", "settingConnectParameters": "Setting connect parameters with payload:", "walletError": "<PERSON><PERSON>", "connection": "connection", "disconnection": "disconnection", "failedDuring": "Failed during {context}", "errorIn": "Error in", "statusChanged": "Wallet connection status changed: {oldStatus} -> {newStatus}"}, "notification": {"chestCollected": "Chest Collected!", "checkInventory": "Check your inventory for rewards", "failedToCollect": "Failed to collect chest", "errorCollecting": "An error occurred while collecting the chest"}, "button": {"claimReward": "<PERSON><PERSON><PERSON>", "claimDailyReward": "Claim Daily Reward", "next": "NEXT", "copy": "COPY", "comingSoon": "Coming soon", "unavailable": "Unavailable"}, "buttons": {"items": "Items"}, "general": {"close": "CLOSE"}, "referral": {"title": "Referral", "level1": "Level 1", "level2": "Level 2", "columns": {"no": "No", "name": "Name", "user": "User", "totalAccumulated": "Total Accumulated", "lastSevenDays": "Last 7 Days", "weekBoost": "Week Boost", "dayBoost": "Day Boost"}, "applied": "Referral Applied", "codeAppliedSuccess": "Your referral code has been successfully applied", "bindingFailedNoResponse": "Failed to bind referral code: No response from API", "processingError": "Error processing referral:", "defaultCodePlaceholder": "Referral Code", "linkCopied": "Link Copied!", "linkCopiedToClipboard": "Referral link copied to clipboard", "failedToCopyError": "Failed to copy: ", "failedToCopyLink": "Failed to copy referral link"}, "pagination": {"previous": "Previous", "next": "Next", "pageInfo": "Page {current} of {total}"}, "earn": {"title": "<PERSON><PERSON><PERSON>", "dailyCheckIn": "DAILY CHECK-IN", "socialTask": "SOCIAL TASK", "loading": "Loading tasks...", "completed": "Completed", "taskCompletedSuccess": "Task completed successfully", "taskCompletedError": "Failed to complete task", "unexpectedError": "An unexpected error occurred", "failedToLoadTasks": "Failed to load tasks"}, "inventory": {"title": "Inventory", "tabs": {"items": "Items", "shards": "Shards"}, "slots": {"emptySlot": "Empty slot selected.", "noItemSelected": "No item selected."}, "buttons": {"craft": "Craft"}, "items": {"ticket": {"name": "Ticket", "description": "Used to enter premium challenges."}, "mysteryEgg": {"name": "Mystery Egg", "description": "A mysterious egg that can be used in future events."}, "gem": {"name": "Gem", "description": "A rare currency."}, "kaia": {"name": "KAIA", "description": "Blockchain token used in decentralized operations."}, "fragments": {"green": {"name": "Green Fragment", "description": "160 needed for 1 mystery egg."}, "blue": {"name": "Blue Fragment", "description": "80 needed for 3 mystery eggs."}, "purple": {"name": "Purple Fragment", "description": "40 needed for 15 mystery eggs."}, "gold": {"name": "Gold Fragment", "description": "20 needed for 60 mystery eggs."}}}}, "alt": {"chestIcon": "Chest Icon", "smallChest": "Small Chest", "accumulatedChestPanel": "Accumulated Chest Panel"}, "tabs": {"info": "Info", "odd": "Odd"}, "titles": {"jackpot": "JACKPOT", "accumulatedChest": "Accumulated Chest"}, "descriptions": {"chestIndication": "Indication about the accumulate chest"}, "jackpotPopup": {"title": "Congratulations!", "rarity": "Jackpot", "message": "You've won the <strong>jackpot reward</strong>! Keep participating to earn even more treasures.", "acceptButton": "Accept"}, "fourChestPopup": {"title": "Welcome Bonus!", "rarity": "Early Access Reward", "message": "As one of our earliest users, you've unlocked a <strong>special reward chest</strong>! Claim it now before it disappears.", "acceptButton": "<PERSON><PERSON><PERSON>est", "loading": "Claiming..."}, "countdownChestPopup": {"infoTitle": "Countdown Chest Info", "infoContent": "You can claim one chest every 24 hours when the countdown ends. After claiming, the timer resets for another 24 hours.<br><br>When you open a <strong>Level 3</strong> or <strong>Level 4</strong> chest, you'll get a <strong>Boost Link</strong> that can be shared with friends. Each valid assist grants both you and the helper rewards:<ul><li><strong>Level 3 Chest:</strong> +1 hour speed boost and GEM ×50</li><li><strong>Level 4 Chest:</strong> +2 hours speed boost and GEM ×100</li></ul>Each link is valid for 12 hours and can be used up to 12 times. After that, it will expire automatically.<br><br>You may also receive <strong>Promo Boost</strong> from other users' chest activity. If you have an active countdown, this assist will automatically reduce your time based on the chest they open.<br><br><table class='promo-boost-table'><thead><tr><th>Chest Level</th><th>Tier 1 Boost</th><th>Tier 2 Boost</th></tr></thead><tbody><tr><td>Level 1</td><td>10 minutes</td><td>5 minutes</td></tr><tr><td>Level 2</td><td>30 minutes</td><td>15 minutes</td></tr><tr><td>Level 3</td><td>1 hour</td><td>30 minutes</td></tr><tr><td>Level 4</td><td>12 hours</td><td>6 hours</td></tr></tbody></table><br><em>Note: Telegram Star auto-claim feature is not yet available.</em>", "oddTitle": "Chest Drop Rates", "oddContent": "Each time you claim a countdown chest, one of the following chest types will be randomly granted:<ul><li><strong>LV1 Chest</strong> – 60% chance (Most common)</li><li><strong>LV2 Chest</strong> – 28% chance (Uncommon)</li><li><strong>LV3 Chest</strong> – 10% chance (Rare)</li><li><strong>LV4 Chest</strong> – 2% chance (Very rare)</li></ul>Higher level chests offer a better chance of receiving rarer fragments and larger amounts of gems.<br><br>Here's a general idea of the kind of rewards each chest may contain:<ul><li><strong>LV1:</strong> Green fragments and a small chance at blue</li><li><strong>LV2:</strong> Blue fragments and possibly purple</li><li><strong>LV3:</strong> Blue, purple, and sometimes gold fragments</li><li><strong>LV4:</strong> Guaranteed purple and gold fragments, and high gem rewards</li></ul><br><em>Note: Item probabilities and reward contents may be adjusted over time to improve game balance.</em>"}, "jackpotInfoOverlay": {"title": "Jackpot Chest", "description": "The Jackpot Chest rewards players for daily activity. By performing key actions, you help grow the KAIA Jackpot pool.", "contribute": "Contribute", "contributeDesc": "Actions like registration and chest opening add KAIA to the pool.", "trigger": "<PERSON><PERSON>", "triggerDesc": "Once the pool is full, the next chest opened instantly wins the Jackpot.", "tiers": "Tiers", "tiersDesc": "Higher levels offer bigger rewards and include multiple contribution types.", "earnedPerAction": "Earned per action:", "newUserRegistration": "New user registration", "openingChest": "Opening chest", "jackpotReward": "Jackpot Reward:", "note": "Note: Jackpot rules and rewards may change to keep the system balanced."}, "ticketTransfer": {"title": "Transfer", "recipientLabel": "Recipient Address", "recipientPlaceholder": "Enter wallet address", "ticketCountLabel": "Number of Mystery Eggs", "ticketCountPlaceholder": "Enter number of mystery eggs", "availableTickets": "{current}/{max} Mystery Eggs Available", "transferButton": "Transfer Mystery Eggs", "transferring": "Transferring...", "successMessage": "Mystery eggs successfully transferred!", "genericError": "Failed to transfer mystery eggs. Please try again.", "errorFetchingLimit": "Could not retrieve available mystery eggs.", "invalidAddress": "Please enter a valid wallet address", "invalidAmount": "Please enter a valid number of mystery eggs", "exceedsLimit": "You can only transfer up to {max} mystery eggs", "noTicketsAvailable": "No mystery eggs available for transfer", "transferComplete": "Transfer Complete", "transferFailed": "Transfer Failed"}, "shop": {"title": "Shop", "categories": {"all": "All", "specialOffer": "Special Offer", "timeWrap": "Time Wrap", "booster": "<PERSON><PERSON><PERSON>", "characters": "Characters", "boosters": "Boosters", "vip": "VIP Membership", "special": "Special Items"}, "buy": "Buy", "owned": "Owned", "insufficientFunds": "Insufficient Funds", "confirmPurchase": "Confirm Purchase", "buyNow": "Buy Now", "purchaseSuccess": "Successfully purchased {name}!", "purchaseError": "Purchase failed, please try again", "loadError": "Failed to load shop products", "noProducts": "No products available", "payment": {"creating": "Creating payment order...", "starting": "Starting payment...", "processing": "Processing payment...", "completed": "Payment completed!", "failed": "Payment failed!", "success": "Payment completed successfully!", "canceled": "Payment canceled", "errors": {"insufficientFunds": "Insufficient balance in your wallet. Please add more funds and try again.", "canceled": "Payment was canceled by user or timed out", "failed": "Payment failed. Please try again.", "generic": "Payment failed", "purchaseLimitExceeded": "Purchase limit exceeded. This item can only be purchased once.", "dailyLimitExceeded": "Daily purchase limit reached. Please try again tomorrow.", "accountLimitExceeded": "Account purchase limit reached. This item can only be purchased once.", "insufficientBalance": "Insufficient PHRS balance. Please add more funds and try again.", "productNotAvailable": "Product is not available for purchase.", "vipAlreadyActive": "VIP membership is already active."}}}, "newTask": {"title": "Mission", "claimed": "Claimed", "claimSuccess": "Claimed successfully!", "claimError": "Claim error, please try again"}, "common": {"loading": "Loading...", "processing": "Processing...", "cancel": "Cancel", "back": "Back", "confirm": "Confirm", "send": "Send", "upgrade": "Upgrade", "upgrading": "Upgrading...", "unlock": "Unlock", "unlocking": "Unlocking...", "timer": "Timer", "rank": "Rank", "name": "Name", "gems": "Gems", "draw1Times": "Draw 1 times", "paymentOrder": "Payment Order", "editUsername": "Edit Username", "enterName": "Enter name...", "usernameUpdated": "Username updated successfully!", "mysteryEggTransfer": "Mystery Egg Transfer", "recipientAddress": "Recipient Address", "enterWalletAddress": "Enter wallet address", "numberOfMysteryEggs": "Number of Mystery Eggs", "enterAmountToSend": "Enter amount to send", "recipientAddressInfo": "The recipient address must be a valid wallet address. Double-check before confirming.", "kaiaWithdraw": "KAIA Withdraw", "jackpotChest": "Jackpot Chest", "excitingRewardsAwait": "Exciting Rewards await", "earnChest": "<PERSON><PERSON><PERSON>", "jackpotInfo": "Jackpot Info", "boosterInventory": "<PERSON><PERSON>er Inventory", "offlineReward": "Offline Reward", "deliveryLine": "Delivery Line", "milkProfit": "Milk profit", "deliverySpeed": "Delivery speed", "milkCapacity": "Milk capacity", "farmPlot": "Farm Plot", "milkProduction": "Milk production", "productionSpeed": "Production speed", "cowCount": "Cow count", "unlockPlotDescription": "Unlock the plot to start earning milk.", "bullFarmVip": "Bull Farm VIP", "removeAllAds": "Absolutely remove all ADS", "x2RevenueMultiplier": "x2 permanent revenue multiplier", "x3AllBonuses": "x3 on all bonuses", "exclusiveDiamondPrestige": "and Exclusive diamond prestige", "start": "Start!", "trialInfo": "3-days free trial, then RM<br />36.99 per week", "subscriptionInfo": "If you want to cancel the subscription during<br />the free trial, you have at least 24 hours<br />before the end of this free trial. If not, you will<br />be charged rm 36.99 per week. You can cancel<br />your subscription through your account in the<br />store.", "subscriptionTerms": "Subscription Terms", "privacyPolicy": "Privacy Policy", "iapShop": "IAP SHOP", "get": "Get", "use": "Use", "hr": "hr", "luckyDraw": "Lucky Draw", "ticket": "Ticket", "ton": "Ton", "mooTimes": "<PERSON><PERSON>(Times)", "offlineRewardDescription": "You obtained {gems} gems while you were away.", "claiming": "Claiming...", "claim": "<PERSON><PERSON><PERSON>", "level": "Level", "inventory": "Inventory"}, "history": {"title": "Payment History", "loading": "Loading history...", "retry": "Retry", "noHistory": "No payment records found", "amount": "Amount", "date": "Date", "orderId": "Order ID", "method": "Payment Method", "previousPage": "Previous", "nextPage": "Next", "total": "Total", "completed": "Completed", "failed": "Failed", "pending": "Pending", "created": "Created", "started": "Started", "registeredOnPg": "Registered on Payment Gateway", "captured": "Captured", "confirmed": "Confirmed", "finalized": "Finalized", "refunded": "Refunded", "confirmFailed": "Confirm Failed", "canceled": "Canceled", "chargeback": "Chargeback", "unknown": "Unknown", "description": "View all your payment records and transaction history", "openHistory": "Open Payment History", "copyOrderId": "Copy Order ID", "openSdkHistory": "Open SDK Payment History"}, "deliveryLine": {"level": "Level"}, "farmPlot": {"plot": "Plot", "locked": "Locked", "maxed": "Maxed", "farmPlot": "Farm Plot"}, "wheel": {"title": "Wheel Game"}, "errors": {"usernameRequired": "Username is required", "usernameLength": "Username must be between 3 and 30 characters", "usernameUpdateFailed": "Failed to update username", "usernameAlreadyExists": "Username already exists", "usernameNotChanged": "New username is the same as current username", "unauthorized": "Unauthorized access"}}