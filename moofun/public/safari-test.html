<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Safari兼容性测试</title>
    <style>
        :root {
            --vh: 1vh;
            --app-width: 100vw;
            --app-height: 100vh;
            --base-unit: calc(100vw / 375);
        }

        @media (min-width: 600px) {
            :root {
                --app-width: 375px;
                --base-unit: 1px;
            }
        }

        body {
            font-family: 'Urbanist', sans-serif;
            font-size: calc(var(--base-unit) * 16);
            background: #000000;
            color: white;
            margin: 0;
            padding: 20px;
        }

        .test-container {
            background: #333;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
        }

        .pass { background: #2d5a2d; }
        .fail { background: #5a2d2d; }
        .info { background: #2d4a5a; }

        #app-simulation {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: var(--app-width);
            height: var(--app-height);
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            z-index: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: calc(var(--base-unit) * 24);
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .controls {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 10;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border-radius: 5px;
        }

        button {
            background: #4ecdc4;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="controls">
        <button onclick="toggleApp()">切换应用显示</button>
        <button onclick="runTests()">运行测试</button>
    </div>

    <div id="app-simulation" style="display: none;">
        <div>
            <h2>应用模拟</h2>
            <p>如果你能看到这个，说明CSS变量工作正常</p>
            <p>宽度: <span id="width-display"></span></p>
            <p>Base Unit: <span id="base-unit-display"></span></p>
        </div>
    </div>

    <h1>Safari兼容性测试结果</h1>
    
    <div class="test-container info">
        <h2>浏览器信息</h2>
        <div id="browser-info"></div>
    </div>

    <div class="test-container" id="css-test-container">
        <h2>CSS特性测试</h2>
        <div id="css-tests"></div>
    </div>

    <div class="test-container" id="js-test-container">
        <h2>JavaScript特性测试</h2>
        <div id="js-tests"></div>
    </div>

    <script>
        // 浏览器检测
        function detectBrowser() {
            const ua = navigator.userAgent;
            const isSafari = /^((?!chrome|android).)*safari/i.test(ua);
            const version = isSafari ? (ua.match(/Version\/(\d+\.\d+)/) || [])[1] : null;
            
            return {
                userAgent: ua,
                isSafari,
                version,
                isChrome: /chrome/i.test(ua),
                isFirefox: /firefox/i.test(ua)
            };
        }

        // CSS特性测试
        function testCSSFeature(property, value) {
            try {
                const testElement = document.createElement('div');
                testElement.style[property] = value;
                return testElement.style[property] === value;
            } catch (error) {
                return false;
            }
        }

        // 获取CSS变量值
        function getCSSVariableValue(variable) {
            return getComputedStyle(document.documentElement).getPropertyValue(variable).trim();
        }

        // 运行所有测试
        function runTests() {
            const browserInfo = detectBrowser();
            
            // 浏览器信息
            document.getElementById('browser-info').innerHTML = `
                <p><strong>User Agent:</strong> ${browserInfo.userAgent}</p>
                <p><strong>Safari:</strong> ${browserInfo.isSafari ? `是 (v${browserInfo.version})` : '否'}</p>
                <p><strong>Chrome:</strong> ${browserInfo.isChrome ? '是' : '否'}</p>
                <p><strong>Firefox:</strong> ${browserInfo.isFirefox ? '是' : '否'}</p>
            `;

            // CSS测试
            const cssTests = [
                { name: 'CSS Custom Properties', test: () => testCSSFeature('--test-var', 'test') },
                { name: 'CSS calc() function', test: () => testCSSFeature('width', 'calc(100px + 50px)') },
                { name: 'CSS min() function', test: () => testCSSFeature('width', 'min(100px, 50%)') },
                { name: 'CSS transform', test: () => testCSSFeature('transform', 'translateX(-50%)') },
                { name: 'CSS filter', test: () => testCSSFeature('filter', 'brightness(0.8)') }
            ];

            let cssTestsHTML = '';
            cssTests.forEach(test => {
                const result = test.test();
                const className = result ? 'pass' : 'fail';
                const icon = result ? '✓' : '✗';
                cssTestsHTML += `<p class="${className}">${icon} ${test.name}</p>`;
            });

            // CSS变量值测试
            const appWidth = getCSSVariableValue('--app-width');
            const baseUnit = getCSSVariableValue('--base-unit');
            const appHeight = getCSSVariableValue('--app-height');

            cssTestsHTML += `<hr>`;
            cssTestsHTML += `<p><strong>CSS变量值:</strong></p>`;
            cssTestsHTML += `<p>--app-width: ${appWidth || '未定义'}</p>`;
            cssTestsHTML += `<p>--base-unit: ${baseUnit || '未定义'}</p>`;
            cssTestsHTML += `<p>--app-height: ${appHeight || '未定义'}</p>`;

            document.getElementById('css-tests').innerHTML = cssTestsHTML;

            // 更新应用模拟显示
            document.getElementById('width-display').textContent = appWidth || '未知';
            document.getElementById('base-unit-display').textContent = baseUnit || '未知';

            // JavaScript测试
            const jsTests = [
                { name: 'ES6 Arrow Functions', test: () => { try { eval('() => {}'); return true; } catch { return false; } } },
                { name: 'ES6 Template Literals', test: () => { try { eval('`test`'); return true; } catch { return false; } } },
                { name: 'ES6 Destructuring', test: () => { try { eval('const {a} = {a:1}'); return true; } catch { return false; } } },
                { name: 'Promises', test: () => typeof Promise !== 'undefined' },
                { name: 'Fetch API', test: () => typeof fetch !== 'undefined' }
            ];

            let jsTestsHTML = '';
            jsTests.forEach(test => {
                const result = test.test();
                const className = result ? 'pass' : 'fail';
                const icon = result ? '✓' : '✗';
                jsTestsHTML += `<p class="${className}">${icon} ${test.name}</p>`;
            });

            document.getElementById('js-tests').innerHTML = jsTestsHTML;

            // 设置容器样式
            document.getElementById('css-test-container').className = 
                cssTests.every(t => t.test()) ? 'test-container pass' : 'test-container fail';
            document.getElementById('js-test-container').className = 
                jsTests.every(t => t.test()) ? 'test-container pass' : 'test-container fail';
        }

        // 切换应用显示
        function toggleApp() {
            const app = document.getElementById('app-simulation');
            app.style.display = app.style.display === 'none' ? 'flex' : 'none';
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
