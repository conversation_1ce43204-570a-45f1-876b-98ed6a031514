<script setup>
const props = defineProps({
  modelValue: [String, Number],
  type: {
    type: String,
    default: 'text',
  },
  placeholder: {
    type: String,
    default: '',
  },
  id: {
    type: String,
    default: '',
  },
  label: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:modelValue'])
</script>

<template>
  <div class="input-field-rect">
    <div class="input-header" v-if="label">{{ label }}</div>
    <div class="input-field">
      <input
        :id="id"
        :type="type"
        :placeholder="placeholder"
        :value="modelValue"
        @input="$emit('update:modelValue', type === 'number' ? Number($event.target.value) : $event.target.value)"        
      />
      <div v-if="$slots.icon">
        <slot name="icon" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.input-field-rect {
    display: flex;
    flex-direction: column;
    gap: calc(var(--base-unit) * 6);
}

.input-header {
  color: #4b5974;
  text-align: start;
}

.input-field {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: aliceblue;
    padding: calc(var(--base-unit) * 8);
    border-radius: calc(var(--base-unit) * 8);
    border: calc(var(--base-unit) * 1) solid #D5D7DA
}

.input-field input {
  flex: 1;
  border: none;
  background: transparent;
  outline: none;
  color:#717680;
}

.input-field input::placeholder {
  opacity: 0.5; 
}


</style>
