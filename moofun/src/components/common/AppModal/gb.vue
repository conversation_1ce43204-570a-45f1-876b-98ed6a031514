<template>
    <div :class="['info-panel']">
        <slot></slot>
    </div>
</template>

<style scoped>
.info-panel {
    padding: calc(var(--base-unit) * 16);
    padding-bottom: calc(var(--base-unit) * 24);
    box-shadow: inset 0 0 0 calc(var(--base-unit) * 3) rgba(240, 255, 255, 1);
    border-radius: calc(var(--base-unit) * 20);
    background: #E7D5CB;
    display: flex;
    flex-direction: column;
    gap: calc(var(--base-unit) * 16);
    color: #68514B;
    -webkit-text-stroke: 0;
    text-shadow: none;
}

.bottom {
    display: flex;
    justify-content: center;
    align-items: center;
}

.bottom img {
    height: calc(var(--base-unit) * 28);
}

.bottom.white  {
    display: hidden;
}
</style>