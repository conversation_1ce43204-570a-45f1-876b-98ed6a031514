<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

const props = defineProps<{
  page: number
  total: number
  pageSize: number
}>()

const emit = defineEmits<{
  (e: 'update:page', newPage: number): void
}>()

const { t } = useI18n()

const totalPages = computed(() =>
  Math.ceil(props.total / props.pageSize)
)

const prevDisabled = computed(() => props.page <= 1)
const nextDisabled = computed(() => props.page >= totalPages.value)

const goToPage = (newPage: number) => {
  if (newPage >= 1 && newPage <= totalPages.value) {
    emit('update:page', newPage)
  }
}
</script>

<template>
  <div class="pagination-controls" v-if="total > pageSize">
    <button
      class="pagination-button"
      :disabled="prevDisabled"
      @click="goToPage(page - 1)"
    >
      {{ t('pagination.previous') }}
    </button>

    <span class="pagination-info">
      {{ t('pagination.pageInfo', { current: page, total: totalPages }) }}
    </span>

    <button
      class="pagination-button"
      :disabled="nextDisabled"
      @click="goToPage(page + 1)"
    >
      {{ t('pagination.next') }}
    </button>
  </div>
</template>

<style scoped>
.pagination-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: calc(var(--base-unit) * 10);
  gap: calc(var(--base-unit) * 8);
}

.pagination-button {
  color: rgba(75, 89, 116, 1);
  background-color: rgba(240, 255, 255, 0.8);
  border: solid calc(var(--base-unit) * 2) rgba(100, 149, 237, 0.8);
  border-radius: calc(var(--base-unit) * 6);
  padding: calc(var(--base-unit) * 4) calc(var(--base-unit) * 12);
  cursor: pointer;
  width: calc(var(--base-unit) * 100);
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  font-size: calc(var(--base-unit) * 12);
  color: white;
}
</style>
