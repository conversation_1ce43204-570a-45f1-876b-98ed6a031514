<template>
    <div 
      class="background-image" 
      :style="{ backgroundImage: `url(${imagePath})` }"
    ></div>
  </template>
    
  <script>
  export default {
    name: 'Background',
    props: {
      imagePath: {
        type: String,
        required: true,
        default: ''
      }
    }
  }
  </script>
    
  <style scoped>
  .background-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    z-index: -1; /* Ensures it stays behind other content */
  }
  </style>