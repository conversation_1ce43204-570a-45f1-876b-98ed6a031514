<template>
    <div 
      class="close-button-container"
      @click="$emit('close')"
    >
      <img 
        :src="closeImagePath" 
        alt="Close"
        class="close-button-image"
      />
    </div>
  </template>
  
  <script>
  import { defineComponent } from 'vue';
  
  export default defineComponent({
    name: 'CloseButton2',
    props: {
      closeImagePath: {
        type: String,
        default: '/ui/gb/close-btn2.png'
      }
    },
    emits: ['close']
  });
  </script>
  
  <style scoped>
  .close-button-container {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    width: calc(var(--base-unit) * 52);
    height: calc(var(--base-unit) * 52);
  }
  
  .close-button-image {
    width: calc(var(--base-unit) * 36);
    height: calc(var(--base-unit) * 36);
  }
  </style>