<template>
    <div 
      class="close-button-container"
      @click="$emit('close')"
    >
      <img 
        :src="closeImagePath" 
        alt="Close"
        class="close-button-image"
      />
    </div>
  </template>
  
  <script>
  import { defineComponent } from 'vue';
  
  export default defineComponent({
    name: 'CloseButton',
    props: {
      closeImagePath: {
        type: String,
        default: '/ui/mf/close-btn.png'
      }
    },
    emits: ['close']
  });
  </script>
  
  <style scoped>
  .close-button-container {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    width: calc(var(--base-unit) * 40);
    height: calc(var(--base-unit) * 40);
  }
  
  .close-button-image {
    width: calc(var(--base-unit) * 36);
    height: calc(var(--base-unit) * 36);
    filter: drop-shadow(0px 1px 2px rgba(16, 24, 40, 0.05));
  }
  </style>