<template>
  <button 
    :class="['button-container', { 'disabled-button': disabled }]"
    @click="$emit('click')"
  >
    <div class="button-inner main-text" :style="{ padding }">
      <slot>{{ $t('button.claimReward') }}</slot>
    </div>
  </button>
</template>

<script>
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'ColorButton',
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    padding: {
      type: String,
      default: 'calc(var(--base-unit) * 8) calc(var(--base-unit) * 14)'
    }
  },
  emits: ['click']
});
</script>

<style scoped>
.button-container {
  position: relative;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  padding: calc(var(--base-unit) * 1);
  background: #D38D17;
  border: none;
  border-radius: calc(var(--base-unit) * 8);
  font-size: calc(var(--base-unit) * 16);
  transition: transform 0.1s ease, box-shadow 0.1s ease;
}

.button-inner {
  position: relative;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  flex: 1; /* Expand to fill the container */
  width: 100%; /* Ensure it takes full width */
  height: 100%; /* Ensure it takes full height */
  background: rgba(255, 170, 24, 1);
  border-radius: calc(var(--base-unit) * 8);
  box-sizing: border-box; /* Ensures padding doesn't shrink the inner box */
}

/* Press effect */
.button-container:active {
  transform: scale(0.95); /* Slightly shrink button */
  box-shadow: inset 0 0 calc(var(--base-unit) * 82) rgba(0, 0, 0, 0.2); /* Add inset shadow */
}

.disabled-button {
  filter: grayscale(100%) brightness(0.6);
  cursor: not-allowed;
}
</style>