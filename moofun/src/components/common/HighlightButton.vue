<template>
    <div class="button-container flex-center">
      <!-- Button Image -->
      <img :src="imgSrc" alt="button" class="button-img" />
      
      <!-- Shadow Effect -->
      <img :src="imgSrc" alt="button shadow" class="button-shadow" />
  
      <!-- Centered Text -->
      <span class="button-text">{{ text }}</span>
    </div>
  </template>
  
  <script>
  export default {
    props: {
      imgSrc: {
        type: String,
        required: true,
        default: "/ui/highlight-button.png"
      },
      text: {
        type: String,
        required: true,
        default: "Click Me"
      }
    }
  };
  </script>
  
  <style scoped>
  .button-container {
    position: relative;
    width: 100%;
    transition: transform 0.15s ease-out;
  }

  .button-container:active {
    transform: scale(0.98);
  }

  
  .button-img {
    width: 100%;
    height: auto;
    position: relative;
    z-index: 2;
  }
  
  .button-shadow {
    position: absolute;
    width: 100%;
    height: auto;
    top: calc(var(--base-unit) * 4); /* Adjust shadow offset */
    filter: blur(calc(var(--base-unit) * 4)) opacity(0.76) brightness(0);
    z-index: 1;
  }
  
  .button-text {
    position: absolute;
    z-index: 3;
  }
  </style>
  