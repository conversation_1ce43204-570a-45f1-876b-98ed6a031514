<template>
    <div class="reward-item">
        <img v-if="type === 'diamond'" :src="theme.diamond" :class="{ 'disabled-reward': disabled }" />
        <img v-else-if="type === 'chest'" :src="theme.chest" :class="{ 'disabled-reward': disabled }" />
        <img v-else-if="type === 'gem'" :src="theme.gem" :class="{ 'disabled-reward': disabled }" />
        <div class="reward-amount main-text">
            {{ formatNumber(amount) }}
        </div>
    </div>
</template>

<script setup lang="ts">
import { formatNumber } from '@/utils/utils'
import { useThemeAssets } from '@/themes'
const theme = useThemeAssets()
defineProps<{
    type: 'diamond' | 'chest' | 'gem' | 'item',
    amount: number
    disabled?: boolean
}>()
</script>

<style scoped src="./style.css"></style>
<style scoped>
.reward-item {
    background: #CF8E61;
    border: #D8BEAF calc(var(--base-unit) * 1) dashed;
}
</style>