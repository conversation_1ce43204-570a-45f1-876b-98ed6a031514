<template>
  <div class="target-box">
    <slot />
  </div>
</template>

<script setup lang="ts">
// No props needed for now; content is passed via slot
</script>

<style scoped>
  .target-box {
    display: flex;
    align-items: center;
    width: calc(var(--base-unit) * 65);
    height: calc(var(--base-unit) * 45);
    padding: calc(var(--base-unit) * 4);
    background-color: #F0DED4;
    border-radius: calc(var(--base-unit) * 4);
    border: dashed #D8BEAF calc(var(--base-unit) * 1);
    color: #83624F;
    font-size: calc(var(--base-unit) * 10);
    line-height: calc(var(--base-unit) * 14);
    text-align: center;
  }
  
</style>
