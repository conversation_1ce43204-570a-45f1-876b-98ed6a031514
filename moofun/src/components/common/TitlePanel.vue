<template>
  <div class="banner-wrapper">
    <div class="banner-container" 
         :style="{ 
           backgroundColor: backgroundColor,
           boxShadow: `0 calc(var(--base-unit) * -4) calc(var(--base-unit) * 2) ${shadowColor} inset`,
           border: `calc(var(--base-unit) * 2) solid ${outlineColor}`
         }">
      <div class="banner-content" :style="{ 
           webkitTextStroke: `calc(var(--base-unit) * 2) ${shadowColor}`
         }">

          <LeafIcon v-if="showLeaves" class="leaf-icon" />
          <div class="banner-text"><slot></slot></div>
          <LeafIcon v-if="showLeaves" class="leaf-icon leaf-icon-right" />
      </div>
    </div>
  </div>
</template>

<script>
import LeafIcon from '@/components/common/LeafIcon.vue'

export default {
  components: {
    LeafIcon
  },
  name: 'TitlePanel',
  props: {
    backgroundColor: {
      type: String,
      default: 'rgba(99, 197, 25, 1)' // Default green background
    },
    outlineColor: {
      type: String,
      default: 'rgba(255, 255, 255, 1)' // Default white outline
    },
    shadowColor: {
      type: String,
      default: 'rgba(206, 249, 174, 1)' // Default light green shadow
    },
    textColor: {
      type: String,
      default: 'rgba(255, 255, 255, 1)' // Default white text
    },
    leafColor: {
      type: String,
      default: 'rgba(255, 255, 255, 0.5)' // Default leaf icon color
    },
    showLeaves: {
      type: Boolean,
      default: true
    }
  }
}
</script>

<style scoped>
.banner-wrapper {
  position: relative;
  height: calc(var(--base-unit) * 42);
}

.banner-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  border-radius: calc(var(--base-unit) * 16);
}

.banner-content {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;
  color: v-bind(textColor);
  font-size: calc(var(--base-unit) * 20);
  -webkit-text-stroke: 0;
  text-shadow: none;
  padding: 0 calc(var(--base-unit) * 16);
  box-sizing: border-box;

}

.banner-text {
  position: absolute;
  z-index: 1;
  width: 100%;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  white-space: nowrap;
  overflow: visible;
  text-overflow: unset;
}

.leaf-icon {
  width: calc(var(--base-unit) * 32);
  height: calc(var(--base-unit) * 24);
  color: v-bind(leafColor);
}

.leaf-icon-right {
  transform: scaleX(-1);
}
</style>