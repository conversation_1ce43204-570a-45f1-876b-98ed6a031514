<template>
    <TitlePanel
      :backgroundColor="colorSet.background"
      :outlineColor="colorSet.outline"
      :shadowColor="colorSet.shadow"
      :textColor="colorSet.text"
      :leafColor="colorSet.leaf"
      :showLeaves="false"
    >
      <slot></slot>
    </TitlePanel>
  </template>
  
  <script>
import TitlePanel  from '../TitlePanel.vue';
  
  const COLOR_SETS = {
    green: {
      background: 'rgba(114, 221, 32, 1)',
      outline: 'rgba(66, 131, 16, 1)',
      shadow: 'rgba(64, 127, 16, 1)'
    },
    blue: {
      background: 'rgba(25, 118, 210, 1)',
      outline: 'rgba(255, 255, 255, 1)',
      shadow: 'rgba(173, 216, 230, 1)'
    },
    purple: {
      background: 'rgba(134, 139, 255, 1)',
      outline: 'rgba(69, 61, 211, 1)',
      shadow: 'rgba(86, 80, 232, 1)'
    },
    orange: {
      background: 'rgba(252, 96, 47, 1)',
      outline: 'rgba(228, 64, 12, 1)',
      shadow: 'rgba(218, 55, 3, 1)'
    },
    red: {
      background: 'rgba(244, 67, 54, 1)',
      outline: 'rgba(255, 255, 255, 1)',
      shadow: 'rgba(255, 205, 210, 1)'
    },
    yellow: {
      background: 'rgba(253, 200, 68, 1)',
      outline: 'rgba(241, 141, 57, 1)',
      shadow: 'rgba(254, 142, 42, 1)'
    },
    brown:{
      background:'rgba(192, 135, 74, 1)',
      outline:'rgba(166, 100, 52, 1)',
      shadow:'rgba(126, 78, 43, 0.5)',
      text: 'rgba(255, 239, 189, 1)',
      leaf: 'rgba(227, 181, 122, 1)'
    },
    brown2:{
      background: 'rgba(111, 66, 52, 1)',
      outline: 'rgba(166, 100, 52, 1)',
      shadow: 'rgba(142, 101, 70, 1)',
      text: 'rgba(255, 239, 189, 1)',
      leaf: 'rgba(227, 181, 122, 1)'
    }
  }
  
  export default {
    name: 'TitlePanelColor',
    components: {
      TitlePanel
    },
    props: {
      variant: {
        type: String,
        default: 'green',
        validator: (value) => Object.keys(COLOR_SETS).includes(value)
      },
      showLeaves: {
        type: Boolean,
        default: false
      }
    },
    computed: {
      colorSet() {
        return {
          background: COLOR_SETS[this.variant].background,
          outline: COLOR_SETS[this.variant].outline,
          shadow: COLOR_SETS[this.variant].shadow,
          text: COLOR_SETS[this.variant].text,
          leaf: COLOR_SETS[this.variant].leaf
        }
      }
    }
  }
  </script>