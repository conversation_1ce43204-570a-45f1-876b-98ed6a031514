<template>
    <footer class="version-container">
      <span class="version-text">v{{ version }}</span>
    </footer>
  </template>
  
  <script>
  export default {
    name: 'VersionText',
    data() {
      return {
        version: __APP_VERSION__,
      }
    },
  }
  </script>
  
  <style scoped>
  .version-container {
    padding: calc(var(--base-unit) * 2) calc(var(--base-unit) * 4);
    float: right;
  }

  .version-text {
    color: white;
    font-family: sans-serif;
    font-size: calc(var(--base-unit) * 10);
    font-weight: 500;
    opacity: 1;
  }
  </style>
  