import { pharosTestnet } from './pharosTestnet'
import { pharosMainnet } from './pharosMainnet'

const getDefaultNetwork = () => {
  const networkType = import.meta.env.VITE_DEFAULT_NETWORK || 'pharos-testnet'
  
  switch (networkType.toLowerCase()) {
    case 'pharos-mainnet':
      return pharosMainnet
    case 'pharos-testnet':
      return pharosTestnet
  }
}

export const defaultNetwork = getDefaultNetwork()
export { pharosTestnet, pharosMainnet } 