import { define<PERSON>hain } from '@reown/appkit/networks'

export const pharosTestnet = defineChain({
  id: 688688,
  name: 'Pharos Testnet',
  nativeCurrency: { 
    name: '<PERSON><PERSON><PERSON>', 
    symbol: 'PHRS', 
    decimals: 18 
  },
  rpcUrls: {
    default: {
      http: ['https://testnet.dplabs-internal.com'],
    },
    public: {
      http: ['https://testnet.dplabs-internal.com'],
    },
  },
  blockExplorers: {
    default: {
      name: 'PharosScan',
      url: 'https://testnet.pharosscan.xyz',
    },
  },
}) 