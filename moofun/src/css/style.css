@font-face {
    font-family: 'Goldman Sans';
    src: url('./fonts/GoldmanSans_Blk.ttf') format('truetype');
    font-weight: 900;
    font-style: normal;
}


:root {
    --vh: 1vh;
    --app-width: 100vw;
    --app-height: 100vh;
    /* Safari兼容性：简化base-unit计算 */
    --base-unit: calc(100vw / 375);
}

@media (min-width: 600px) {
    :root {
        /* Safari兼容性：使用条件判断替代min()函数 */
        --app-width: 375px;
        /* Safari兼容性：为固定宽度提供固定的base-unit */
        --base-unit: 1px;
    }
}

/* Safari兼容性：为不支持min()的浏览器提供fallback */
@supports not (width: min(100vw, 375px)) {
    @media (min-width: 600px) {
        :root {
            --app-width: 375px;
            --base-unit: 1px;
        }
    }
}

/* Safari兼容性：为不支持CSS变量的浏览器提供fallback */
@supports not (--css: variables) {
    body {
        font-size: 16px;
    }

    .main-text {
        -webkit-text-stroke: 2px rgba(59, 59, 59, 1);
        text-shadow: 0 1px 2px rgba(59, 59, 59, 1);
    }
}

    /* Rammetto One /
    / global-styles.css or font-style.css */
    body {
        font-family: 'Urbanist', sans-serif;
        font-size: calc(var(--base-unit) * 16);
        font-weight: 900;
        line-height: 1;
        /* Safari兼容性：移除paint-order，使用传统方法 */
        /* paint-order: stroke fill; */
    }

    .main-text {
        color: #FFFFFF;

        -webkit-text-stroke: calc(var(--base-unit) * 2) rgba(59, 59, 59, 1);
        text-shadow: 0 calc(var(--base-unit) * 1) calc(var(--base-unit) * 2) rgba(59, 59, 59, 1);
    }

    body {
        background: #000000;
        margin: 0;
        padding: 0;
    }

    .main-text {
        color: #FFFFFF;
        font-weight: 600;
        /* -webkit-text-stroke: calc(var(--base-unit) * 2) #090909; */
        /* text-shadow: 0 calc(var(--base-unit) * 1) calc(var(--base-unit) * 2) rgba(0, 0, 0, 0.75); */
    }

    /* Add a universal selector to inherit text styles */
    * {
        font-family: inherit;
        font-size: inherit;
        font-weight: inherit;
        line-height: inherit;
        letter-spacing: inherit;
        color: inherit;
        /* Safari兼容性：移除paint-order继承 */
        /* paint-order: inherit; */
    }

    .flex-center {
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
    }