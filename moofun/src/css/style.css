@font-face {
    font-family: 'Goldman Sans';
    src: url('./fonts/GoldmanSans_Blk.ttf') format('truetype');
    font-weight: 900;
    font-style: normal;
}


:root {
    --vh: 1vh; 
    --app-width: 100vw;
    --app-height: 100vh; 
    --base-unit: calc(var(--app-width) / 375); 
}

@media (min-width: 600px) {
    :root {
        --app-width: min(100vw, 375px);
    }
}

    /* Rammetto One /
    / global-styles.css or font-style.css */
    body {
        font-family: 'Urbanist', sans-serif;
        font-size: calc(var(--base-unit) * 16);
        font-weight: 900;
        line-height: 1;
        paint-order: stroke fill;
    }

    .main-text {
        color: #FFFFFF;

        -webkit-text-stroke: calc(var(--base-unit) * 2) rgba(59, 59, 59, 1);
        text-shadow: 0 calc(var(--base-unit) * 1) calc(var(--base-unit) * 2) rgba(59, 59, 59, 1);
    }

    body {
        background: #000000;
        margin: 0;
        padding: 0;
    }

    .main-text {
        color: #FFFFFF;
        font-weight: 600;
        /* -webkit-text-stroke: calc(var(--base-unit) * 2) #090909; */
        /* text-shadow: 0 calc(var(--base-unit) * 1) calc(var(--base-unit) * 2) rgba(0, 0, 0, 0.75); */
    }

    /* Add a universal selector to inherit text styles */
    * {
        font-family: inherit;
        font-size: inherit;
        font-weight: inherit;
        line-height: inherit;
        letter-spacing: inherit;
        color: inherit;
        paint-order: inherit;

    }

    .flex-center {
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
    }