<script lang="ts" setup>
import { onMounted, watch } from 'vue'
import { useWalletAuth2 } from "../../composables/useWalletAuth2"
import { useUser } from "../../composables/useUser"
import audioService from '@/lib/audioService'
import { useI18n } from 'vue-i18n'
import { useAppKit, useAppKitEvents, useAppKitAccount } from '@reown/appkit/vue'
import { useRouter } from 'vue-router'



const { t } = useI18n()
const { user, loadUser } = useUser()
const { processConnectSuccess, loadConnection, isConnected, disconnect, processShareCodes } = useWalletAuth2()
const { open } = useAppKit()
const accountData = useAppKitAccount();
const router = useRouter()

watch(isConnected, (newIsConnected) => {
  if (newIsConnected) {
    handleSuccessfulConnect();
  }
});

const handleSuccessfulConnect = async () => {
  await loadUser();
  await processShareCodes();
  if (user.value) {
    router.push('/v1');
  }
};


// connect
const connectWallet = () => {
  if (isConnected.value)return;
  if(accountData.value.address)
  {
    processConnectSuccess(accountData.value.address)
  }
  audioService.play('button1'); // Play sound effect
  open()
};

const events = useAppKitEvents();
watch(
  () => events.data.event,
  (eventName) => {
    console.log('AppKit event received:', eventName, 'timestamp:', events.timestamp)

    if (eventName === 'CONNECT_SUCCESS') {
      processConnectSuccess(accountData.value.address)
    }
  }
)


onMounted(async () => {
  await loadConnection()
  if(!isConnected.value) {
    await disconnect()
  }
})
</script>

<template>
  <div class="connect-button-container">
    <button class="connect-button" :disabled="isConnected" @click="connectWallet">
      <span class="connect-text">
        {{ accountData.address ? accountData.address?.slice(0, 6) + '...' + accountData.address?.slice(-4) : t('wallet.connect') }}
      </span>
    </button>
  </div>
</template>

<style scoped>
.connect-button {
  min-width: calc(var(--base-unit) * 280);
  height: calc(var(--base-unit) * 60);
  border-radius: 15px;
  background-color: rgba(255, 170, 24, 1);
  color: #FFFFFF;
  border: calc(var(--base-unit) * 1) solid rgba(10, 13, 18, 0.18);
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 calc(var(--base-unit) * 20);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  transition: background-color 0.3s ease, opacity 0.3s ease;
}

.connect-button:hover:not(:disabled) {
  background-color: rgba(255, 150, 0, 1);
}

.connect-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.connect-text {
  font-size: calc(var(--base-unit) * 20);
  font-weight: 600;
}
</style>
