<script setup>
import { onMounted, watch } from 'vue'
import { useWalletAuth } from "../../composables/useWalletAuth"
import { useUser } from "../../composables/useUser"
import audioService from '@/lib/audioService'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

const { t } = useI18n()
const { user, loadUser } = useUser()

const { connect, loadConnection, isConnected, processShareCodes } = useWalletAuth()
const router = useRouter()

watch(isConnected, (newIsConnected) => {
  if (newIsConnected) {
    handleSuccessfulConnect();
  }
});

const handleSuccessfulConnect = async () => {
  await loadUser();
  await processShareCodes();
  if (user.value) {
    router.push('/v1');
  }
};
// connect
const connectWallet = () => {
  if (isConnected.value) return
  audioService.play('button1'); // Play sound effect
  connect();
};

onMounted(async () => {
  await loadConnection()
})
</script>

<template>
  <div class="connect-button-container">
    <button class="connect-button" :disabled="isConnected" @click="connectWallet">
      <img v-if="!isConnected" src="/icon/minidapp.png" alt="icon" class="connect-icon" />
      <span class="connect-text">
        {{ isConnected ? user?.walletAddress?.slice(0, 6) + '...' + user?.walletAddress?.slice(-4) : t('wallet.connect') }}
      </span>
    </button>
  </div>
</template>

<style scoped>
.connect-button {
  min-width: calc(var(--base-unit) * 280);
  height: calc(var(--base-unit) * 60);
  border-radius: 15px;
  background-color: #06C755;
  color: #FFFFFF;
  border: none;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 calc(var(--base-unit) * 20);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  transition: background-color 0.3s ease, opacity 0.3s ease;
}

.connect-button:hover:not(:disabled) {
  background-color: #05a546;
}

.connect-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.connect-icon {
  width: calc(var(--base-unit) * 26);
  height: calc(var(--base-unit) * 26);
  margin-right: calc(var(--base-unit) * 12);
  filter: brightness(0) invert(1);
}

.connect-text {
  font-size: calc(var(--base-unit) * 20);
  font-weight: 600;
}
</style>
