import { watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAppKitEvents, useAppKitNetwork } from '@reown/appkit/vue'
import { useWalletAuth2 } from './useWalletAuth2'
import { useI18n } from 'vue-i18n'
import { useNotificationStore } from '@/features/notification'
import { defaultNetwork } from '@/config/networks'

export const useNetworkSwitchHandler = () => {
  const router = useRouter()
  const { disconnect } = useWalletAuth2()
  const { t } = useI18n()
  const notificationStore = useNotificationStore()
  const events = useAppKitEvents()
  const { switchNetwork, addNetwork } = useAppKitNetwork(defaultNetwork)

  const handleNetworkEvents = () => {
    watch(
      () => events.data.event,
      (eventName) => {
        console.log('Network event received:', eventName, 'timestamp:', events.timestamp)

        if (eventName === 'DISCONNECT_SUCCESS' || eventName === 'SWITCH_NETWORK_DISCONNECT') {
          handleDisconnect()
        }

        if (eventName === 'SWITCH_NETWORK_ERROR') {
          handleNetworkSwitchError()
        }

        if (eventName === 'ADD_NETWORK_SUCCESS') {
          handleNetworkAddSuccess()
        }

        if (eventName === 'ADD_NETWORK_ERROR') {
          handleNetworkAddError()
        }
      }
    )
  }

  const handleDisconnect = async () => {
    try {
      console.log('Handling disconnect from network switch modal')
      
      await disconnect()
      
      notificationStore.addNotification({
        type: 'info',
        message: t('wallet.disconnected'),
        duration: 2000
      })
      
      router.push('/authentication')
      
    } catch (error) {
      console.error('Error handling disconnect:', error)
    }
  }

  const handleNetworkSwitchError = () => {
    console.log('Network switch failed, user may have clicked disconnect')
    notificationStore.addNotification({
      type: 'error',
      message: t('wallet.networkSwitchFailed'),
      duration: 3000
    })
  }

  const handleNetworkAddSuccess = () => {
    console.log('Network added successfully')
    notificationStore.addNotification({
      type: 'success',
      message: t('wallet.networkAddedSuccess'),
      duration: 2000
    })
  }

  // 新增：处理网络添加失败
  const handleNetworkAddError = () => {
    console.log('Network add failed')
    notificationStore.addNotification({
      type: 'error',
      message: t('wallet.networkAddFailed'),
      duration: 3000
    })
  }

  // 新增：自动添加网络的方法
  const autoAddNetwork = async () => {
    try {
      console.log('Attempting to add network:', defaultNetwork.name)
      await addNetwork()
      return true
    } catch (error) {
      console.error('Failed to add network:', error)
      return false
    }
  }

  return {
    handleNetworkEvents,
    handleDisconnect,
    handleNetworkSwitchError,
    handleNetworkAddSuccess,
    handleNetworkAddError,
    autoAddNetwork
  }
} 