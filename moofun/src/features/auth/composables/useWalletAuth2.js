// useWalletAuth.js
import { ref } from 'vue'
import { getAuthMessage, verifySignature } from '../api'
import { useToken } from './useToken'
import { useUser } from './useUser'
import { useAppKitWallet } from './useAppKitWallet'
import { useReferralBinding } from '@/features/referral'
import { useBoostLink } from '@/features/countdownChest'
import { useShopStore } from '@/features/iap'
import { devLog } from '@/utils/config'
import { useAppKit, useAppKitEvents, useWalletInfo, useAppKitAccount, useDisconnect, useAppKitNetwork } from '@reown/appkit/vue'
import { defaultNetwork } from '@/config/networks'


const isConnected = ref(false)
const isProcessingCodes = ref(false)

export const useWalletAuth2 = () => {
  const { token, isTokenExpired, loadToken, setToken, clearToken } = useToken()
  const { user, loadUser, isWalletMatch, setUser, remove } = useUser()
  const { signMessage } = useAppKitWallet()
  const { bindCode: bindReferralCode } = useReferralBinding()
  const { processBoostCode } = useBoostLink()
  const shopStore = useShopStore()
  const accountData = useAppKitAccount();
  const { switchNetwork } = useAppKitNetwork(defaultNetwork);

  const loadConnection = async () => {
    // Wait for AppKit to initialize
    await new Promise(resolve => setTimeout(resolve, 1000))

    loadToken()
    loadUser()

    console.log('loadConnection', token.value, isTokenExpired(token.value), user.value, accountData.value)
    
    // if there's no token or token is expired or user stored in local storage 
    if (!token.value || isTokenExpired(token.value) || !user.value) {
      isConnected.value = false
      return false
    }

    try {
      const wallet = accountData.value.address
      const match = isWalletMatch(wallet)
      isConnected.value = !!(wallet && match)
      if (isConnected.value) {
        try {
          await shopStore.initializeShop()
        } catch (error) {
          console.error('Failed to load shop data on reconnection:', error)
        }
      }

      return isConnected.value
    } catch {
      isConnected.value = false
      return false
    }
  }

  const processShareCodes = async () => {
    if (isProcessingCodes.value) return
    isProcessingCodes.value = true

    try {
      // Get URL parameters
      const urlParams = new URLSearchParams(window.location.search)
      const refParam = urlParams.get('ref')

      if (refParam) {
        devLog('refParam', refParam)
        // Check if it's a referral code (starts with r_)
        if (refParam.startsWith('r_')) {
          const referralCode = refParam.substring(2) // Remove 'r_' prefix
          try {
            await bindReferralCode(referralCode)
            devLog('Referral code processed:', referralCode)
          } catch (error) {
            console.error('Failed to process referral code:', error)
          }
        }
        // Check if it's a boost code (starts with b_)
        else if (refParam.startsWith('b_')) {
          const boostCode = refParam.substring(2) // Remove 'b_' prefix
          try {
            await processBoostCode(boostCode)
            devLog('Boost code processed:', boostCode)
          } catch (error) {
            console.error('Failed to process boost code:', error)
          }
        }
        // Handle legacy referral codes without prefix
        else {
          try {
            await bindReferralCode(refParam)
            devLog('Legacy referral code processed:', refParam)
          } catch (error) {
            console.error('Failed to process legacy referral code:', error)
          }
        }

        // Remove the ref parameter after processing
        urlParams.delete('ref')
      }

      // Update URL without the processed parameters
      const newUrl = window.location.pathname + (urlParams.toString() ? `?${urlParams.toString()}` : '')
      window.history.replaceState({}, '', newUrl)
    } finally {
      isProcessingCodes.value = false
    }
  }
  const processConnectSuccess = async (wallet) => {
    const { message } = await getAuthMessage(wallet)
    const signature = await signMessage(message, wallet)
    console.log('signature', signature)
    const { token: newToken, user: newUser } = await verifySignature(wallet, message, signature)
    console.log('newToken', newToken)
    setToken(newToken)
    setUser(newUser)
    isConnected.value = true

    devLog("authenticated: ", { address: wallet, token: newToken, user: newUser })

    // Process share codes after successful connection
    await processShareCodes()

    // 🎯 自动初始化商店数据
    try {
      await shopStore.initializeShop()
      devLog('Shop data loaded after wallet connection')
    } catch (error) {
      console.error('Failed to load shop data after wallet connection:', error)
    }

    return { address: wallet, token: newToken, user: newUser }
  }

  const disconnect = async () => {
    clearToken()
    remove()
    isConnected.value = false
    const disconnect = useDisconnect();
    await disconnect.disconnect();
    
    try {
      shopStore.resetStore()
    } catch (error) {
      console.error('Failed to clear shop data after disconnection:', error)
    }
  }

  return {
    loadConnection,
    processConnectSuccess,
    disconnect,
    isConnected,
    processShareCodes
  }
}
