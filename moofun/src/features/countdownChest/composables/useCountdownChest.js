// features/countdownChest/composables/useCountdownChest.js
import { ref, computed, onUnmounted } from 'vue'
import { fetchCountdownChest, accelerateCountdownChest, collectCountdownChest } from '@/features/countdownChest'
import { useChestOverlayStore } from '@/features/openChest'
import { devLog } from '@/utils/config'

const countdownData = ref(null)
const remaining = ref({
  total: 0,
  hours: 0,
  minutes: 0,
  seconds: 0
})

const initialDurationMs = 86400000 // 24h in ms
const lastAcceleratedSeconds = ref(0)

let timer = null

export const useCountdownChest = () => {
  const chestOverlayStore = useChestOverlayStore();

  const fetchData = async () => {
    const { data } = await fetchCountdownChest()
    countdownData.value = data
    updateRemaining(data.remainingTime?.total || 0)
    startTimer()
  }

  const updateRemaining = (totalMilliseconds) => {
    remaining.value.total = totalMilliseconds
    const totalSeconds = Math.floor(totalMilliseconds / 1000)
    remaining.value.hours = Math.floor(totalSeconds / 3600)
    remaining.value.minutes = Math.floor((totalSeconds % 3600) / 60)
    remaining.value.seconds = totalSeconds % 60
  }

  const openChest = async () => {
    devLog('OPENING CHEST...')
    const { data } = await collectCountdownChest()
    devLog('Chest opened!', data)
    chestOverlayStore.openChest({ ok: true, data: data.result })
    await fetchData()
  }

  const accelerate = async () => {
    const randomSeconds = parseFloat((Math.random() * (2 - 0.1) + 0.1).toFixed(2))
    lastAcceleratedSeconds.value = randomSeconds
    await accelerateCountdownChest(randomSeconds)
    await fetchData()
  }

  const tick = () => {
    if (remaining.value.total > 0) {
      updateRemaining(remaining.value.total - 1000)
    }
  }

  const startTimer = () => {
    if (timer) clearInterval(timer)
    timer = setInterval(tick, 1000)
  }

  const progressPercent = computed(() => {
    const remainingTime = remaining.value?.total ?? initialDurationMs
    return Math.min(Math.max(1 - remainingTime / initialDurationMs, 0), 1)
  })

  const isReadyToOpen = computed(() => progressPercent.value >= 1)

  const chestImage = computed(() => {
    const percent = progressPercent.value
    if (percent >= 1) return '/assets/countdownChest/countdown-chest-4.png'
    if (percent >= 0.75) return '/assets/countdownChest/countdown-chest-3.png'
    if (percent >= 0.25) return '/assets/countdownChest/countdown-chest-2.png'
    return '/assets/countdownChest/countdown-chest-1.png'
  })

  const chestTierClass = computed(() => {
    const percent = progressPercent.value
    if (percent >= 1) return 'pulse'
    if (percent >= 0.75) return 'jiggle-3'
    if (percent >= 0.25) return 'jiggle-2'
    return 'jiggle-1'
  })

  const formatTime = (t) =>
    `${String(t.hours).padStart(2, '0')}:${String(t.minutes).padStart(2, '0')}:${String(t.seconds).padStart(2, '0')}`

  onUnmounted(() => {
    clearInterval(timer)
  })

  return {
    countdownData,
    remaining,
    fetchData,
    openChest,
    accelerate,
    initialDurationMs,
    lastAcceleratedSeconds,

    // derived values
    isReadyToOpen,
    progressPercent,
    chestImage,
    chestTierClass,
    formatTime
  }
}
