<script setup>
import { useGameMenuItems } from '../../composables/menuItems'
import { useRouter } from 'vue-router'
import audioService from '@/lib/audioService'

const router = useRouter()
const gameMenuItems = useGameMenuItems()

function navigate(path) {
  audioService.play('button1')
  router.push(path)
}
</script>

<template>
  <div class="game-menu">
    <div
      v-for="item in gameMenuItems"
      :key="item.id"
      class="menu-item"
      :class="{ selected: router.path === item.path }"
      @click="navigate(item.path)"
    >
      <div class="icon-container">
        <img :src="item.icon" :alt="$t(`menu.${item.name.toLowerCase()}`)" />
        <p class="item-text">{{ $t(`menu.${item.name.toLowerCase()}`) }}</p>
      </div>
    </div>
  </div>
</template>


<style scoped>
.game-menu {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 100%;
  height: calc(var(--base-unit) * 76);
  bottom: 0;
  position: fixed;
  z-index: 100;

  /* Add borders on top and bottom */
  border-top: calc(var(--base-unit) * 1) solid #222835;
  border-bottom: calc(var(--base-unit) * 1) solid #222835;
  overflow: hidden;
  background: #55413A;
}

.menu-item {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #55413A;
  display: flex;
  align-items: start;
  justify-content: center;
  position: relative; /* Ensure proper positioning for icon and text */
  overflow: visible; /* Allow overflow to show outside */
}

/* Selected item will turn red with gradient background */
.menu-item.selected {
  background: linear-gradient(180deg, #374056 28%, #46516D 52.5%, #374056 85%);

}

/* Icon container to handle icon and text */
.icon-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease-out;
}

/* Move icon up when selected */
.menu-item.selected .icon-container {
  transform: translateY(calc(var(--base-unit) * -32));
}

/* Icon styling */
.menu-item img {
  width: calc(var(--base-unit) * 58);
  height: calc(var(--base-unit) * 58);
  object-fit: cover;
  transition: transform 0.2s ease-out;
}

.menu-item.selected .item-text {
  margin-top: calc(var(--base-unit) * 8); /* Space between icon and text */
}

/* Text styling */
.item-text {
  font-size: calc(var(--base-unit) * 14);
  color: #fff; /* White text */
  text-align: center;
  margin-top: calc(var(--base-unit) * 0); /* Space between icon and text */
  transition: margin-top 0.2s ease-out;
}
</style>