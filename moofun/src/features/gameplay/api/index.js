import fetch from '@/lib/fetch'

export const getDeliveryLine = () => {
  return fetch.get('/delivery/delivery-line').then(res => res.data)
}

export const upgradeDeliveryLine = () => {
  return fetch.post('/delivery/delivery-line/upgrade').then(res => res.data)
}

// Farm plots related endpoints
export const getFarmPlots = () => {
  return fetch.get('/farm/farm-plots').then(res => res.data)
}

export const upgradeFarmPlot = (plotNumber) => {
  return fetch.post('/farm/farm-plots/upgrade', { plotNumber }).then(res => res.data)
}

export const unlockFarmPlot = (plotNumber) => {
  return fetch.post('/farm/farm-plots/unlock', { plotNumber }).then(res => res.data)
}

// Wallet related endpoints
export const increaseMilk = (pendingMilk) => {
  return fetch.post('/wallet/increase-milk', { pendingMilk }).then(res => res.data)
}

export const increaseGem = (milkAmount) => {
  return fetch.post('/wallet/increase-gem', { milkAmount }).then(res => res.data)
}

export const batchUpdateResources = (gemRequest, milkOperations) => {
  return fetch.post('/wallet/batch-update-resources', { gemRequest, milkOperations });
}