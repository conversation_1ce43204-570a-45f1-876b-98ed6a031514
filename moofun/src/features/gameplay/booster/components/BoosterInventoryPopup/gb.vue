<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { TitlePanelColor } from '@/components/common'
import { BoosterItem, BoosterLabel, BoosterTimeRemaining } from '@/features/gameplay/booster'
import { useBoosterInventoryPopupStore } from '../../stores/boosterInventoryPopupStore'
import { useBoosterStore } from '../../stores/boosterStore'
import audioService from '@/lib/audioService'
import { useBooster } from '@/features/iap'
import { useI18n } from 'vue-i18n'
import { devLog } from '@/utils/config'
import { BoosterPopup } from '@/features/gameplay/booster'


const { t } = useI18n()
const router = useRouter()

function goToIapShop() {
  audioService.play('button1')
  router.push("v1/shop")
}

const boosterInventoryPopupStore = useBoosterInventoryPopupStore()
const boosterStore = useBoosterStore()

const getBoosterImagePath = (productId: number): string => {
  const boosterImagePaths = {
    1: '/assets/booster/speed-boost-x2.png',
    2: '/assets/booster/speed-boost-x2.png',
    3: '/assets/booster/speed-boost-x4.png',
    4: '/assets/booster/speed-boost-x4.png',
    5: '/assets/booster/time-warp-1hr.png',
    6: '/assets/booster/time-warp-6hr.png',
    7: '/assets/booster/time-warp-24hr.png'
  }
  return boosterImagePaths[productId] || '/assets/booster/speed-boost-x2.png'
}

const handleUseBooster = async (boosterId: number) => {
  devLog('handleUseBooster', boosterId)
  await useBooster(boosterId)
  await boosterStore.refreshAll()
}

onMounted(async () => {
  await boosterStore.refreshAll()
})
</script>


<template>
  <BoosterPopup 
    class="booster-inventory-popup"
    :isOpen="boosterInventoryPopupStore.isBoosterInventoryShow"
    @close="boosterInventoryPopupStore.closeBoosterInventoryPopup"
  >
  <template #title>
    <TitlePanelColor variant="brown">{{ $t('common.boosterInventory') }}</TitlePanelColor>
  </template>
    <div class="booster-inventory-content">
      <!-- Active Boosters Section -->
      <div class="booster-grid">
        <div
          v-for="booster in boosterStore.activeBoosters"
          :key="booster.id"
          class="boosted-status"
        >
          <div class="booster-item-content">
            <img
              class="booster-item-icon"
              :src="getBoosterImagePath(booster.product.id)"
              alt="booster-item-icon"
            />
          </div>
          <BoosterTimeRemaining :endTime="booster.endTime" />
        </div>
      </div>

      <!-- Inventory Section -->
      <BoosterLabel> {{ $t('common.inventory') }} </BoosterLabel>
      <div class="booster-grid">
        <BoosterItem
          :highlight="true"
          :labelText="$t('common.iapShop')"
          :iconSrc="'/assets/booster/iap-store.png'"
          :buttonText="$t('common.get')"
          @click="goToIapShop"
        >
        </BoosterItem>

        <BoosterItem
          v-for="booster in boosterStore.boosters"
          :key="booster.id"
          :highlight="false"
          :labelText="booster.product.duration + ' ' + $t('common.hr')"
          :iconSrc="getBoosterImagePath(booster.product.id)"
          :buttonText="$t('common.use')"
          @click="() => handleUseBooster(booster.id)"
        />
      </div>
    </div>
  </BoosterPopup>
</template>


<style scoped>
.title {
  margin-bottom: calc(var(--base-unit) * 12);
}

.booster-inventory-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: calc(var(--base-unit) * 12);
}

.booster-grid {
  border-radius: calc(var(--base-unit) * 8);
  display: flex;
  justify-content: left;
  align-items: center;
  width: 100%;
  min-height: calc(var(--base-unit) * 100);
  padding: calc(var(--base-unit) * 12);
  box-sizing: border-box;
  gap: calc(var(--base-unit) * 12);
  overflow-x: auto;
  flex-wrap: nowrap;
  /* Prevent content from shrinking */
  min-width: 0;

  background:#F7ECE6;
  background-size: cover;
  border: calc(var(--base-unit) * 2) dashed #D8BEAF;
}

.booster-grid :deep(.booster-item) {
  flex: 0 0 auto;
  min-width: calc(var(--base-unit) * 80);
}

.booster-item-label {
  color: #FDD99B;
  font-size: calc(var(--base-unit) * 12);
  padding: calc(var(--base-unit) * 4);
  text-shadow: none;
  text-align: center;
  margin-top: calc(var(--base-unit) * 4);
}

.booster-item-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.booster-item-icon {
  width: calc(var(--base-unit) * 64);
  height: calc(var(--base-unit) * 64);
  object-fit: contain;
  box-sizing: border-box;
}

.boosted-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}


</style>

