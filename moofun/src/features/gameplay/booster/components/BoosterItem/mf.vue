<script setup>
import { ColorButton } from '@/components/common';

defineProps({
  highlight: {
    type: Boolean,
    default: false
  },
  labelText: {
    type: String,
    default: '1 hr'
  },
  iconSrc: {
    type: String,
    default: '/public/assets/booster/speed-boost-x2.png'
  },
  buttonText: {
    type: String,
    default: 'Use'
  }
});
</script>

<template>
  <div class="booster-item"  :class="{ highlighted: highlight }">
    <div class="booster-item-label">{{ labelText }}</div>
    <div class="booster-item-content">
      <img class="booster-item-icon" :src="iconSrc" alt="booster-item-icon">
    </div>
    <div class="booster-item-button-container">
      <ColorButton class="booster-item-button" :padding="'calc(var(--base-unit) * 4) calc(var(--base-unit) * 18)'">{{ buttonText }}</ColorButton>
    </div>
  </div>
</template>

<style scoped>
.booster-item {
  background: #CF8E61;
  border: calc(var(--base-unit) * 3) solid #FDD99B;
  border-radius: calc(var(--base-unit) * 16);
  margin-bottom: calc(var(--base-unit) * 12);
  width: fit-content;
  position: relative;
}

.highlighted.booster-item {
  background: linear-gradient(180deg, #F59A13 0%, #A74B8D 100%);
  color: white;
}

.booster-item-label {
  color: #FDD99B;
  font-size: calc(var(--base-unit) * 12);
  padding: calc(var(--base-unit) * 8) calc(var(--base-unit) * 8) calc(var(--base-unit) * 4) calc(var(--base-unit) * 8);
  text-shadow: none;
  text-align: center;
  border-bottom: calc(var(--base-unit) * 3) solid #FDD99B;
  border-radius: calc(var(--base-unit) * 12) calc(var(--base-unit) * 12) 0 0;
}

.highlighted .booster-item-label {
  color: white;
  background-color: #1AA4E9;
}

.booster-item-content {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: calc(var(--base-unit) * 16);
}

.booster-item-icon {
  padding: calc(var(--base-unit) * 8);
  width: calc(var(--base-unit) * 64);
  height: calc(var(--base-unit) * 64);
  object-fit: contain;
  box-sizing: border-box;
}

.booster-item-button-container {
  position: absolute;
  width: 100%;
  bottom: calc(var(--base-unit) * -16);
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.booster-item-button {
  width: 100%;
  border: calc(var(--base-unit) * 2) solid #252635;
}
</style>
