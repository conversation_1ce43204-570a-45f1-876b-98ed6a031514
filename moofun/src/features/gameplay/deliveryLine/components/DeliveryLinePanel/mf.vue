<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ColorButton } from '@/components/common'
import { useBoosterInventoryPopupStore } from '@/features/gameplay/booster'
import { useDeliveryLineUpgradePopupStore } from '@/features/gameplay/deliveryLine'
import audioService from '@/lib/audioService'
import { useDeliveryLineStore } from '../../stores/deliveryLineStore'
import type { DeliveryLineStats } from '../../stores/deliveryLineStore'
import { useUserInfo } from '@/features/userInfo'
import { useThemeAssets } from '@/themes';

const theme = useThemeAssets()


const deliveryLineStore = useDeliveryLineStore()    
const boosterInventoryPopupStore = useBoosterInventoryPopupStore()
const deliveryLineUpgradePopupStore = useDeliveryLineUpgradePopupStore()
const { userInfo, fetchUserInfo } = useUserInfo();

const openBoosterInventoryPopup = () => {
  audioService.play('button1'); // Play sound effect
  boosterInventoryPopupStore.openBoosterInventoryPopup()
}

const openDeliveryLineUpgradePopup = () => {
  audioService.play('button1'); // Play sound effect
  deliveryLineUpgradePopupStore.openDeliveryLineUpgradePopup()
}

onMounted(async () => {
  await fetchUserInfo();
  await deliveryLineStore.fetchDeliveryLineFromApi()
})

function formatNumber(num: number): string {
  if (num < 1000) {
    if (num >= 100) return Math.round(num).toString();
    if (num >= 10) return (Math.round(num * 10) / 10).toString(); // e.g., 10.2
    if (num >= 1) return (Math.round(num * 100) / 100).toString(); // e.g., 1.25
    return (Math.round(num * 1000) / 1000).toString(); // e.g., 0.667
  }

  const units = ["k", "m", "b", "t"];
  let unitIndex = -1;

  while (num >= 1000 && unitIndex < units.length - 1) {
    num /= 1000;
    unitIndex++;
  }

  const rounded = num >= 100 ? Math.round(num) : Math.round(num * 10) / 10;

  return `${rounded}${units[unitIndex]}`;
}

</script>


<template>
<div class="delivery-line-panel">
    <div class="delivery-line-panel-left">
        <div class="pending-milk-container">
            <img :src="theme.product" alt="Milk" class="milk-icon">
            <span class="pending-milk-text">{{ formatNumber(Number(deliveryLineStore.deliveryLine?.pendingMilk || 0) + Number(deliveryLineStore.temporalStats.milkOperations.produce - deliveryLineStore.temporalStats.milkOperations.consume) || 0) }}</span>
        </div>
        <button class="speed-boost-button" @click="openBoosterInventoryPopup">
        <img 
            src="/public/assets/booster/speed-boost-x2.png" 
            alt="Speed Boost x2"
            class="speed-boost-icon"
        />
        </button>
    </div>

    <ColorButton    
    @click="openDeliveryLineUpgradePopup"
    :disabled="Number(userInfo?.gem || 0) < Number(deliveryLineStore.deliveryLine?.upgradeCost || 0)"         
    :padding="`calc(var(--base-unit) * 4) calc(var(--base-unit) * 12)`"
    class="delivery-line-button">
        {{ $t('common.upgrade') }}
    </ColorButton>
</div>
</template>


<style scoped>

.delivery-line-panel {
    width: 100%;
    height: calc(var(--base-unit) * 44);
    background: linear-gradient(90deg, #255E66 0%, #1B324A 100%);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: calc(var(--base-unit) * 12);
    box-sizing: border-box;
    gap: calc(var(--base-unit) * 12);

}

.delivery-line-panel-left {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: calc(var(--base-unit) * 12);
}

.pending-milk-container {
  display: flex;
  align-items: center;
  background: #262626;
  border: calc(var(--base-unit) * 1.5) solid #FDC844;
  border-radius: calc(var(--base-unit) * 8);
  padding: 0 calc(var(--base-unit) * 8);
  height: calc(var(--base-unit) * 28);
  min-width: calc(var(--base-unit) * 80);
  color: #ffffff;
  font-weight: 600;
  font-size: calc(var(--base-unit) * 12);
  box-shadow:
    0px 0px 0px 1px #0a0d122e inset,
    0px -2px 0px 0px #0a0d120d inset,
    0px 1px 2px 0px #1018280d;
}

.milk-icon {
  width: calc(var(--base-unit) * 18);
  height: calc(var(--base-unit) * 24);
  object-fit: contain;
  flex-shrink: 0;
}

.pending-milk-text {
  flex-grow: 1;
  text-align: center;
}


.milk-icon {
    transform: translateY(calc(var(--base-unit) * 2));
    width: calc(var(--base-unit) * 18);
    height: calc(var(--base-unit) * 24);
    object-fit: contain;
}

.speed-boost-button {
    width: calc(var(--base-unit) * 48);
    height: calc(var(--base-unit) * 48);
    padding: calc(var(--base-unit) * 8);
    background: transparent;
    border: none;
    transition: transform 0.1s ease-in-out;
}

.speed-boost-button:active {
    transform: scale(0.9);
}

.speed-boost-icon {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.delivery-line-button {
    padding: calc(var(--base-unit) * 2) calc(var(--base-unit) * 2);
    min-width: calc(var(--base-unit) * 80);
}




</style>
