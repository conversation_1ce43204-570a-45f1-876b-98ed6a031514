<script setup>
import { devLog } from '@/utils/config'
import { ref, onMounted } from 'vue'
import { TitlePanelColor } from '@/components/common'
import { BoosterPopup, BoosterLabel } from '@/features/gameplay/booster'
import { ColorButton } from '@/components/common'
import { useUserInfo } from '@/features/userInfo'

import { upgradeDeliveryLine } from '@/features/gameplay/api'
import audioService from '@/lib/audioService'
import { useI18n } from 'vue-i18n'
import { useDeliveryLineUpgradePopupStore } from '../../stores/deliveryLineUpgradePopup'
import { useDeliveryLineStore } from '../../stores/deliveryLineStore'
import { useThemeAssets } from '@/themes' 
const theme = useThemeAssets()


const { t } = useI18n()
const { userInfo, fetchUserInfo } = useUserInfo();

const deliveryLineUpgradePopupStore = useDeliveryLineUpgradePopupStore()
const deliveryLineStore = useDeliveryLineStore()

const isLoading = ref(false)

const handleUpgrade = async () => {
  if (isLoading.value) return
  audioService.play('button1'); // Play sound effect
  try {
    isLoading.value = true
    await upgradeDeliveryLine()
    
    // Refresh data after successful upgrade
    await Promise.all([
      fetchUserInfo(),
      deliveryLineStore.fetchDeliveryLineFromApi()
    ])
  } catch (error) {
    console.error('Failed to upgrade delivery line:', error)
  } finally {
    isLoading.value = false
  }
}

onMounted(async () => {
  await fetchUserInfo();
  await deliveryLineStore.fetchDeliveryLineFromApi()
  devLog("deliveryLine",deliveryLineStore.deliveryLine)
})

const getDeliverySpeed = () => {
  return deliveryLineStore.deliveryLine?.speedPercentage || 0
}

const getNextDeliverySpeedGrowth = () => {
  return deliveryLineStore.deliveryLine?.nextUpgradeGrowth?.nextSpeedPercentage || 0
}


</script>

<template>
  <BoosterPopup class="upgrade-popup" :isOpen="deliveryLineUpgradePopupStore.isDeliveryLineUpgradeShow" @close="deliveryLineUpgradePopupStore.closeDeliveryLineUpgradePopup">
    <div class="stage-title">{{ $t('deliveryLine.level') }} {{ deliveryLineStore.deliveryLine?.level }}</div>
    <div class="upgrade">
        <div class="upgrade-icon">
            <img class="icon-image" :src="theme.profitUpgrade" alt="milk-profit-icon">
        </div>
        <div class="upgrade-info">
            <div class="upgrade-title">{{ $t('common.milkProfit') }}</div>
            <div class="upgrade-stats">
              <div class="before">{{deliveryLineStore.deliveryLine?.blockPrice}}</div>
              <div class="after">+{{deliveryLineStore.deliveryLine?.nextUpgradeGrowth?.nextBlockPrice - deliveryLineStore.deliveryLine?.blockPrice}}</div>
            </div>
        </div>
    </div>
    <div class="upgrade">
        <div class="upgrade-icon">
            <img class="icon-image" :src="theme.deliverySpeedUpgrade" alt="production-speed-icon">
        </div>
        <div class="upgrade-info">
            <div class="upgrade-title">{{ $t('common.deliverySpeed') }}</div>
            <div class="upgrade-stats">
              <div class="before">{{getDeliverySpeed()}}%</div>
              <div class="after">+{{getNextDeliverySpeedGrowth()}}%</div>
            </div>
        </div>
    </div>
    <div class="upgrade">
        <div class="upgrade-icon">
            <img class="icon-image" :src="theme.capacityUpgrade" alt="milk-capacity-icon">
        </div>
        <div class="upgrade-info">
            <div class="upgrade-title">{{ $t('common.milkCapacity') }}</div>
            <div class="upgrade-stats">
              <div class="before">{{Number(deliveryLineStore.deliveryLine?.blockUnit)}}</div>
              <div class="after">+{{deliveryLineStore.deliveryLine?.nextUpgradeGrowth?.nextBlockUnit - deliveryLineStore.deliveryLine?.blockUnit}}</div>
            </div>
        </div>
    </div>
    <div class="button-group-container">
        <div class="button-container">  
            <div style="color: #E7D5CB;">{{ $t('common.upgrade') }}</div>
        </div>
        <ColorButton 
          id="delivery-line-upgrade-button"
          :disabled="userInfo.gem < Number(deliveryLineStore.deliveryLine?.upgradeCost) || isLoading" 
          :class="{ 'pulse-animation': !(userInfo.gem < Number(deliveryLineStore.deliveryLine?.upgradeCost)) }"
          @click="handleUpgrade"
        >
            <div class="button-container">
                <div >{{ isLoading ? $t('common.upgrading') : `${Number(deliveryLineStore.deliveryLine?.upgradeCost) || 0} ${$t('common.gems')}` }}</div>
            </div>
        </ColorButton>
    </div>
  </BoosterPopup>
</template>

<style scoped>
.upgrade-popup {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stage-title {
  font-size: calc(var(--base-unit) * 16);
  font-weight: 900;
  text-align: center;
  margin-bottom: calc(var(--base-unit) * 12);
  color: #68514B;
}

.upgrade {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: calc(var(--base-unit) * 12);
  margin-bottom: calc(var(--base-unit) * 12);

  border: calc(var(--base-unit) * 2) solid #E9EAEB;
  background-image: url('/ui/wooden-texture.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: calc(var(--base-unit) * 4) calc(var(--base-unit) * 14);
  border-radius: calc(var(--base-unit) * 12);
}

.upgrade-icon {
  background: #BF7E58;
  border: calc(var(--base-unit) * 2) solid #572918;
  width: calc(var(--base-unit) * 56);
  height: calc(var(--base-unit) * 56);
  border-radius: calc(var(--base-unit) * 12);
  box-sizing: border-box;

  display: flex;
  align-items: center;
  justify-content: center;
  padding: calc(var(--base-unit) * 12);
}

.icon-image {
  width: calc(var(--base-unit) * 56);
  height: calc(var(--base-unit) * 56);
  object-fit: contain;
}

.upgrade-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: calc(var(--base-unit) * 12) calc(var(--base-unit) * 12);
  width: 100%;
  height: calc(var(--base-unit) * 56);

  box-sizing: border-box;
  
  color:#E7D5CB;
  font-size: calc(var(--base-unit) * 14);
}

.upgrade-title {
  font-size: calc(var(--base-unit) * 16);
  font-weight: 600;
}

.upgrade-stats {
  display: flex;
  align-items: end;
  justify-content: space-between;
  flex-direction: column;
  gap: calc(var(--base-unit) * 4);
}

.before {
  color: #E7D5CB;
}

.after {
  color: #FDC844;
}

.button-group-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: calc(var(--base-unit) * 12);
  margin: 0 auto;

  border: calc(var(--base-unit) * 2) solid #E9EAEB;
  background-image: url('/ui/wooden-texture.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: calc(var(--base-unit) * 12) calc(var(--base-unit) * 12);
  border-radius: calc(var(--base-unit) * 12);
}

.button-icon {
  width: calc(var(--base-unit) * 24);
  height: calc(var(--base-unit) * 24);
  object-fit: contain;
}

.button-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: calc(var(--base-unit) * 8);
}

@keyframes pulse-scale {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.pulse-animation {
  animation: pulse-scale 2s ease-in-out infinite;
}
</style>

