import { Assets } from 'pixi.js';
import { GameObject, SpriteRendererComponent, Scene, Vector2 } from '@/features/gameplay/shared';
import { useThemeAssets } from '@/themes';

export class ConveyorGameObject extends GameObject {
  private spriteRenderer!: SpriteRendererComponent;
  private theme = useThemeAssets();
  constructor(scene: Scene) {
    super(scene);

    // Set layout via transform (relative to screen)
    this.transform.scale = new Vector2(1, 1);
    this.transform.position = new Vector2(0.5, 0.85);
  }

  async start(): Promise<void> {
    const texture = await Assets.load(this.theme.value.conveyor);

    this.spriteRenderer = new SpriteRendererComponent({
      texture,
      anchor: { x: 0.5, y: 0.5 },
    });

    this.addComponent(this.spriteRenderer);
  }

  protected onUpdate(deltaSeconds: number): void {
    // Optional: animate conveyor belt
  }

  get height(): number {
    return this.spriteRenderer.sprite.height;
  }
}
