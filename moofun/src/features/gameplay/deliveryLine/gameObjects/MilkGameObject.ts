import { Assets } from 'pixi.js';
import { SpriteRendererComponent, Scene, GameObject, Vector2 } from '@/features/gameplay/shared';
import { TweenBuilder, Easing } from '@/features/gameplay/animation';
import { useThemeAssets } from '@/themes';

export class MilkGameObject extends GameObject {
  private spriteRenderer!: SpriteRendererComponent;

  constructor(scene: Scene) {
    super(scene);

    // Layout configuration moved to transform
    this.transform.scale = new Vector2(0.05, 0.05);
  }

  async start(): Promise<void> {
    const theme = useThemeAssets()
    const texture = await Assets.load(theme.value.product);

    this.spriteRenderer = new SpriteRendererComponent({
      texture,
      anchor: { x: 0.5, y: 0.5 },
    });

    this.addComponent(this.spriteRenderer);
    this.spriteRenderer.render(); // apply initial layout

    const baseScale = new Vector2(0.08, 0.08);

    TweenBuilder
    .update(this.transform, (target, value) => {
      target.scale = Vector2.lerp(new Vector2(0.0, 0.0), baseScale.multiply(1.2), value);
    }, 0, 1, 0.1)
    .setEase(Easing.EaseOutQuad)
    .play();

  TweenBuilder
    .update(this.transform, (target, value) => {
      target.scale = Vector2.lerp(baseScale.multiply(1.2), baseScale, value);
    }, 0, 1, 0.2)
    .setDelay(0.2)
    .setEase(Easing.EaseInQuad)
    .play();
  }


  protected onUpdate(): void {
    // Animation or motion logic is handled by other components
  }
  
}
