<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useI18n } from 'vue-i18n';
import { useFarmStore } from '../../stores/farmPlotStore';
import { ColorButton } from '@/components/common';
import audioService from '@/lib/audioService';
import { useUserInfo } from '@/features/userInfo'

import type { FarmPlotStats } from '@/features/gameplay/farmPlot';
import { useFarmPlotUpgradePopupStore } from '../../stores/farmPlotUpgradePopupStore';
import { useFarmPlotUnlockPopupStore } from '../../stores/farmPlotUnlockPopupStore';
import { useFarmGroupScene } from '../../scenes/useFarmGroupScene';
import { useThemeAssets } from '@/themes';

const { t } = useI18n()

const farmPlotUpgradePopupStore = useFarmPlotUpgradePopupStore();
const farmPlotUnlockPopupStore = useFarmPlotUnlockPopupStore();
const openFarmPlotUpgradePopup = (plot: FarmPlotStats) => {
  audioService.play('button1');
  farmPlotUpgradePopupStore.openFarmPlotUpgradePopup(plot.plotNumber);
};

const openFarmPlotUnlockPopup = (plot: FarmPlotStats) => {
  audioService.play('button1');
  farmPlotUnlockPopupStore.openFarmPlotUnlockPopup(plot);
};

function formatNumber(num: number): string {
  if (num < 1000) {
    if (num >= 100) return Math.round(num).toString();
    if (num >= 10) return (Math.round(num * 10) / 10).toString(); // e.g., 10.2
    if (num >= 1) return (Math.round(num * 100) / 100).toString(); // e.g., 1.25
    return (Math.round(num * 1000) / 1000).toString(); // e.g., 0.667
  }

  const units = ["k", "m", "b", "t"];
  let unitIndex = -1;

  while (num >= 1000 && unitIndex < units.length - 1) {
    num /= 1000;
    unitIndex++;
  }

  const rounded = num >= 100 ? Math.round(num) : Math.round(num * 10) / 10;

  return `${rounded}${units[unitIndex]}`;
}


const canvasRef = ref<HTMLElement | null>(null);
const farmStore = useFarmStore();
const { plots } = storeToRefs(farmStore);
const PIXELS_PER_PLOT = 211;
const { userInfo, fetchUserInfo } = useUserInfo();

onMounted(async () => {
  await fetchUserInfo();
  await farmStore.setFarmPlotsFromApi();
  //farmStore.simulateUpdates();
  await useFarmGroupScene(canvasRef.value);
});

</script>


<template>
  <div class="farm-group" :style="{ height: 'calc(var(--base-unit) * ' + (PIXELS_PER_PLOT * plots.length) + ')' }">
    <!-- Left side milk bar -->
    <div class="milk-bar">
      <div v-for="i in (4 * plots.length)" :key="i" class="milk-indicator">
        <img :src="useThemeAssets().value.farmSide" alt="Egg" class="milk-image" />
      </div>
    </div>

    <!-- <TestBox /> -->
    <!-- Right side Pixi canvas -->
    <div class="pixi-container" ref="canvasRef" >
      <div class="overlay-layer"> 
        <div v-for="i in plots.length" 
        :key="i" class="farm-card-overlay"
        :style="{
          top: 'calc(var(--base-unit) * ' + ((i - 1) * PIXELS_PER_PLOT) + ')',
        }"
        >

            <div class="farm-card-information">
              <div v-if="plots[i - 1].isUnlocked" class="farm-card-text-title">{{ $t('common.level') }} {{ plots[i - 1].level }} : {{ formatNumber(plots[i - 1].milkProduction) }} /s </div>
              <div v-else class="farm-card-text-title locked"> {{ $t('farmPlot.plot') }} {{ plots[i - 1].plotNumber }} : {{ $t('farmPlot.locked') }}</div>
            </div>

            <ColorButton
            v-if="plots[i - 1].level === 50"
            :disabled="true"
            :padding="`calc(var(--base-unit) * 4) calc(var(--base-unit) * 14)`"
            class="plot-button"
            :id="'farm-plot-' + (i) + '-button'"
            @click="openFarmPlotUpgradePopup(plots[i - 1])"
          >
            {{ $t('farmPlot.maxed') }}
          </ColorButton>
            <ColorButton
            v-else-if="plots[i - 1].isUnlocked"
            :disabled="Number(userInfo.gem) < Number(plots[i - 1].upgradeCost)"
            :padding="`calc(var(--base-unit) * 4) calc(var(--base-unit) * 14)`"
            class="plot-button"
            :id="'farm-plot-' + (i) + '-button'"
            @click="openFarmPlotUpgradePopup(plots[i - 1])"
          >
            {{ $t('common.upgrade') }}
          </ColorButton>
          <ColorButton
            v-else-if="i !== 1 && plots[i - 2].isUnlocked"
            :disabled="Number(userInfo.gem) < Number(plots[i - 1].unlockCost)"
            :padding="`calc(var(--base-unit) * 4) calc(var(--base-unit) * 14)`"
            class="plot-button"
            :id="'farm-plot-' + (i) + '-button'"
            @click="openFarmPlotUnlockPopup(plots[i - 1])"
          >
            {{ $t('common.unlock') }}
          </ColorButton>
        </div>
      </div>
    </div>
  </div>
</template>


<style scoped>
.farm-group {
  position: relative;
  display: flex;
  width: 100%;
}

.pixi-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow-x: hidden; 
  overflow-y: hidden;
  background-color: black;
}

.overlay-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.farm-card-overlay {
  position: absolute;
  pointer-events: auto;
  z-index: 10;
  width: 100%;
  height: calc(var(--base-unit) * 60);
  background-color: rgba(0, 0, 0, 0);
  
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 calc(var(--base-unit) * 18);
  box-sizing: border-box;
}

.farm-card-text-title {
  font-size: calc(var(--base-unit) * 14);
  font-weight: 600;
  color: #E7D5CB;
}

.locked {
  color: #E7D5CB;
}

.milk-bar {
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-direction: column;
  background: #2b2b2b;
  width: calc(var(--base-unit) * 48);
  border: calc(var(--base-unit) * 4) solid #F5EBE5;
  background: #E7D5CB;  
  padding: calc(var(--base-unit) * 4) 0;
}

.milk-indicator {
  height: calc(var(--base-unit) * 40);
  width: calc(var(--base-unit) * 40);
  display: flex;
  align-items: center;
  justify-content: center;
}

.milk-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

</style>
