<script setup>
import { ref, onMounted } from 'vue'
import { useFarmPlotUnlockPopupStore } from '../../stores/farmPlotUnlockPopupStore'
import { useFarmStore } from '../../stores/farmPlotStore'
import { ColorButton } from '@/components/common'
import { useUserInfo } from '@/features/userInfo'
import { BoosterPopup } from '@/features/gameplay/booster'
import { useMainTaskStore } from '@/features/newTask'

import audioService from '@/lib/audioService'
import { unlockFarmPlot } from '@/features/gameplay/api'
import { useI18n } from 'vue-i18n'
import { useThemeAssets } from '@/themes'

const { t } = useI18n()

const farmPlotUnlockPopupStore = useFarmPlotUnlockPopupStore()
const farmPlotStore = useFarmStore();

const { userInfo, fetchUserInfo } = useUserInfo();
const { updateMainTask } = useMainTaskStore()
const theme = useThemeAssets()

const isLoading = ref(false)

const handleUnlock = async () => {
  if (isLoading.value) return
  audioService.play('button1'); // Play sound effect

  try {
    isLoading.value = true
    await unlockFarmPlot(farmPlotUnlockPopupStore.plot?.plotNumber)
    await Promise.all([
      fetchUserInfo(),
      farmPlotStore.setFarmPlotsFromApi(),
      updateMainTask()
    ])
    farmPlotUnlockPopupStore.closeFarmPlotUnlockPopup()
  } catch (error) {
    console.error('Failed to unlock farm plot:', error)
  } finally {
    isLoading.value = false
  }
}

onMounted(async () => {
  await fetchUserInfo();
})
</script>

<template>
  <BoosterPopup :isOpen="farmPlotUnlockPopupStore.isFarmPlotUnlockShow" @close="farmPlotUnlockPopupStore.closeFarmPlotUnlockPopup">
    <div class="image-container">
      <img :src="theme.farmUnlock" alt="Farm Plot Unlock" />
    </div>
    <div class="title">{{ $t('common.unlock') }}</div>
    <div class="description">{{ $t('common.unlockPlotDescription') }}</div>
    <div class="button-group-container">
        <div class="button-container">  
            <img class="button-icon" src="/public/assets/upgrade/upgrade.png" alt="upgrade-icon">
            <div class="button-text top">{{ $t('common.unlock') }}</div>
        </div>
        <ColorButton 
          id="farm-plot-unlock-button"
          :disabled="userInfo.gem < farmPlotUnlockPopupStore.plot?.unlockCost || isLoading" 
          :class="{ 'pulse-animation': !(userInfo.gem < farmPlotUnlockPopupStore.plot?.unlockCost) }"
          @click="handleUnlock"
        >
            <div class="button-container">
                <img class="button-icon" :src="theme.gem" alt="gem-icon">
                <div class="button-text">{{ isLoading ? $t('common.unlocking') : `${Number(farmPlotUnlockPopupStore.plot?.unlockCost) || 0} ${$t('common.gems')}` }}</div>
            </div>
        </ColorButton>
    </div>
  </BoosterPopup>
</template>

<style scoped>

.image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(var(--base-unit) * 120);
  width: calc(var(--base-unit) * 120);
  margin: 0 auto;
  margin-bottom: calc(var(--base-unit) * 24);
  margin-top: calc(var(--base-unit) * 8);
}

.image-container img {
  height: 100%;
  width: 100%;
  object-fit: contain;
}

.title {
  color: #FEEEC6;
  font-size: calc(var(--base-unit) * 24);
  margin-bottom: calc(var(--base-unit) * 12);
  margin-top: calc(var(--base-unit) * -8);
  text-align: center;
  -webkit-text-stroke: 0;
  text-shadow: none;
}

.description {
  color: #FEEEC6;
  font-size: calc(var(--base-unit) * 14);
  margin-bottom: calc(var(--base-unit) * 12);
  text-align: center;
}

.button-group-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  gap: calc(var(--base-unit) * 12);
  margin: 0 auto;

  background: #FDC844;
  border: calc(var(--base-unit) * 1) solid #F0FFFF;
  border-radius: calc(var(--base-unit) * 12);
  width: calc(var(--base-unit) * 157);
  padding: calc(var(--base-unit) * 8) calc(var(--base-unit) * 16);
}

.button-icon {
  width: calc(var(--base-unit) * 24);
  height: calc(var(--base-unit) * 24);
  object-fit: contain;
}

.button-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: calc(var(--base-unit) * 8);
}

.button-text {
  transform: translateY(calc(var(--base-unit) * 2));
}

.button-text.top {
  font-weight: 700;
  font-size: calc(var(--base-unit) * 16);
}

@keyframes pulse-scale {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.pulse-animation {
  animation: pulse-scale 2s ease-in-out infinite;
}
</style>

