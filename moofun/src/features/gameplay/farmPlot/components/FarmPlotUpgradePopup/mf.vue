<script setup>
import { ref, onMounted, computed } from 'vue'
import { useFarmPlotUpgradePopupStore } from '../../stores/farmPlotUpgradePopupStore'
import { useFarmStore } from '../../stores/farmPlotStore'
import { ColorButton } from '@/components/common'
import { useUserInfo } from '@/features/userInfo'
import { BoosterPopup, BoosterLabel } from '@/features/gameplay/booster'

import audioService from '@/lib/audioService'
import { upgradeFarmPlot } from '@/features/gameplay/api'
import { storeToRefs } from 'pinia'
import { useI18n } from 'vue-i18n'
import { useMainTaskStore } from '@/features/newTask'
import { useThemeAssets } from '@/themes'

const { t } = useI18n()
const { userInfo, fetchUserInfo } = useUserInfo();
const farmPlotUpgradePopupStore = useFarmPlotUpgradePopupStore();
const farmPlotStore = useFarmStore();
const { plots } = storeToRefs(farmPlotStore);
const { updateMainTask } = useMainTaskStore()
const theme = useThemeAssets()

const reactivePlot = computed(() =>
  plots.value.find(p => p.plotNumber === farmPlotUpgradePopupStore.plotNumber)
);


const isLoading = ref(false)

const handleUpgrade = async () => {
  if (isLoading.value) return
  audioService.play('button1'); // Play sound effect
  try {
    isLoading.value = true
    await upgradeFarmPlot(reactivePlot.value?.plotNumber)
    await Promise.all([
      fetchUserInfo(),
      farmPlotStore.setFarmPlotsFromApi(),
      updateMainTask()
    ])
  } catch (error) {
    console.error('Failed to upgrade farm plot:', error)
  } finally {
    isLoading.value = false
  }
}
onMounted(async () => {
  await fetchUserInfo();
})

const getProductionSpeed = () => {
  return reactivePlot.value?.speedPercentage || 0
}

const getNextProductionSpeedGrowth = () => {
  return reactivePlot.value?.nextUpgradeGrowth?.nextSpeedPercentage || 0
}

</script>

<template>
  <BoosterPopup class="upgrade-popup" :isOpen="farmPlotUpgradePopupStore.isFarmPlotUpgradeShow" @close="farmPlotUpgradePopupStore.closeFarmPlotUpgradePopup">
    <BoosterLabel class="title">{{ $t('farmPlot.farmPlot') }} #{{ reactivePlot?.plotNumber }}</BoosterLabel>
    <div class="stage-title">{{ $t('farmPlot.plot') }} {{ reactivePlot?.plotNumber }} - {{ $t('common.level') }} {{ reactivePlot?.level }}</div>
    <div class="upgrade">
        <div class="upgrade-icon">
            <img class="icon-image" :src="theme.productionUpgrade" alt="production-icon">
        </div>
        <div class="upgrade-info">
            <div class="upgrade-title">{{ $t('common.milkProduction') }}</div>
            <div class="upgrade-stats">
              <div class="before">{{ Number(reactivePlot?.milkProduction || 0).toFixed(2) }}</div>
              <div class="after" v-if="reactivePlot?.nextUpgradeGrowth">
                +{{ (Number(reactivePlot?.nextUpgradeGrowth?.nextMilkProduction) - Number(reactivePlot?.milkProduction) || 0).toFixed(2) }}
              </div>
            </div>
        </div>
    </div>
    <div class="upgrade">
        <div class="upgrade-icon">
            <img class="icon-image" :src="theme.productionSpeedUpgrade" alt="farm-plot-icon">
        </div>
        <div class="upgrade-info">
            <div class="upgrade-title">{{ $t('common.productionSpeed') }}</div>
            <div class="upgrade-stats">
              <div class="before">{{getProductionSpeed()}}%</div>
              <div class="after" v-if="reactivePlot?.nextUpgradeGrowth">+{{getNextProductionSpeedGrowth()}}%</div>
            </div>
        </div>
    </div>
    <div class="upgrade">
        <div class="upgrade-icon">
            <img class="icon-image" :src="theme.cowCountUpgrade" alt="farm-plot-icon">
        </div>
        <div class="upgrade-info">
            <div class="upgrade-title">{{ $t('common.cowCount') }}</div>
            <div class="upgrade-stats">
              <div class="before">{{reactivePlot?.barnCount}}</div>
              <div class="after" v-if="reactivePlot?.nextUpgradeGrowth">+{{reactivePlot?.nextUpgradeGrowth?.nextBarnCount - reactivePlot?.barnCount}}</div>
            </div>
        </div>
    </div>
    <div class="button-group-container" v-if="reactivePlot?.level < 20">
        <div class="button-container">  
            <img class="button-icon" src="/public/assets/upgrade/upgrade.png" alt="upgrade-icon">
            <div class="button-text top">{{ $t('common.upgrade') }}</div>
        </div>
        <ColorButton 
          id="farm-plot-upgrade-button"
          :disabled="Number(userInfo?.gem || 0) < Number(reactivePlot?.upgradeCost || 0) || isLoading" 
          :class="{ 'pulse-animation': !(Number(userInfo?.gem || 0) < Number(reactivePlot?.upgradeCost || 0)) }"
          @click="handleUpgrade"
        >
            <div class="button-container">
                <img class="button-icon" :src="theme.gem" alt="gem-icon">
                <div class="button-text">{{ isLoading ? $t('common.upgrading') : `${Number(reactivePlot?.upgradeCost) || 0} ${$t('common.gems')}` }}</div>
            </div>
        </ColorButton>
    </div>
  </BoosterPopup>
</template>


<style scoped>
.upgrade-popup {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.title {
    padding: calc(var(--base-unit) * 12);
    width: 100%;
    box-sizing: border-box;
    margin-bottom: calc(var(--base-unit) * 12);
    margin-top: calc(var(--base-unit) * -8);
    border-bottom: calc(var(--base-unit) * 2) solid #FCF1CB;
}

.stage-title {
  font-size: calc(var(--base-unit) * 16);
  font-weight: 600;
  text-align: center;
  margin-bottom: calc(var(--base-unit) * 12);
  color: #FCF1CB;
}

.upgrade {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: calc(var(--base-unit) * 12);
  margin-bottom: calc(var(--base-unit) * 12);
}

.upgrade-icon {
  background: radial-gradient(50% 50% at 50% 50%, #374DD7 18.27%, #A885AD 96.63%);
  border: calc(var(--base-unit) * 2) solid #0D2259;
  width: calc(var(--base-unit) * 56);
  height: calc(var(--base-unit) * 56);
  border-radius: calc(var(--base-unit) * 12);
  background-color: #FCF1CB;
  box-sizing: border-box;

  display: flex;
  align-items: center;
  justify-content: center;
  padding: calc(var(--base-unit) * 12);
}

.icon-image {
  width: calc(var(--base-unit) * 56);
  height: calc(var(--base-unit) * 56);
  object-fit: contain;
}

.upgrade-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #CF8E61;
  border: calc(var(--base-unit) * 3) solid #FDD99B;
  border-radius: calc(var(--base-unit) * 12);
  padding: calc(var(--base-unit) * 12) calc(var(--base-unit) * 12);
  width: 100%;
  height: calc(var(--base-unit) * 56);

  box-sizing: border-box;
  
  color:#FEEEC6;
  font-size: calc(var(--base-unit) * 14);
}

.upgrade-title {
  font-size: calc(var(--base-unit) * 16);
  font-weight: 600;
}

.upgrade-stats {
  display: flex;
  align-items: end;
  justify-content: space-between;
  flex-direction: column;
}

.before {
  color: #FEEEC6;
}

.after {
  color: #FDC844;
}


.button-group-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  gap: calc(var(--base-unit) * 12);
  margin: 0 auto;

  background: #FDC844;
  border: calc(var(--base-unit) * 1) solid #F0FFFF;
  border-radius: calc(var(--base-unit) * 12);
  width: calc(var(--base-unit) * 157);
  padding: calc(var(--base-unit) * 8) calc(var(--base-unit) * 16);
}

.button-icon {
  width: calc(var(--base-unit) * 24);
  height: calc(var(--base-unit) * 24);
  object-fit: contain;
}

.button-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: calc(var(--base-unit) * 8);
}

.button-text {
  transform: translateY(calc(var(--base-unit) * 2));
}

.button-text.top {
  font-weight: 700;
  font-size: calc(var(--base-unit) * 16);
}

@keyframes pulse-scale {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.pulse-animation {
  animation: pulse-scale 2s ease-in-out infinite;
}

</style>

