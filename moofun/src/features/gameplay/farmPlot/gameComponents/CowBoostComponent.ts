import { GameComponent } from '@/features/gameplay/shared';

export class CowBoostComponent extends GameComponent {
  private isBoosted = false;
  private boostTimer = 0;
  private boostDuration = 0;


  public activateBoost(durationSeconds: number): void {
    this.isBoosted = true;
    this.boostDuration = durationSeconds;
    this.boostTimer = 0;
  }


  public getBoostState(): boolean {
    return this.isBoosted;
  }

  public getRemainingBoostTime(): number {
    return this.isBoosted ? this.boostDuration - this.boostTimer : 0;
  }

  override update(deltaSeconds: number): void {
    if (this.isBoosted) {
      this.boostTimer += deltaSeconds;
      
      // Disable boost when timer expires
      if (this.boostTimer >= this.boostDuration) {
        this.isBoosted = false;
        this.boostTimer = 0;
        this.boostDuration = 0;
      }
    }
  }
}
