import { GameComponent, SpriteRendererComponent } from '@/features/gameplay/shared';
import { SpineRendererComponent } from '@/features/gameplay/shared/common/SpineRendererComponent';

export class IndexZSetterComponent extends GameComponent {
  zIndex = 1;
  private spriteRenderer?: SpriteRendererComponent;
  private spineRenderer?: SpineRendererComponent;

  override attach(gameObject: import('@/features/gameplay/shared').GameObject): void {
    super.attach(gameObject);
    this.spriteRenderer = this.gameObject.getComponent(SpriteRendererComponent);
    this.spineRenderer = this.gameObject.getComponent(SpineRendererComponent);
  }

  override update(): void {
    const yNormalized = this.gameObject.transform.position.y;
    // Convert normalized y into a zIndex with offset to ensure positive values
    // Add 100 as base offset to handle negative y coordinates
    // Higher y -> higher zIndex for front-to-back perspective
    this.zIndex = Math.floor((yNormalized + 1) * 100);

    if (this.spriteRenderer) {
      this.spriteRenderer.sprite.zIndex = this.zIndex;
    }
    if (this.spineRenderer) {
      this.spineRenderer.spine.zIndex = this.zIndex;
    }
  }
}
