import { Assets } from 'pixi.js';
import { Spine } from '@esotericsoftware/spine-pixi-v8';
import { GameObject, Mathf, Vector2, Scene } from '@/features/gameplay/shared';
import { AssetService } from '../../services/AssetService';
import { CowRunnerComponent } from '../../gameComponents/CowRunnerComponent';
import { IndexZSetterComponent } from '../../gameComponents/IndexZSetterComponent';
import { CowProduceComponent } from '../../gameComponents/CowProduceComponent';
import { CowBoostComponent } from '../../gameComponents/CowBoostComponent';
import { Ref } from 'vue';
import type { FarmPlotStats } from '../../stores/farmPlotStore';
import { useThemeAssets } from '@/themes';
import { SpineRendererComponent } from '@/features/gameplay/shared/common/SpineRendererComponent';

export class GooseGameObject extends GameObject {
  constructor(scene: Scene, private plot: Ref<FarmPlotStats>) {
    super(scene);
  }

  async start(): Promise<void> {
    this.transform.scale = new Vector2(0.15, 0.15);

    // Register the skeleton and atlas with Pixi's Assets system (if not already registered)
    const skeletonKey = 'goose-skeleton';
    const atlasKey = 'goose-atlas';
    const skeletonPath = '/assets/entity/goose/Duck.json';
    const atlasPath = '/assets/entity/goose/Duck.atlas';
    Assets.add({ alias: skeletonKey, src: skeletonPath });
    Assets.add({ alias: atlasKey, src: atlasPath });
    // Preload both assets
    await Assets.load([skeletonKey, atlasKey]);

    // Create the SpineRendererComponent using the Spine.from() factory
    const spineRenderer = new SpineRendererComponent({ skeletonKey, atlasKey });

    // Set a default animation (replace 'idle' with your actual animation name if needed)
    const anims = spineRenderer.spine.state.data.skeletonData.animations;
    const walkAnim = anims.find(a => a.name === 'walk');
    if (walkAnim) {
      spineRenderer.spine.state.setAnimation(0, 'walk', true);
    } else if (anims.length > 0) {
      const firstAnim = anims[0].name;
      spineRenderer.spine.state.setAnimation(0, firstAnim, true);
    }

    // Add the Spine renderer component
    this.addComponent(spineRenderer);

    // Add other components as before
    let cowRunner = new CowRunnerComponent();
    this.addComponent(cowRunner);
    cowRunner.randomizePosition();

    let indexZBasedOnY = new IndexZSetterComponent();
    this.addComponent(indexZBasedOnY);

    let cowProduce = new CowProduceComponent(this.plot);
    this.addComponent(cowProduce);

    // Add boost component
    let cowBoost = new CowBoostComponent();
    this.addComponent(cowBoost);
  }

  override onUpdate(): void {
    // Optionally update logic
  }
}
