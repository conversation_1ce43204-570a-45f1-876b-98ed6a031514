import { Sprite, Assets } from 'pixi.js'; 
import { GameObject, Mathf, Vector2, Scene, SpriteRendererComponent } from '@/features/gameplay/shared';
import { AssetService } from '../../services/AssetService';
import { CowRunnerComponent } from '../../gameComponents/CowRunnerComponent';
import { IndexZSetterComponent } from '../../gameComponents/IndexZSetterComponent';
import { CowProduceComponent } from '../../gameComponents/CowProduceComponent';
import { CowBoostComponent } from '../../gameComponents/CowBoostComponent';
import { Ref } from 'vue';
import type { FarmPlotStats } from '../../stores/farmPlotStore';
import { useThemeAssets } from '@/themes';

export class CowGameObject extends GameObject {
  constructor(scene: Scene, private plot: Ref<FarmPlotStats>) {
    super(scene);
  }

  async start(): Promise<void> {
    this.transform.scale = new Vector2(0.15, 0.15);

    const assets = await AssetService.loadPlotAssets(this.plot.value.plotNumber);
    const texture = await Assets.load(assets.cow);

    let spriteRenderer = new SpriteRendererComponent({
      texture,
      anchor: { x: 0.5, y: 0.5 },
    });

    this.addComponent(spriteRenderer);
    spriteRenderer.render(); // initial sync

    let cowRunner = new CowRunnerComponent();
    this.addComponent(cowRunner);
    cowRunner.randomizePosition();

    let indexZBasedOnY = new IndexZSetterComponent();
    this.addComponent(indexZBasedOnY);

    let cowProduce = new CowProduceComponent(this.plot);
    this.addComponent(cowProduce);

    // Add boost component
    let cowBoost = new CowBoostComponent();
    this.addComponent(cowBoost);
  }

  override onUpdate(): void {

  }
}
