import { computed, ref, Ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useFarmStore } from '../stores/farmPlotStore';
import { EmptyGameObject, GameObject, SpriteRendererComponent, Vector2, Scene } from '@/features/gameplay/shared';
import { FarmPlotGameObject } from '../gameObjects/FarmPlotGameObject';
import { Assets, ColorMatrixFilter } from 'pixi.js';
import { FarmPlotRunnerComponent } from '../gameComponents/FarmPlotRunnerComponent';
import type { FarmPlotStats } from '../stores/farmPlotStore';
import { useThemeAssets } from '@/themes';

const PIXELS_PER_PLOT = 211;
let scene: Scene | null = null;
const frameRenderers = new Map<number, SpriteRendererComponent>();
let hasWatcher = false;

export const useFarmGroupScene = async (container: HTMLElement | null) => {
  const theme = useThemeAssets()
  const farmStore = useFarmStore();
  const { plots } = storeToRefs(farmStore);
  let sceneReady = false;

  // Clean up old scene
  if (scene) {
    scene.destroy();
    scene = null;
  }
  frameRenderers.clear();

  await farmStore.setFarmPlotsFromApi();

  const sceneHeight = PIXELS_PER_PLOT * plots.value.length;
  
  container.style.height = `calc(var(--base-unit) * ${sceneHeight})`;
  await new Promise((resolve) => requestAnimationFrame(resolve));
  
  scene = new Scene(container, {
    width: container.offsetWidth,
    height: container.offsetHeight,
    background: '#228b22',
    antialias: false,
    resolution: 1,
    preference: 'webgpu',
    powerPreference: 'high-performance'
  });
  await scene.init();

  const plotHeight = 1 / plots.value.length;

  await Promise.all(
    plots.value.map(async (plot, index) => {
    // Frame
    const gameObject = new EmptyGameObject(scene!);
    await GameObject.instantiate(gameObject);
    const texture = await Assets.load(theme.value.frameEmpty);
    const spriteRenderer = new SpriteRendererComponent({
      texture,
      anchor: { x: 0.5, y: 0.5 },
      zIndex: 1000,
    });
    frameRenderers.set(plot.plotNumber, spriteRenderer);

    spriteRenderer.scaleRelativeToWidth = true;
    gameObject.addComponent(spriteRenderer);
    gameObject.transform.position = new Vector2(0.5, index * plotHeight + plotHeight * 0.5);
    gameObject.transform.scale = new Vector2(1, 1);
    
    // Plot
    const reactivePlot = computed(() => plots.value.find(p => p.plotNumber === plot.plotNumber));
    const farmPlot = await GameObject.instantiate(new FarmPlotGameObject(scene!, reactivePlot));
    farmPlot.transform.position = new Vector2(0.505, index * plotHeight + plotHeight * 0.6);
    farmPlot.transform.scale = new Vector2(plotHeight * 0.67, plotHeight * 0.67);
    farmPlot.addComponent(new FarmPlotRunnerComponent(reactivePlot));
    
  }));
  sceneReady = true;

  const updateFrameFilters = (plots: FarmPlotStats[]) => {
    if (!sceneReady) {
      console.warn('[FarmGroupScene] Scene not ready, skipping filter update.');
      return;
    }
  
    plots.forEach((plot) => {
      const spriteRenderer = frameRenderers.get(plot.plotNumber);
  
      if (!spriteRenderer) {
        console.warn(`[FarmGroupScene] No spriteRenderer found for plot ${plot.plotNumber}`);
        return;
      }
  
      if (!spriteRenderer.sprite) {
        console.warn(`[FarmGroupScene] spriteRenderer.sprite is missing for plot ${plot.plotNumber}`);
        return;
      }
  
      try {
        if (!plot.isUnlocked) {
          const grayscaleFilter = new ColorMatrixFilter();
          grayscaleFilter.desaturate();
          grayscaleFilter.brightness(0.4, true);
          spriteRenderer.sprite.filters = [grayscaleFilter];
        } else {
          spriteRenderer.sprite.filters = [];
        }
      } catch (err) {
        console.error(`[FarmGroupScene] Failed to apply filter for plot ${plot.plotNumber}:`, err);
      }
    });
  };
  

  // One-time watcher
  if (!hasWatcher) {
    watch(
      plots,
      (newPlots) => {
        updateFrameFilters(plots.value);
      },
      { deep: true }
    );
    hasWatcher = true;
  }
  updateFrameFilters(plots.value);


  return scene;
};
