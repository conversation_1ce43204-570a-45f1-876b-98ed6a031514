import { Assets } from 'pixi.js';
import { useThemeAssets } from '@/themes';

export interface FarmPlotAssets {
  background: string;
  cow: string;
  colorTint: number;
}

export class AssetService {
  // Only static for non-theme assets
  private static readonly COW_PATHS = {
    level0: '/assets/entity/cow/cow-1.png',
    level1: '/assets/entity/cow/cow-2.png',
    level2: '/assets/entity/cow/cow-3.png',
    level3: '/assets/entity/cow/cow-4.png'
  };

  private static readonly COLOR_TINTS = {
    level0: 0xFFFFFF, // White
    level1: 0xADD8E6, // Light blue
    level2: 0x98FB98, // Light green
    level3: 0xFFFFB3, // Light yellow
    level4: 0xFFDAAB, // Light orange
    level5: 0xFFB6B6, // Light red
    level6: 0xE6B3FF, // Light purple
    level7: 0xD3D3D3  // Light gray (instead of black)
  };

  /**
   * Returns a function that provides the current themed farm plot backgrounds.
   * Can be used for advanced scenarios, but not required for loadPlotAssets.
   */
  static useBackgroundAssets() {
    return {
      level0: useThemeAssets().value.farmPlot1,
      level1: useThemeAssets().value.farmPlot2,
      level2: useThemeAssets().value.farmPlot3,
      level3: useThemeAssets().value.farmPlot4,
    };
  }

  /**
   * Loads plot assets for the given plot number, using the current theme.
   * Must be called in a Vue setup() or runtime context.
   */
  static async loadPlotAssets(plotNumber: number): Promise<FarmPlotAssets> {
    const cowLevel = `level${(plotNumber - 1) % 4}`;
    const bgLevel = `level${Math.floor((plotNumber - 1) / 4) % 4}`;
    const colorLevel = `level${Math.floor((plotNumber - 1) / 16) % 8}`;

    // Get themed backgrounds internally
    const backgrounds = this.useBackgroundAssets();
    const background = backgrounds[bgLevel];
    const cow = this.COW_PATHS[cowLevel];

    // Load the assets
    await Assets.load([
      background,
      cow
    ]);

    return {
      background,
      cow,
      colorTint: this.COLOR_TINTS[colorLevel]
    };
  }
} 