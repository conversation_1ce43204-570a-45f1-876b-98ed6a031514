<script setup>
import { ref, onMounted } from 'vue'
import { useUserInfo } from '@/features/userInfo'
import { BoosterPopup2 } from '@/features/gameplay/booster'
import { ColorButton } from '@/components/common'

import { fetchOfflineReward, claimOfflineReward } from '../api'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const { userInfo, fetchUserInfo } = useUserInfo();

// Offline reward state
const offlineRewardData = ref(null)
const hasOfflineReward = ref(false)
const isLoading = ref(false)

const fetchOfflineRewardData = async () => {
  try {
    isLoading.value = true
    const response = await fetchOfflineReward()
    offlineRewardData.value = response.data
    hasOfflineReward.value = response.data?.offlineReward?.gem > 0
  } catch (error) {
    console.error('Failed to fetch offline reward:', error)
    hasOfflineReward.value = false
  } finally {
    isLoading.value = false
  }
}

const handleClaimOfflineReward = async () => {
  try {
    isLoading.value = true
    const response = await claimOfflineReward()
    // Refresh user info after claiming
    await fetchUserInfo()
    // Refresh offline reward data
    await fetchOfflineRewardData()
  } catch (error) {
    console.error('Failed to claim offline reward:', error)
  } finally {
    isLoading.value = false
  }
}

onMounted(async () => {
  await fetchUserInfo();
  await fetchOfflineRewardData()
})

</script>

<template>
  <BoosterPopup2 class="farm-plot-upgrade-popup" :isOpen="hasOfflineReward" @close="hasOfflineReward = false">
    <div class="image-container">
      <img src="/public/assets/upgrade/bonus.png" alt="Farm Plot Unlock" />
    </div>
    <div class="title">{{ $t('common.offlineReward') }}</div>
    <div class="description">{{ $t('common.offlineRewardDescription', { gems: offlineRewardData?.offlineReward?.gem }) }}</div>
    <div class="button-container">
      <ColorButton :disabled="isLoading" :isLoading="isLoading" @click="handleClaimOfflineReward">
        {{ isLoading ? $t('common.claiming') : $t('common.claim') }}
      </ColorButton>
    </div>
  </BoosterPopup2>
</template>

<style scoped>
.image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(var(--base-unit) * 120);
  width: calc(var(--base-unit) * 120);
  margin: 0 auto;
  margin-bottom: calc(var(--base-unit) * 24);
  margin-top: calc(var(--base-unit) * 8);
}

.image-container img {
  height: 100%;
  width: 100%;
  object-fit: contain;
}

.title {
  color: #68514B;
  font-size: calc(var(--base-unit) * 24);
  margin-bottom: calc(var(--base-unit) * 12);
  margin-top: calc(var(--base-unit) * -8);
  text-align: center;
  -webkit-text-stroke: 0;
  text-shadow: none;
}

.description {
  color: #68514B;
  font-size: calc(var(--base-unit) * 14);
  margin-bottom: calc(var(--base-unit) * 12);
  text-align: center;
}

.button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: calc(var(--base-unit) * 12);
}

.button-container button {
  width: 100%;

}
</style>

