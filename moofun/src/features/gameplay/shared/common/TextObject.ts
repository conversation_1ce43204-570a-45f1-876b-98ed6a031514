import { Text, TextStyle } from 'pixi.js';
import { GameObject, Vector2, RendererComponent } from '@/features/gameplay/shared';
import { Scene } from '@/features/gameplay/shared';

export interface TextObjectConfig {
  text: string;
  scale?: Vector2;
  position: Vector2; // relative (0–1)
  style?: Partial<TextStyle>;
}

export class TextObject extends GameObject {
  public text!: Text;
  public config: Required<TextObjectConfig>;

  constructor(scene: Scene, config: TextObjectConfig) {
    super(scene);

    this.config = {
      text: config.text,
      scale: config.scale ?? new Vector2(1, 1),
      position: config.position.clone(),
      style: {
        fontFamily: 'Goldman Sans',
        fontSize: 24,
        fill: '#ffffff',
        align: 'center',
        stroke: {
          color: '#000000',
          width: 2,
          alignment: 0
        },
        ...config.style
      }
    };


  }

  override async start(): Promise<void> {
    this.text = new Text({
      text: this.config.text,
      style: this.config.style
    });

    this.text.anchor.set(0.5);
    this.scene.container.addChild(this.text);

    this.transform.scale = this.config.scale;
    this.transform.position = this.config.position;

    this.updateLayout();
  }

  protected onUpdate(): void {
    this.updateLayout();
  }

  updateLayout(): void {
    if (!this.text) return;

    this.setSpriteScale(this.transform.scale);
    this.setSpritePosition(this.transform.position);

  }

  private setSpriteScale(scale: Vector2): void {
    this.text.scale.set(scale.x * this.getParentSize().x, scale.y * this.getParentSize().x);
  }
  
  private setSpritePosition(pos: Vector2): void {
    if(this.transform.parent) {
      const position = this.transform.position;

      const localX = position.x * this.getParentSize().x;
      const localY = position.y * this.getParentSize().y;
      const x = this.getParentPosition().x + localX;
      const y = this.getParentPosition().y + localY;

      this.text.position.set(x, y);

      return;
    }

    this.text.position.set(pos.x * this.getSceneSize().x, pos.y * this.getSceneSize().y);
  }

  getParentSize(): Vector2 {
    if(!this.transform.parent) return this.getSceneSize();
    if(!this.transform.parent.gameObject.getComponent(RendererComponent)) return this.getSceneSize();
    return this.transform.parent.gameObject.getComponent(RendererComponent).getRawSize();
  }

  getParentPosition(): Vector2 {
    if(!this.transform.parent) return new Vector2(1, 1);
    if(!this.transform.parent.gameObject.getComponent(RendererComponent)) return new Vector2(1, 1);
    return this.transform.parent.gameObject.getComponent(RendererComponent).getRawPosition();
  }

  // size of the scene container
  getSceneSize(): Vector2 {
    if(!this.transform.gameObject) return new Vector2(0, 0);
    return this.transform.gameObject.scene.getSize();
  }

  override destroy(): void {
    if (this.text) {
      this.scene.container.removeChild(this.text);
      this.text.destroy();
    }
    super.destroy();
  }
}
