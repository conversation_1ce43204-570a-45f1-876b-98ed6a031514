import { GameComponent } from './GameComponent';
import { Transform } from './Transform';
import { Scene } from '../scene/Scene';

export abstract class GameObject {
  public readonly id: string;
  public readonly tag?: string;
  public readonly transform: Transform;
  public readonly type: string;
  public readonly scene: Scene;

  private readonly components: GameComponent[] = [];
  private destroyed = false;

  constructor(scene: Scene, id?: string, tag?: string) {
    this.scene = scene;
    this.transform = new Transform(this);
    this.id = id || `GameObject_${Math.random().toString(36).substring(2, 11)}`;
    this.tag = tag;
    this.type = this.constructor.name;


  }

  update(deltaSeconds: number): void {
    this.updateComponents(deltaSeconds);
    this.onUpdate(deltaSeconds);
  }

  protected abstract onUpdate(deltaSeconds: number): void;

  abstract start(): Promise<void>;

  addComponent(component: GameComponent): void {
    component.attach(this);
    this.components.push(component);
  }

  getComponent<T extends GameComponent>(type: new (...args: any[]) => T): T | undefined {
    return this.components.find((c): c is T => c instanceof type);
  }

  removeComponent(component: GameComponent): void {
    const index = this.components.indexOf(component);
    if (index !== -1) {
      component.destroy?.();
      this.components.splice(index, 1);
    }
  }

  private updateComponents(deltaSeconds: number): void {
    for (const component of this.components) {
      component.update?.(deltaSeconds);
    }
  }

  destroy(): void {
    if (this.destroyed) return;
    this.destroyed = true;

    for (const component of this.components) {
      component.destroy?.();
    }

    this.components.length = 0;

    this.scene.remove(this);
  }

  static destroy(target: GameObject, delaySeconds: number = 0): void {
    if (delaySeconds <= 0) {
      target.destroy();
    } else {
      setTimeout(() => {
        if (!target.isDestroyed) {
          target.destroy();
        }
      }, delaySeconds * 1000);
    }
  }

  get isDestroyed(): boolean {
    return this.destroyed;
  }

  get screenWidth(): number {
    return this.scene.app.screen.width;
  }

  get screenHeight(): number {
    return this.scene.app.screen.height;
  }

  static async instantiate<T extends GameObject>(gameObject: T): Promise<T> {
    gameObject.scene.add(gameObject);
    await gameObject.start();
    return gameObject;
  }
}
