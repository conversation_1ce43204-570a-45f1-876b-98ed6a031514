

export class Mathf {

    static Lerp(a: number, b: number, t: number): number {
      t = Mathf.Clamp01(t);
      return a + (b - a) * t;
    }
  
    static Clamp(value: number, min: number, max: number): number {
      return Math.max(min, Math.min(max, value));
    }
  
    static Clamp01(value: number): number {
      return Mathf.Clamp(value, 0, 1);
    }
  
    static MoveTowards(current: number, target: number, maxDelta: number): number {
      if (Math.abs(target - current) <= maxDelta) return target;
      return current + Math.sign(target - current) * maxDelta;
    }
  
    static Abs(value: number): number {
      return Math.abs(value);
    }
  
  }
  