// SceneManager.ts
import { Application, Container } from 'pixi.js';
import { Scene } from './Scene';

interface SceneContext {
  app: Application;
  scene: Scene;
  container: Container;
  containerRef: HTMLElement;
}

export class SceneManager {
  private static scenes: Map<string, SceneContext> = new Map();

  static async initScene(
    key: string,
    containerRef: HTMLElement,
    appOptions: ConstructorParameters<typeof Application>[0]
  ): Promise<void> {
    if (SceneManager.scenes.has(key)) return;

    const app = new Application();
    await app.init(appOptions);

    const container = new Container();
    const scene = new Scene(containerRef, appOptions);
    scene.init();
    app.stage.addChild(container);

    containerRef.appendChild(app.canvas);

    SceneManager.scenes.set(key, {
      app,
      scene,
      container,
      containerRef,
    });

    // Start update loop
    app.ticker.add((ticker) => {
      scene.update(ticker.deltaMS / 1000);
    });

    // Add resize handling for this canvas
    window.addEventListener('resize', () => {
      const width = containerRef.offsetWidth;
      const height = containerRef.offsetHeight;
      app.renderer.resize(width, height);
      scene.resize();
    });
  }

  static getScene(key: string): Scene | undefined {
    return SceneManager.scenes.get(key)?.scene;
  }

  static getApp(key: string): Application | undefined {
    return SceneManager.scenes.get(key)?.app;
  }

  static destroyScene(key: string): void {
    const context = SceneManager.scenes.get(key);
    if (!context) return;

    context.scene.destroy();
    context.app.destroy(true);
    SceneManager.scenes.delete(key);
  }

  static isInitialized(key: string): boolean {
    return SceneManager.scenes.has(key);
  }
}
