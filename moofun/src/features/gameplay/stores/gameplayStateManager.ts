import { defineStore } from 'pinia';
import { devLog } from '@/utils/config'
import { useDeliveryLineStore } from '@/features/gameplay/deliveryLine';
import { useFarmStore } from '@/features/gameplay/farmPlot';
import { useUserInfo } from '@/features/userInfo'
import { batchUpdateResources } from '@/features/gameplay/api'
import { useMainTaskStore } from '@/features/newTask'

export interface GameplayState {
  isActive: boolean;
  lastSyncTime: number;
  pendingChanges: boolean;
  syncInterval?: number | null;
  milkProductionInterval?: number | null;
}

export const useGameplayStateManager = defineStore('gameplayState', {
  state: (): GameplayState => ({
    isActive: false,
    lastSyncTime: 0,
    pendingChanges: false,
    syncInterval: null,
    milkProductionInterval: null,
  }),

  actions: {
    // Called when entering gameplay
    async enterGameplay() {
      this.isActive = true;
      this.lastSyncTime = Date.now();

      // Fetch initial state from API
      await this.syncFromServer();

      // Start periodic syncing
      this.startPeriodicSync();

      // Start milk production updates
      this.startMilkProduction();
    },

    // Called when exiting gameplay
    async exitGameplay() {
      this.isActive = false;

      // Stop periodic syncing
      this.stopPeriodicSync();

      // Stop milk production updates
      this.stopMilkProduction();

      // Final sync to server
      await this.syncToServer();
    },

    // Sync data from server
    async syncFromServer() {
      try {
        const deliveryLineStore = useDeliveryLineStore();
        const farmStore = useFarmStore();
        const { fetchUserInfo } = useUserInfo();
        const { updateMainTask } = useMainTaskStore()

        await Promise.all([
          deliveryLineStore.fetchDeliveryLineFromApi(),
          farmStore.setFarmPlotsFromApi(),
          fetchUserInfo(),
          updateMainTask()
        ]);

        this.lastSyncTime = Date.now();
        this.pendingChanges = false;
      } catch (error) {
        console.error('Failed to sync from server:', error);
      }
    },

    // Sync data to server with batch update
    async syncToServer() {
      try {
        const deliveryLineStore = useDeliveryLineStore();
        const farmStore = useFarmStore();

        // Prepare temporal stats for batch update
        const temporalStats = deliveryLineStore.temporalStats;

        // Prepare gem request (convert milk to gems if needed)
        const gemRequest = temporalStats.temporallyGem;

        // Prepare milk operations (add accumulated milk)
        const milkOperations = temporalStats.milkOperations;

        // Call batch update API
        const res = await batchUpdateResources(gemRequest, milkOperations);
        devLog("batchUpdateResources:", res);

        // Refresh UI data after batch update
        await this.refreshUIData();

        // Reset temporal stats after successful sync
        deliveryLineStore.temporalStats.milkOperations.produce = 0;
        deliveryLineStore.temporalStats.milkOperations.consume = 0;
        deliveryLineStore.temporalStats.temporallyGem = 0;

        this.pendingChanges = false;
        this.lastSyncTime = Date.now();
      } catch (error) {
        console.error('Failed to sync to server:', error);
      }
    },

    // Refresh UI data after batch update
    async refreshUIData() {
      try {
        const deliveryLineStore = useDeliveryLineStore();
        const farmStore = useFarmStore();
        const { fetchUserInfo } = useUserInfo();
        const { updateMainTask } = useMainTaskStore()

        // Fetch updated data to refresh UI
        await Promise.all([
          deliveryLineStore.fetchDeliveryLineFromApi(),
          farmStore.setFarmPlotsFromApi(),
          fetchUserInfo(),
          updateMainTask()
        ]);

        devLog('UI data refreshed successfully');
      } catch (error) {
        console.error('Failed to refresh UI data:', error);
      }
    },

    // Mark that changes need to be synced
    markPendingChanges() {
      this.pendingChanges = true;
    },

    // Manual sync for testing/debugging
    async manualSync() {
      devLog('Manual sync triggered');
      await this.syncToServer();
    },

    startPeriodicSync() {
      // Sync every 30 seconds while gameplay is active
      this.syncInterval = setInterval(async () => {
        if (this.isActive) {
          devLog('Performing periodic sync...');
          await this.syncToServer();
        }
      }, 15000);
    },

    stopPeriodicSync() {
      if (this.syncInterval) {
        clearInterval(this.syncInterval);
        this.syncInterval = null;
      }
    },

    // Calculate total milk production per second from all unlocked farm plots
    calculateTotalMilkProduction(): number {
      const farmStore = useFarmStore();
      let totalProduction = 0;

      for (const plot of farmStore.plots) {
        if (plot.isUnlocked && plot.milkProduction > 0) {
          totalProduction += Number(plot.milkProduction);
        }
      }

      return totalProduction;
    },

    // Start milk production updates every second
    startMilkProduction() {
      this.milkProductionInterval = setInterval(() => {
        if (this.isActive) {
          const deliveryLineStore = useDeliveryLineStore();
          const milkProduced = this.calculateTotalMilkProduction();
          
          if (milkProduced > 0) {
            deliveryLineStore.temporalStats.milkOperations.produce += milkProduced;
            //devLog(`Milk produced: +${milkProduced.toFixed(2)}, Total pending: ${deliveryLineStore.temporalStats.milkOperations.produce.toFixed(2)}`);
          }
        }
      }, 1000); // Update every second
    },

    // Stop milk production updates
    stopMilkProduction() {
      if (this.milkProductionInterval) {
        clearInterval(this.milkProductionInterval);
        this.milkProductionInterval = null;
      }
    }
  }
});