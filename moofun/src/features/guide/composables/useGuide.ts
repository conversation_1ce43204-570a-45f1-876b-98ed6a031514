import { ref } from 'vue';

export const useGuide = () => {
  const currentStep = ref(0);
  const steps = ref<GuideStep[]>([]); // Loaded from a config
  const isActive = ref(false);

  const startGuide = () => {

    isActive.value = true;
    console.log('loadCheckpoint()', loadCheckpoint());
    const firstStep = loadCheckpoint() ?? 0;
    if (steps.value[firstStep]?.skipOnStart) {
      currentStep.value = firstStep + 1;
    } else {
      currentStep.value = firstStep;
    }
  };

  const nextStep = () => {
    currentStep.value++;
    if (steps.value[currentStep.value]?.checkpoint) {
      saveCheckpoint(currentStep.value);
    }
    if (currentStep.value >= steps.value.length) endGuide();
  };

  const endGuide = () => {
    isActive.value = false;
  };

  const saveCheckpoint = (step: number) => {
    localStorage.setItem('guide_checkpoint', step.toString());
    console.log('saveCheckpoint()', step);
  }

  const loadCheckpoint = (): number | null => {
    const step = localStorage.getItem('guide_checkpoint');
    return step ? parseInt(step) : null;
  };

  const clearCheckpoint = () => localStorage.removeItem('guide_checkpoint');

  return {
    steps,
    currentStep,
    isActive,
    saveCheckpoint,
    startGuide,
    nextStep,
    endGuide,
    clearCheckpoint
  };
};

export interface GuideStep {
  id: number;
  selector?: string; // Optional element to highlight
  type: 'restrict' | 'anywhere';
  text: string;
  image?: string;
  checkpoint?: boolean;
  showBubble?: boolean;
  bubblePosition?: 'center' | 'top' | 'bottom';
  skipOnStart?: boolean;
  // Optional manual highlight box (overrides selector)
  highlightArea?: {
    top: number;
    left: number;
    width: number;
    height: number;
    radius?: number;
  };

  // Optional pointer image placement (close-button icon)
  pointer?: {
    offsetX?: number;
    offsetY?: number;
  };
}
