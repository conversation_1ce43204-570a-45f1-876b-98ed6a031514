<script setup>
import { ref } from 'vue'
import { useThemeAssets } from '@/themes'

const theme = useThemeAssets()
  
const props = defineProps({
  usdPrice: {
    type: [Number, String],
    required: true
  },
  kaiaPrice: {
    type: [Number, String],
    required: true
  },
  processing: {
    type: Boolean,
    default: false
  },
  paymentStep: {
    type: String,
    default: 'initial'
  }
})

const emit = defineEmits(['select', 'close'])

const selectedMethod = ref(null)

const handlePaymentSelect = (method) => {
  if (props.processing) return
  selectedMethod.value = method
  emit('select', method)
}
</script>

<template>
  <div class="payment-content">    
    <div class="payment-order-content">
      <div class="payment-header">
        <div class="payment-title">{{ $t('common.paymentOrder') }}</div>
        <div class="close-button" @click="$emit('close')">
          <img :src="theme.closeBtn" alt="Close" class="close-button-image" />
        </div>
      </div>

      <div class="payment-options">
        <div 
          :class="['payment-option', 'usd-option', { 'processing': processing && selectedMethod === 'usd' }]" 
          @click="handlePaymentSelect('usd')">
          <div class="payment-icon">
            <img src="/iap/usd.png" alt="USD" />
          </div>
          <div v-if="processing && selectedMethod === 'usd'" class="loading-spinner"></div>
          <span>{{ usdPrice }} USD</span>
        </div>
        
        <div 
          :class="['payment-option', 'kaia-option', { 'processing': processing && selectedMethod === 'kaia' }]" 
          @click="handlePaymentSelect('kaia')">
          <div class="payment-icon">
            <img src="/iap/kaia.png" alt="KAIA" />
          </div>
          <div v-if="processing && selectedMethod === 'kaia'" class="loading-spinner"></div>
          <span>{{ kaiaPrice }} KAIA</span>
        </div>
              </div>
      
      <div class="payment-terms">
        <div class="term-item">1. You agree that the product(s)is/are non-refundable</div>
        <div class="term-item">2. If paid via LINE IAP , you agree to providing encrypted ID info to LY Corporation.</div>
      </div>
    </div>
  </div>
</template>

<style scoped>

* {
  font-family: 'Urbanist';
}

.payment-order-content {
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 10);
}



.payment-header {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.payment-title {
  font-size: calc(var(--base-unit) * 18);
  font-weight: bold;
  color: #6C2C0F;
  text-align: center;
  margin-bottom: calc(var(--base-unit) * 16);
  padding-bottom: calc(var(--base-unit) * 8);
  border-bottom: 2px solid #6C2C0F;
}

.close-button {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  width: calc(var(--base-unit) * 40);
  height: calc(var(--base-unit) * 40);
  position: absolute;
  right: 0;
  top: 0;
}

.close-button-image {
  width: calc(var(--base-unit) * 36);
  height: calc(var(--base-unit) * 36);
}

.payment-options {
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 12);
}

.payment-option {
  border-radius: calc(var(--base-unit) * 12);
  padding: calc(var(--base-unit) * 16) calc(var(--base-unit) * 20);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: transform 0.2s ease;
  font-size: calc(var(--base-unit) * 18);
  font-weight: bold;
  color: white;
  gap: calc(var(--base-unit) * 8);
}

.payment-option:hover {
  transform: scale(1.02);
}

.payment-option:active {
  transform: scale(0.98);
}

.payment-option.processing {
  cursor: not-allowed;
  opacity: 0.8;
}

.payment-option.processing:hover {
  transform: none;
}

.payment-option.processing:active {
  transform: none;
}

.usd-option,
.kaia-option {
  background: #FDC844;
  border: calc(var(--base-unit) * 3) solid #FF8A28;
  box-shadow: 0 0 0 calc(var(--base-unit) * 3) #252635;
}

.payment-option span {
  font-size: calc(var(--base-unit) * 18);
  font-weight: bold;
  color: #ffffff;
  -webkit-text-stroke-width: calc(var(--base-unit) * 2);
  -webkit-text-stroke-color: #3B3B3B;
  line-height: 0.2;
  display: flex;
  align-items: center;
}

.loading-spinner {
  width: calc(var(--base-unit) * 16);
  height: calc(var(--base-unit) * 16);
  border: calc(var(--base-unit) * 2) solid rgba(255, 255, 255, 0.3);
  border-top: calc(var(--base-unit) * 2) solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.payment-icon {
  height: calc(var(--base-unit) * 32);
  display: flex;
  align-items: center;
  justify-content: center;
}

.payment-icon img {
  height: calc(var(--base-unit) * 32);
  object-fit: contain;
}

.payment-terms {
  background: #B56A28;
  border-radius: calc(var(--base-unit) * 8);
  padding: calc(var(--base-unit) * 12);
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 8);
  text-align: left;
  border: 2px solid #6C2C0F;
}

.term-item {
  font-size: calc(var(--base-unit) * 11);
  color: #FCF1CB;
  line-height: 1.4;
}



@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 