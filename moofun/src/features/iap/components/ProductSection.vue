<script setup>
import { ShopLabel, ProductCard } from '@/features/iap'

  defineProps({
    products: {
      type: Array,
      required: true,
      default: () => []
    },
    categoryLabel: {
      type: String,
      required: true
    }
  })

  defineEmits(['product-click'])
</script>

<template>
  <div v-if="products.length > 0" class="product-section">
    <ShopLabel :text="categoryLabel" />
    <div class="products-grid">
      <component :is="ProductCard"  
        v-for="product in products"
        :key="product.id"
        :product-id="product.id"
        :name="product.name"
        :price="product.price"
        :variant="product.variant"
        :can-purchase="product.canPurchase"
        @click="$emit('product-click', product)"
      />
    </div>
  </div>
</template>

<style scoped>
.product-section {
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 24);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: calc(var(--base-unit) * 16);
  justify-content: center;
  max-width: calc(var(--base-unit) * 400);
  margin: 0;
}
</style> 