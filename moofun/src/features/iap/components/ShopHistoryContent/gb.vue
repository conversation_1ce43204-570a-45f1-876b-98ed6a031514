<script setup>
import { ref, onMounted } from 'vue'
import { getPurchaseHistory } from '@/features/iap'
import { useDappPortalSdk } from '@/features/auth'
import { TitlePanelColor } from '@/components/common'
import { useI18n } from 'vue-i18n'
import { devLog } from '@/utils/config'

const { t } = useI18n()
const { openPaymentHistory } = useDappPortalSdk()
const historyData = ref([])
const loading = ref(false)
const error = ref(null)
const sdkLoading = ref(false)
const pagination = ref({
  total: 0,
  page: 1,
  limit: 10,
  totalPages: 0
})

const loadPaymentHistory = async (page = 1) => {
  loading.value = true
  error.value = null
  
  try {
    devLog('Loading payment history, page:', page)
    const response = await getPurchaseHistory(page, pagination.value.limit)
    devLog('API response:', response)
    
    if (!response || !response.purchases) {
      throw new Error('Invalid response format')
    }
    
    historyData.value = response.purchases.map((purchase, index) => {
      try {
        return {
          id: purchase.paymentId || `purchase-${purchase.id}`,
          productName: purchase.IapProduct?.name || `Product #${purchase.productId}`,
          amount: (purchase.amount || 0).toString(),
          currency: purchase.currency,
          status: purchase.status || 'unknown',
          date: purchase.purchaseDate ? new Date(purchase.purchaseDate).toLocaleString('zh-CN') : '未知时间',
          paymentMethod: purchase.paymentMethod === 'stripe' ? 'usd' : 'kaia',
          originalData: purchase
        }
      } catch (itemError) {
        console.error(`Error processing purchase item ${index}:`, itemError, purchase)
        return {
          id: `error-${purchase.id || index}`,
          productName: 'Error loading product',
          amount: '0',
          currency: 'USD',
          status: 'error',
          date: '未知时间',
          paymentMethod: 'usd',
          originalData: purchase
        }
      }
    })
    
    devLog('Processed history data:', historyData.value)
    
    if (response.pagination) {
      pagination.value = {
        ...response.pagination,
        page: page
      }
    } else {
      const total = response.purchases.length
      const totalPages = Math.ceil(total / pagination.value.limit)
      pagination.value = {
        total: total,
        page: page,
        limit: pagination.value.limit,
        totalPages: Math.max(1, totalPages)
      }
    }
    
    devLog('Updated pagination:', pagination.value)
    
  } catch (err) {
    console.error('Failed to load payment history:', err)
    console.error('Error details:', err.message, err.stack)
    error.value = `Failed to load payment history: ${err.message}`
    historyData.value = []
    pagination.value = {
      total: 0,
      page: 1,
      limit: pagination.value.limit,
      totalPages: 0
    }
  } finally {
    loading.value = false
  }
}

const getStatusColor = (status) => {
  switch (status) {
    case 'CONFIRMED':
    case 'FINALIZED':
      return '#4CAF50'
    
    case 'CONFIRM_FAILED':
    case 'CHARGEBACK':
    case 'CANCELED':
      return '#F44336'
    
    case 'CREATED':
    case 'STARTED':
    case 'REGISTERED_ON_PG':
    case 'CAPTURED':
    case 'PENDING':
    case 'REFUNDED':
    default:
      return '#FF9800'
  }
}

const getStatusTextColor = (status) => {
  switch (status) {
    case 'CONFIRMED':
    case 'FINALIZED':
    case 'CONFIRM_FAILED':
    case 'CHARGEBACK':
    case 'CANCELED':
      return '#FFFFFF'
    
    case 'CREATED':
    case 'STARTED':
    case 'REGISTERED_ON_PG':
    case 'CAPTURED':
    case 'PENDING':
    case 'REFUNDED':
    default:
      return '#6C2C0F'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'CREATED': return t('history.created')
    case 'STARTED': return t('history.started')
    case 'REGISTERED_ON_PG': return t('history.registeredOnPg')
    case 'CAPTURED': return t('history.captured')
    case 'PENDING': return t('history.pending')
    case 'CONFIRMED': return t('history.confirmed')
    case 'FINALIZED': return t('history.finalized')
    case 'REFUNDED': return t('history.refunded')
    case 'CONFIRM_FAILED': return t('history.confirmFailed')
    case 'CANCELED': return t('history.canceled')
    case 'CHARGEBACK': return t('history.chargeback')
    default: return t('history.unknown')
  }
}

const loadNextPage = () => {
  console.log('Load next page clicked. Current page:', pagination.value.page, 'Total pages:', pagination.value.totalPages)
  if (pagination.value.page < pagination.value.totalPages) {
    loadPaymentHistory(pagination.value.page + 1)
  } else {
    console.log('Cannot load next page - already at last page')
  }
}

const loadPreviousPage = () => {
  console.log('Load previous page clicked. Current page:', pagination.value.page, 'Total pages:', pagination.value.totalPages)
  if (pagination.value.page > 1) {
    loadPaymentHistory(pagination.value.page - 1)
  } else {
    console.log('Cannot load previous page - already at first page')
  }
}

const copyOrderId = async (orderId) => {
  try {
    await navigator.clipboard.writeText(orderId)
  } catch (err) {
    console.error('Failed to copy order ID:', err)
  }
}

const openSdkPaymentHistory = async () => {
  sdkLoading.value = true
  try {
    devLog('Opening payment history via SDK')
    await openPaymentHistory()
    devLog('Payment history opened successfully')
  } catch (err) {
  } finally {
    sdkLoading.value = false
  }
}

onMounted(() => {
  loadPaymentHistory()
})
</script>

<template>
  <div class="history-page">

    <div class="page-header">
      <div class="header-row">
        <div class="title-col">
          <TitlePanelColor variant="brown" class="page-title">
            {{ $t('history.title') }}
          </TitlePanelColor>
        </div>
      </div>
    </div>

    <div class="history-container">

      <div v-if="loading" class="loading-state">
        <div class="spinner"></div>
        <div class="loading-text">{{ $t('history.loading') }}</div>
      </div>
      
      <div v-else-if="error" class="error-state">
        <div class="error-icon">⚠️</div>
        <div class="error-text">{{ error }}</div>
        <button class="retry-button" @click="() => loadPaymentHistory(1)">
          {{ $t('history.retry') }}
        </button>
      </div>
      
      <div v-else-if="historyData.length > 0" class="history-list">
        <div 
          v-for="item in historyData" 
          :key="item.id"
          class="history-item"
        >
          <div class="item-header">
            <div class="item-title">{{ item.productName }}</div>
            <div 
              class="item-status"
              :style="{ 
                backgroundColor: getStatusColor(item.status),
                color: getStatusTextColor(item.status)
              }"
            >
              <span>{{ getStatusText(item.status) }}</span>
            </div>
          </div>
          
          <div class="item-details">
            <div class="detail-row">
              <span class="detail-label">{{ $t('history.amount') }}:</span>
              <span class="detail-value">{{ item.amount }} {{ item.currency }}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">{{ $t('history.date') }}:</span>
              <span class="detail-value">{{ item.date }}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">{{ $t('history.orderId') }}:</span>
              <span class="detail-value order-id">
                <button 
                  class="copy-btn" 
                  @click="copyOrderId(item.id)"
                  :title="$t('history.copyOrderId')"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                  </svg>
                </button>
                {{ item.id.substring(0, 8) }}...{{ item.id.substring(item.id.length - 4) }}
              </span>
            </div>
            <div class="detail-row">
              <span class="detail-label">{{ $t('history.method') }}:</span>
              <span class="detail-value">
                <span>{{ item.currency === 'PHRS' ? 'PHRS' : item.paymentMethod.toUpperCase() }}</span>
              </span>
            </div>
          </div>
        </div>
        
        <div v-if="pagination?.totalPages > 1" class="pagination">
          <button 
            :disabled="pagination?.page <= 1"
            @click="loadPreviousPage"
            class="pagination-btn"
          >
            ←
          </button>
          
          <span class="pagination-info">
            {{ pagination?.page }} / {{ pagination?.totalPages }}
            ({{ $t('history.total') }}: {{ pagination?.total }})
          </span>
          
          <button 
            :disabled="pagination?.page >= pagination?.totalPages"
            @click="loadNextPage"
            class="pagination-btn"
          >
           →
          </button>
        </div>
      </div>
      
      <div v-else class="empty-state">
        <div class="empty-icon">💳</div>
        <div class="empty-text">{{ $t('history.noHistory') }}</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
* {
  font-family: 'Urbanist';
}

.history-page {
  margin: calc(var(--base-unit) * 10) calc(var(--base-unit) * 20);
  margin-bottom: calc(var(--base-unit) * 20);
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 24);
  min-height: calc(var(--app-height) - calc(var(--base-unit) * 40));
}

.page-header {
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 12);
}

.header-row {
  display: flex;
  align-items: center;
  gap: calc(var(--base-unit) * 16);
}

.title-col {
  flex: 8;
  min-width: 0;
}

.button-col {
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  flex-shrink: 0;
  height: 100%;
  
}

.page-title {
  margin-bottom: calc(var(--base-unit) * 8);
}

.history-container {
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 16);
  width: 100%;
  max-width: calc(var(--base-unit) * 500);
  margin: 0 auto;
}

.sdk-history-btn {
  background-color: rgb(192, 135, 74);
  box-shadow: 0 calc(var(--base-unit) * -4) calc(var(--base-unit) * 2) rgba(126, 78, 43, 0.5) inset;
  border: calc(var(--base-unit) * 2) solid rgba(166, 100, 52, 1);
  color: white;
  border-radius: calc(var(--base-unit) * 12);
  padding: calc(var(--base-unit) * 8);
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  position: relative;
  width: calc(var(--base-unit) * 42);
  height: calc(var(--base-unit) * 44);
  aspect-ratio: 1;
}

.sdk-history-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 calc(var(--base-unit) * -6) calc(var(--base-unit) * 3) rgba(126, 78, 43, 0.6) inset;
}

.sdk-history-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 calc(var(--base-unit) * -2) calc(var(--base-unit) * 1) rgba(126, 78, 43, 0.4) inset;
}

.sdk-history-btn:disabled {
  background: #8E8E93;
  cursor: not-allowed;
  opacity: 0.7;
  transform: none;
  box-shadow: none;
}

.sdk-history-btn .btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.sdk-history-btn .btn-icon .dapp-icon {
  width: calc(var(--base-unit) * 40);
  height: calc(var(--base-unit) * 40);
  object-fit: contain;
}

.btn-spinner {
  display: inline-block;
  width: calc(var(--base-unit) * 16);
  height: calc(var(--base-unit) * 16);
  border: calc(var(--base-unit) * 2) solid rgba(255, 255, 255, 0.3);
  border-top: calc(var(--base-unit) * 2) solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: calc(var(--base-unit) * 4);
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: calc(var(--base-unit) * 12);
  padding: calc(var(--base-unit) * 32);
}

.spinner {
  display: inline-block;
  width: calc(var(--base-unit) * 32);
  height: calc(var(--base-unit) * 32);
  border: calc(var(--base-unit) * 3) solid rgba(161, 193, 230, 0.3);
  border-top: calc(var(--base-unit) * 3) solid #2D7CCD;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #6C2C0F;
  font-size: calc(var(--base-unit) * 14);
  font-weight: 500;
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: calc(var(--base-unit) * 12);
  padding: calc(var(--base-unit) * 32);
}

.error-icon {
  font-size: calc(var(--base-unit) * 32);
}

.error-text {
  color: #6C2C0F;
  font-size: calc(var(--base-unit) * 14);
  text-align: center;
  font-weight: 500;
}

.retry-button {
  background: #FF6B35;
  color: white;
  border: 1px solid rgba(10, 13, 18, 0.18);
  border-radius: calc(var(--base-unit) * 12);
  padding: calc(var(--base-unit) * 12) calc(var(--base-unit) * 20);
  font-size: calc(var(--base-unit) * 14);
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.2s ease;
  box-shadow: 
    0 2px 8px rgba(255, 107, 53, 0.3),
    inset 0 0 0 1px rgba(10, 13, 18, 0.18),
    inset 0 -2px 0 0 rgba(10, 13, 18, 0.05);
}

.retry-button:hover {
  transform: scale(1.02);
}

.retry-button:active {
  transform: scale(0.98);
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 12);
}

.history-item {
  background: rgba(231, 213, 203, 1);
  border: 3px dashed #D8BEAF;
  border-radius: calc(var(--base-unit) * 16);
  padding: calc(var(--base-unit) * 20);
  transition: transform 0.2s ease;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.history-item:hover {
  transform: scale(1.01);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: calc(var(--base-unit) * 16);
  padding-bottom: calc(var(--base-unit) * 12);
  border-bottom: 2px solid #E9EAEB;
}

.item-title {
  font-size: calc(var(--base-unit) * 18);
  font-weight: bold;
  color: rgba(135, 106, 98, 1);
}

.item-status {
  font-size: calc(var(--base-unit) * 14);
  font-weight: bold;
  padding: calc(var(--base-unit) * 6) calc(var(--base-unit) * 12);
  border-radius: calc(var(--base-unit) * 8);
  line-height: normal;
}

.item-details {
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 8);
  line-height: normal;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: calc(var(--base-unit) * 12);
}

.detail-label {
  color: rgba(113, 118, 128, 1);
  font-weight: 500;
}

.detail-value {
  color: #6C2C0F;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: calc(var(--base-unit) * 4);
  padding-top: 2px;
}

.order-id {
  max-width: calc(var(--base-unit) * 200);
  text-align: right;
  font-size: calc(var(--base-unit) * 12);
  display: flex;
  align-items: center;
  gap: calc(var(--base-unit) * 4);
}

.copy-btn {
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0.6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6C2C0F;
}

.copy-btn:hover {
  background-color: rgba(108, 44, 15, 0.1);
  opacity: 1;
}

.copy-btn:active {
  transform: scale(0.95);
}

.copy-btn svg {
  width: calc(var(--base-unit) * 14);
  height: calc(var(--base-unit) * 14);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: calc(var(--base-unit) * 12);
  padding: calc(var(--base-unit) * 32);
}

.empty-icon {
  font-size: calc(var(--base-unit) * 48);
  opacity: 0.6;
}

.empty-text {
  color: rgba(113, 118, 128, 1);
  font-size: calc(var(--base-unit) * 14);
  text-align: center;
  font-weight: 500;
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: calc(var(--base-unit) * 16);
  padding: calc(var(--base-unit) * 8);
  background: #F7ECE6;
  border-radius: calc(var(--base-unit) * 12);
  border: 2px dashed #D8BEAF;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pagination-btn {
  background: #FFAA18;
  color: white;
  border: 1px solid rgba(10, 13, 18, 0.18);
  border-radius: calc(var(--base-unit) * 12);
  padding: calc(var(--base-unit) * 12) calc(var(--base-unit) * 20);
  font-size: calc(var(--base-unit) * 14);
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.2s ease;
  box-shadow: 
    0 2px 8px rgba(255, 107, 53, 0.3),
    inset 0 0 0 1px rgba(10, 13, 18, 0.18),
    inset 0 -2px 0 0 rgba(10, 13, 18, 0.05);
}

.pagination-btn:hover:not(:disabled) {
  transform: scale(1.02);
}

.pagination-btn:active:not(:disabled) {
  transform: scale(0.98);
}

.pagination-btn:disabled {
  background: #8E8E93;
  cursor: not-allowed;
  opacity: 0.5;
  transform: none;
}

.pagination-info {
  color: #6C2C0F;
  font-size: calc(var(--base-unit) * 14);
  text-align: center;
  font-weight: 500;
}
</style> 