import { ref } from 'vue'
import { useShopStore } from '../stores/shopStore'
import { devLog } from '@/utils/config'
import { useTheme } from '@/themes/useTheme'

export function usePayment() {
  const shopStore = useShopStore()
  const selectedProduct = ref(null)
  const showPurchaseModal = ref(false)

  const closePurchaseModal = () => {
    if (shopStore.processing) {
      shopStore.cancelPayment()
    }
    
    showPurchaseModal.value = false
    selectedProduct.value = null
  }

  const purchaseProduct = (product) => {
    if (!product.canPurchase) {
      return
    }
    selectedProduct.value = product
    showPurchaseModal.value = true
  }

  const selectPaymentMethod = async (method) => {
    if (!selectedProduct.value || shopStore.processing) return
    
    try {
      const { themeName } = useTheme()
      
      if (themeName === 'gb') {
        const response = await shopStore.purchaseWithPhrsBalance(selectedProduct.value.createdOrderID)
        devLog('PHRS Payment completed:', response)
        
        closePurchaseModal()
        await shopStore.fetchProducts()
      } else {
        const paymentData = {
          productId: selectedProduct.value.createdOrderID,
          imageUrl: `${window.location.origin}/iap/${selectedProduct.value.id}.png`,
          paymentMethod: method,
          testMode: true // import.meta.env.DEV
        }
        
        const response = await shopStore.purchaseProduct(paymentData)
        devLog('Payment created:', response)
        
        closePurchaseModal()
        await shopStore.fetchProducts()
      }
    } catch (error) {
      
    }
  }

  return {
    selectedProduct,
    showPurchaseModal,
    closePurchaseModal,
    purchaseProduct,
    selectPaymentMethod
  }
} 