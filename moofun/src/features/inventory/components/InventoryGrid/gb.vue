<script setup lang="ts">
import InventorySlot from '../InventorySlot'

interface InventoryItem {
  key: string | null
  icon: string | null
  name?: string
  quantity: number
}

defineProps<{
  items: InventoryItem[]
  selectedIndex: number | null
}>()

defineEmits<{
  (e: 'select', index: number, item: InventoryItem): void
}>()
</script>

<template>
  <div class="slot-container">
    <InventorySlot
      v-for="(item, index) in items"
      :key="index"
      :item="item"
      :selected="selectedIndex === index"
      @click="$emit('select', index, item)"
    />
  </div>
</template>

<style scoped>
.slot-container {
  display: flex;
  justify-content: space-between;
  align-items: center;

  flex-wrap: wrap;
  gap: calc(var(--base-unit) * 4);
}
</style>
