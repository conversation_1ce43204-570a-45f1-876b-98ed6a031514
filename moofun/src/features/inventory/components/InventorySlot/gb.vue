<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

interface InventoryItem {
  key: string | null
  icon: string | null
  name?: string
  quantity: number
}

defineProps<{
  item: InventoryItem
  selected?: boolean
}>()

defineEmits<{
  (e: 'click'): void
}>()
</script>

<template>
  <div class="slot" :class="[{ selected }, { empty: !item?.key }]" @click="$emit('click')">
    <div v-if="item?.key" class="slot-content">
      <div class="item-oval"></div>
      <img :src="item.icon" :alt="item.name" />
      <span class="item-quantity">{{ Number(item.quantity).toFixed() }}</span>
    </div>
  </div>
</template>

<style scoped>
.slot {
  width: calc(var(--base-unit) * 70);
  height: calc(var(--base-unit) * 84);
  border-radius: calc(var(--base-unit) * 12);
  background: #F0DED4;
  border: calc(var(--base-unit) * 2) dashed #D8BEAF;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
}

.slot.selected {
  background: #F7ECE6;
}

.slot.empty {
  background: #D4B7A7;
}

.slot-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}

.item-oval {
  position: absolute;
  top: 60%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: calc(var(--base-unit) * 56);
  height: calc(var(--base-unit) * 18);
  background: #D8BEAF;
  border-radius: 50% / 50%;
  z-index: 0;
}

.slot-content img {
  width: calc(var(--base-unit) * 48);
  height: calc(var(--base-unit) * 48);
  object-fit: contain;
  margin-bottom: calc(var(--base-unit) * 4);
  position: relative;
  z-index: 1;
}

.item-quantity {
  color: #252B37;
  font-size: calc(var(--base-unit) * 14);
  font-weight: bold;
}

.slot.selected .item-quantity {
  color: #876A62;
}
</style>
