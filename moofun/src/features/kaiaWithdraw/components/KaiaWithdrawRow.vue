<script setup>
import { AppRow } from '@/components/common';
import { useKaiaWithdrawPopupStore } from '../stores/kaiaWithdrawPopupStore'
import audioService from '@/lib/audioService'

const kaiaWithdrawPopupStore = useKaiaWithdrawPopupStore()

const OpenPopup = () => {
  audioService.play('button1'); // Play sound effect
  kaiaWithdrawPopupStore.openKaiaWithdrawPopup()
}
</script>

<template>
    <AppRow :iconSrc="'/icon/trophy.png'" :showArrow="true" :clickable="true" @click="OpenPopup">
      <slot>{{ $t('profile.tonRewardBalance') }}</slot>
    </AppRow>
</template>
  
<style scoped>

</style>