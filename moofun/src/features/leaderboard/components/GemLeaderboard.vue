<script setup>
import { useI18n } from 'vue-i18n'
import { useGemLeaderboard } from '../composables/useGemLeaderboard'
import { TitlePanelColor, DataTable, AppPagination } from '@/components/common'

const { t } = useI18n()

const columns = [
  { title: t('leaderboard.rank'), key: 'rank', width: '10%', class: 'text-center' },
  { title: t('leaderboard.user'), key: 'user', width: '30%', class: 'with-padding-left' },
  { title: t('leaderboard.gemAmount'), key: 'gemAmount', width: '30%', class: 'text-center' }
]

const {
  leaderboard,
  page,
  pageSize,
  total,
  loading,
  changePage
} = useGemLeaderboard()
</script>

<template>
  <div class="gem-leaderboard">
    <TitlePanelColor variant="green">
      {{ t('leaderboard.title') }}
    </TitlePanelColor>

    <DataTable :columns="columns" :data="leaderboard" :loading="loading" />

    <AppPagination  
      v-if="total > pageSize"
      :page="page"
      :total="total"
      :pageSize="pageSize"
      @update:page="changePage"
    />
  </div>
</template>

<style scoped>
.gem-leaderboard {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 16);
}
</style> 