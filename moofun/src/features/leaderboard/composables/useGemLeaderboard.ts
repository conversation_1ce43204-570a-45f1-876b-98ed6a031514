import { ref, onMounted } from 'vue'
import { getGemLeaderboard } from '../api'

export const useGemLeaderboard = () => {
  const leaderboard = ref<any[]>([])
  const page = ref(1)
  const pageSize = 10
  const total = ref(0)
  const loading = ref(false)

  const fetchLeaderboard = async () => {
    loading.value = true
    const offset = (page.value - 1) * pageSize

    try {
      const result = await getGemLeaderboard(pageSize, offset)
      const users = Array.isArray(result?.data?.leaderboard) ? result.data.leaderboard : []

      total.value = result?.data?.pagination?.total || 0

      leaderboard.value = users.map((user: any) => ({
        rank: offset + user.rank,
        user: user.username || user.firstName || user.walletAddress?.slice(0, 6) + '...',
        gemAmount: user.gemAmount.toLocaleString()
      }))
    } catch (err) {
      console.error('Failed to fetch gem leaderboard:', err)
    } finally {
      loading.value = false
    }
  }

  const changePage = (newPage: number) => {
    page.value = newPage
    fetchLeaderboard()
  }

  onMounted(fetchLeaderboard)

  return {
    leaderboard,
    page,
    pageSize,
    total,
    loading,
    changePage,
  }
} 