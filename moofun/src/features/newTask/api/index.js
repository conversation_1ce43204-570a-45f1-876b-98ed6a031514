import fetch from '@/lib/fetch'

export const claimNewTask = (taskId) => {
  return fetch.post('/new-tasks/claim', { taskId }).then(res => res.data)
}

export const fetchNewTaskList = () => {
  return fetch.get('/new-tasks/user', { params: { showAll: false } }).then(res => res.data)
}

export const fetchMainTask = () => {
  return fetch.get('/new-tasks/user', { params: { main: true } }).then(res => res.data)
}