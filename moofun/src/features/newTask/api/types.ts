export interface GetNewTaskResponse {
    tasks: Task[];
    mainTask: Task;
    total: number;
}

export enum TaskConfigType {
    UNLOCK_AREA = 1,
    UPGRADE_FARM = 2,
    UPGRADE_DELIVERY = 3,
    INVITE_FRIENDS = 4
}

export interface Task {
    id: number;
    taskId: number;
    status: "not_accepted" | "accepted" | "completed" | "claimed";
    statusDescription: string;
    currentProgress: number;
    targetProgress: number;
    progressText: string;
    progressPercentage: number;
    canClaim: boolean;
    isCompleted: boolean;
    isClaimed: boolean;
    isInProgress: boolean;
    acceptedAt: string;
    completedAt: string;
    claimedAt?: any;
    displayPriority: number;
    taskConfig: TaskConfig;
}

export interface TaskConfig {
    id: number;
    describe: string;
    type: TaskConfigType;
    typeDescription: string;
    parameterDescription: string;
    rewards: Reward[];
    hasRewards: boolean;
}

export interface Reward {
    type: "diamond" | "box" | "coin" | "item";
    amount: number;
}

export interface GetNewTaskParams {
    main: boolean
    showAll: boolean
}
