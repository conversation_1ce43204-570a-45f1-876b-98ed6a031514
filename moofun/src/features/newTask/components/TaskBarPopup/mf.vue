<template>
    <BoosterPopup :isOpen="isNewTaskShow" @close="closeNewTaskPopup()">
        <TitlePanelColor variant="brown2">
            {{ t('newTask.title') }}
        </TitlePanelColor>

        <div class="task-list">
            <TransitionGroup name="fade">
                <TaskItem v-for="task in tasks" :key="task.id" :task="task" @claim="() => handleClaimClick(task)"
                    v-show="!task.isClaimed" />
            </TransitionGroup>
        </div>
    </BoosterPopup>
</template>

<script setup>
import { useI18n } from 'vue-i18n';
import { TaskItem, useTaskBarPopup } from '@/features/newTask'
import { BoosterPopup } from '@/features/gameplay/booster/index'
import TitlePanelColor from '@/components/common/TitlePanelColor/gb.vue'

const { t } = useI18n()
const { tasks, isNewTaskShow, handleClaimClick, closeNewTaskPopup } = useTaskBarPopup()
</script>

<style scoped src="./style.css"></style>
<style scoped>
.task-list {
    margin-top: calc(var(--base-unit) * 16);
}
</style>
