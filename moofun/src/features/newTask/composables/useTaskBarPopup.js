
import { storeToRefs } from 'pinia'
import { useNewTaskPopupStore, useNewTaskStore } from '@/features/newTask'
import audioService from '@/lib/audioService'
import { useChestOverlayStore } from '@/features/openChest/stores/chestOverlayStore'
import { useMainTaskStore } from '@/features/newTask'
import { useUserInfo } from '@/features/userInfo'

export function useTaskBarPopup() {
    const chestOverlayStore = useChestOverlayStore();
    const newTaskStore = useNewTaskStore()
    const { updateMainTask } = useMainTaskStore()
    const newTaskPopupStore = useNewTaskPopupStore()
    const { tasks } = storeToRefs(newTaskStore)
    const { isNewTaskShow } = storeToRefs(newTaskPopupStore)

    const { openChest } = chestOverlayStore
    const { closeNewTaskPopup } = newTaskPopupStore
    const { claim, fetchNewTasks } = newTaskStore
    const { fetchUserInfo } = useUserInfo()


    async function handleClaimClick(task) {
        const taskIndex = tasks.value.findIndex(item => item.taskId === task.taskId)

        if (taskIndex === -1)
            throw new Error('Task not found')

        if (!task.canClaim || task.isClaimed)
            return

        audioService.play('button1')
        const data = await claim(task.taskId)

        tasks.value[taskIndex].isClaimed = true

        Promise.all([
            updateMainTask(),
            fetchNewTasks(),
            fetchUserInfo()
        ])

        if (data?.chestRewards) {
            for (const reward of data.chestRewards) {
                await openChest({ ok: true, data: reward })
            }
        }
    }

    return {
        tasks,
        isNewTaskShow,
        handleClaimClick,
        closeNewTaskPopup
    }
}