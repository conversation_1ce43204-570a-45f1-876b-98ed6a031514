import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useNewTaskStore } from '@/features/newTask'

export const useNewTaskPopupStore = defineStore('NewTaskPopup', () => {
  const isNewTaskShow = ref(false)
  const newTaskStore = useNewTaskStore()
  const openNewTaskPopup = async () => {
    await newTaskStore.fetchNewTasks()
    isNewTaskShow.value = true
  }
  const closeNewTaskPopup = () => (isNewTaskShow.value = false)

  return {
    isNewTaskShow,
    openNewTaskPopup,
    closeNewTaskPopup,
  }
})
