import { defineStore } from 'pinia'
import { fetchNewTaskList, claimNewTask } from '@/features/newTask'
import { ref } from 'vue'
import { devLog } from '@/utils/config'
import { useNotificationStore } from '@/features/notification'
import { useI18n } from 'vue-i18n'

export const useNewTaskStore = defineStore('NewTask', () => {
    const tasks = ref([])
    const { t } = useI18n()
    const { addNotification } = useNotificationStore()

    const fetchNewTasks = async () => {
        try {
            const result = await fetchNewTaskList()
            tasks.value = result.tasks
            if (Array.isArray(result.tasks)) {
                tasks.value = result.tasks
                devLog("getNewTasks", tasks.value);
            } else {
                tasks.value = []
                throw new Error('Failed to fetch new tasks.')
            }
        } catch (err) {
            console.error('fetchNewTasks error:', err)
            tasks.value = []
        }
    }

    async function claim(taskId) {
        try {
            const data = await claimNewTask(taskId)

            if (!data?.rewards) {
                throw new Error('Failed to claim task.')
            }

            addNotification({ type: 'success', message: t('newTask.claimSuccess'), duration: 3000 })

            return data.rewards
        } catch (error) {
            console.error('Failed to claim:', error);
            addNotification({ type: 'error', message: t('newTask.claimError'), duration: 3000 })
            return
        }
    }

    return {
        tasks,
        fetchNewTasks,
        claim,
    }
})
