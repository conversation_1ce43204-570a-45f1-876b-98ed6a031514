<script setup>
import { ref, onBeforeUnmount } from 'vue'
import { useI18n } from 'vue-i18n'
import { useChestOverlayStore } from '../stores/chestOverlayStore'
import ChestAnimation from './ChestAnimation.vue'
import ChestItemsView from './ChestItemsView.vue'
import ChestShareView from './ChestShareView.vue'
import ChestSummaryView from './ChestSummaryView.vue'
import ChestNextView from './ChestNextView.vue'

const chestOverlay = useChestOverlayStore()
const { t } = useI18n()
const currentStep = ref('initial')
const animationTimeout = ref(null)
const showJackpot = ref(false)

const handleOverlayClick = () => {
  if (currentStep.value === 'initial' && !showJackpot.value) {
    openChestAnimation()
  }
}

const openChestAnimation = () => {
  if (currentStep.value !== 'initial') return
  currentStep.value = 'opening'
  animationTimeout.value = setTimeout(() => {
    currentStep.value = 'opened'
    if (chestOverlay.jackpotWinner) {
      showJackpot.value = true
    }
  }, 800)
}

const goToItems = () => {
  currentStep.value = 'items'
}

const goToShare = () => {
  currentStep.value = 'share'
  if (!chestOverlay.currentChestConfig.shareLink) {
    goToNextChest()
  }
}

const goToNextChest = () => {
  currentStep.value = 'nextChest'
}

const openNextChest = () => {
  if (chestOverlay.hasNextChest) {
    chestOverlay.moveToNextChest()
    currentStep.value = 'initial'
  }
}

const closeOverlay = () => {
  chestOverlay.closeOverlay()
  currentStep.value = 'initial'
  if (animationTimeout.value) clearTimeout(animationTimeout.value)
}

const handleJackpotAccept = () => {
  chestOverlay.jackpotWinner = null
}

onBeforeUnmount(() => {
  if (animationTimeout.value) clearTimeout(animationTimeout.value)
})
</script>


<template>
  <div class="chest-overlay" v-if="chestOverlay.isVisible" @click="handleOverlayClick">


    <div v-if="!showJackpot" @click="handleOverlayClick">
      <ChestAnimation
        :step="currentStep"
        @open="handleOverlayClick"
        @next="goToItems"
      />

      <ChestItemsView
        v-if="currentStep === 'items'"
        @next="goToShare"
      />

      <ChestShareView
        v-if="currentStep === 'share' && chestOverlay.currentChestConfig.shareLink"
        @next="goToNextChest"
      />

      <ChestNextView
        v-if="currentStep === 'nextChest' && chestOverlay.hasNextChest"
        @next="openNextChest"
      />

      <ChestSummaryView
        v-if="currentStep === 'nextChest' && !chestOverlay.hasNextChest"
        @close="closeOverlay"
      />
    </div>
  </div>
</template>

<style scoped>
.chest-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
</style>