<template>
    <div class="card-container" :style="containerStyle">
      <div class="card-content" :class="{ 'card-content-decor': contentDecorShow }" :style="contentStyle">
        <div class="icon-container" :style="iconContainerStyle">
          <img :src="imageSrc" alt="Unlimited Gems" class="icon">
        </div>
        <div class="card-text main-text">{{ title }}</div>
        <slot class="card-text main-text"></slot>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    name: 'ResourceCard',
    props: {
      title: {
        type: String,
        default: 'Unlimited Gems'
      },
      imageSrc: {
        type: String,
        default: '/icon/coins.png'
      },
      bgColor: {
        type: String,
        default: '#FEF5D0'
      },
      borderColor: {
        type: String,
        default: '#000000'
      },
      contentBg: {
        type: String,
        default: '#FFA402'
      },
      contentShadow: {
        type: String,
        default: '0 calc(var(--base-unit) * 2) calc(var(--base-unit) * 4) rgba(0, 0, 0, 0.2)'
      },
      /** 控制是否显示背景装饰效果，设置为 true 开启装饰，false 关闭装饰 */
      contentDecorShow:{
        type: Boolean,
        default: false
      },
      iconBg: {
        type: String,
        default: '#FBDF63'
      },
      iconBorderColor: {
        type: String,
        default: '#FEF5D0'
      },
      iconBorderWidth: {
        type: Number,
        default: 2
      }
    },
    computed: {
      containerStyle() {
        return {
          background: this.bgColor,
          border: `calc(var(--base-unit) * 1) solid ${this.borderColor}`
        };
      },
      contentStyle() {
        return {
          background: this.contentBg,
          boxShadow: this.contentShadow,
        };
      },
      iconContainerStyle() {
        return {
          background: this.iconBg,
          border: `calc(var(--base-unit) * ${this.iconBorderWidth}) solid ${this.iconBorderColor}`
        };
      }
    }
  }
  </script>
  
  <style scoped>
  .card-container {
    padding: calc(var(--base-unit) * 4);
    width: calc(var(--base-unit) * 92);
    border-radius: calc(var(--base-unit) * 12);
    box-sizing: border-box;
  }
  
  .card-content {
    height: calc(var(--base-unit) * 128);
    padding: calc(var(--base-unit) * 8);
    border-radius: calc(var(--base-unit) * 12);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: calc(var(--base-unit) * 8);
    box-sizing: border-box;
    position: relative;
  }
  
  .card-content-decor::before {
    content: '';
    position: absolute;
    display: block;
    top: 0;
    z-index: 0;
    width: 100%;
    height: 100%;
    background: url('/ui/resource-card-icon-border.png') no-repeat;
    background-size: 100%;
    background-position: 0 calc(var(--base-unit) * 4);
  }

  .icon-container {
    border-radius: calc(var(--base-unit) * 8);
    width: calc(var(--base-unit) * 70);
    height: calc(var(--base-unit) * 70);
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    position: relative;
  }
  
  .icon {
    width: calc(var(--base-unit) * 56);
    height: calc(var(--base-unit) * 56);
    box-sizing: border-box;
  }
  
  .card-text {
    font-size: calc(var(--base-unit) * 8);
    text-shadow: none;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    line-height: calc(var(--base-unit) * 12);
    flex-grow: 1; /* Make the text take up the remaining space */
    width: 100%; /* Ensure it spans the width */
  }
  </style>
  