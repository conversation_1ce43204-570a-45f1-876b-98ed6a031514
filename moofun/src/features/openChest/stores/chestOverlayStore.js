import { defineStore } from 'pinia';
import { useUserInfo } from '@/features/userInfo'

import { useChestAssets } from '../composables/useChestAssets'

export const useChestOverlayStore = defineStore('chestOverlay', {
  state: () => ({
    isVisible: false,
    currentChestIndex: 0,
    openedCount: 0,
    chestIds: [],
    allRewards: [],
    summary: {},
    levelSummary: {},
    jackpotWinner: null,
    shareLinks: [],
    resolveTrigger: () => { }
  }),

  getters: {
    hasNextChest: (state) => state.currentChestIndex < state.openedCount - 1,

    currentShareLink: (state) => {
      const currentId = state.chestIds[state.currentChestIndex]
      const link = state.shareLinks.find((l) => l.chestId === currentId)
      return link?.shareCode || null
    },

    currentChestReward: (state) => {
      return state.allRewards[state.currentChestIndex] || null
    },

    currentChestConfig: (state) => {
      const reward = state.allRewards[state.currentChestIndex]
      const chestId = state.chestIds[state.currentChestIndex]
      if (!reward) return null

      const { formatItemName, getItemImage } = useChestAssets()

      const items = reward.items.map((item) => ({
        id: `${item.type}-${Math.random().toString(36).substr(2, 9)}`,
        name: formatItemName(item.type, item.amount),
        image: getItemImage(item.type),
        amount: item.amount,
        type: item.type
      }))

      return {
        chestId,
        chestRarity: reward.level,
        items,
        shareLink: state.shareLinks.find((s) => s.chestId === chestId)?.shareCode || null
      }
    }
  },

  actions: {
    processChestResponse(response) {
      if (!response?.ok) {
        console.error('Invalid chest response')
        return false
      }

      const data = response.data

      this.openedCount = data.openedCount || 0
      this.chestIds = data.chestIds || []
      this.allRewards = data.rewards || []
      this.summary = data.summary || {}
      this.levelSummary = data.levelSummary || {}
      this.shareLinks = data.shareLinks || []
      this.jackpotWinner = data.jackpotWinner || null
      this.currentChestIndex = 0

      return this.openedCount > 0
    },

    openChest(apiResponse = null) {
      if (apiResponse && !this.processChestResponse(apiResponse)) return
      this.isVisible = true
      return new Promise((resolve) => {
        this.resolveTrigger = resolve
      })
    },

    moveToNextChest() {
      if (!this.hasNextChest) return false
      this.currentChestIndex++
      return true
    },

    closeOverlay() {
      this.isVisible = false
      this.currentChestIndex = 0
      this.jackpotWinner = null
      this.resolveTrigger()      
      
      const { fetchUserInfo } = useUserInfo()
      fetchUserInfo()
    }
  }
});