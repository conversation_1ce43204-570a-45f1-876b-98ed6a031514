/* shared.css */
.oc-button {
  background-color: #FFAA18;
  color: #FFF;
  border: none;
  border-radius: calc(var(--base-unit) * 6);
  padding: calc(var(--base-unit) * 8) calc(var(--base-unit) * 16);
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
}

.oc-button:hover {
  transform: scale(1.05);
  box-shadow: 0 0 calc(var(--base-unit) * 10) rgba(255, 170, 24, 0.7);
}

.oc-heading {
  color: #FFAA18;
  margin-bottom: 1.5rem;
}

.oc-subheading {
  color: #FFAA18;
  font-size: 1.2rem;
  margin-bottom: 1rem;
  text-align: center;
}
