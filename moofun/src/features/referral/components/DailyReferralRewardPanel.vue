  <script setup>
  import { useI18n } from 'vue-i18n';
  import { useDailyReferralReward } from '../composables/useDailyReferralReward'
  import DailyReferralRewardButton from './DailyReferralRewardButton.vue'
  import ReferralProgressBar from './ReferralProgressBar'
  import { AppModal, TargetBox } from '@/components/common'
  
  // i18n
  const { t } = useI18n();


  const { referralStatus } = useDailyReferralReward()

  </script>
  
<template>
    <AppModal class="info-container">
      <div class="info-text">{{ t('invite.infoText') }}</div>
  
      <div class="row-group target-text">
        <TargetBox>{{ t('invite.dailyChestTargets', { count: 1 }) }}</TargetBox>
        <TargetBox>{{ t('invite.dailyChestTargets', { count: 2 }) }}</TargetBox>
        <TargetBox>{{ t('invite.dailyChestTargets', { count: 3 }) }}</TargetBox>
        <TargetBox>{{ t('invite.dailyChestTargets', { count: 4 }) }}</TargetBox>
        <TargetBox>{{ t('invite.dailyChestTargets', { count: 5 }) }}</TargetBox>
      </div>

      <ReferralProgressBar
        :value="referralStatus.currentReferrals"
        :milestones="[2, 5, 10, 20, 30]"
        :max="30"
      >
        <template #label="{ milestone }">
          {{ milestone }} {{ t('invite.user') }}
        </template>
      </ReferralProgressBar>

      <DailyReferralRewardButton />
    </AppModal>
  </template>

  
  <style scoped>
  .row-group {
    display: flex;
    gap: calc(var(--base-unit) * 10);
  }
  
  .target-text {
    color: rgba(153, 98, 0, 1);
    font-size: calc(var(--base-unit) * 10);
    line-height: calc(var(--base-unit) * 14);
    text-align: center;
  }
  
  .info-container {
    padding: calc(var(--base-unit) * 24) calc(var(--base-unit) * 16);
  }
  
  .info-text {
    text-align: start;
    font-size: calc(var(--base-unit) * 14);
    line-height: calc(var(--base-unit) * 20);
  }
  </style>
  