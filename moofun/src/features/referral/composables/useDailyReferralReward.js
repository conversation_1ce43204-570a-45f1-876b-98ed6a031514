import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useNotificationStore } from '@/features/notification'
import { useChestOverlayStore } from '@/features/openChest/stores/chestOverlayStore'
import { getReferralStatus, claimDailyReferralChest } from '../api'
import audioService from '@/lib/audioService'
import { devLog } from '@/utils/config'

export function useDailyReferralReward() {
  const { t } = useI18n()
  const isLoading = ref(false)
  const referralStatus = ref([])
  const dailyAvailable = ref(false)

  const notificationStore = useNotificationStore()
  const chestOverlayStore = useChestOverlayStore()

  const fetchReferralStatus = async () => {
    try {
      const res = await getReferralStatus()
      devLog("getReferralStatus", res)
      referralStatus.value = res;
      dailyAvailable.value = res?.status === 2
    } catch (err) {
      console.error('Failed to fetch referral status', err)
      dailyAvailable.value = false
    }
  }


  const handleClaim = async () => {
    if (!dailyAvailable.value) return

    isLoading.value = true
    try {
      const res = await claimDailyReferralChest()
      audioService.play('button1')

      if (res?.rewards) {
        chestOverlayStore.openChest({ ok: true, data: res.rewards })
        fetchReferralStatus()
      } else {
        notificationStore.addNotification({
          type: 'error',
          message: res?.message || t('notification.failedToCollect'),
          duration: 3000
        })
      }
    } catch (err) {
      console.error('Error claiming daily referral chest', err)
      notificationStore.addNotification({
        type: 'error',
        message: t('notification.errorCollecting'),
        duration: 3000
      })
    } finally {
      isLoading.value = false
    }
  }

  onMounted(fetchReferralStatus)

  return {
    referralStatus,
    isLoading,
    dailyAvailable,
    handleClaim
  }
}
