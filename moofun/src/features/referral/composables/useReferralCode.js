import { computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useNotificationStore } from '@/features/notification'
import { useUserInfo } from '@/features/userInfo'
import { generateReferralLink } from '@/utils/config'

import audioService from '@/lib/audioService'
import { detectPlatform } from '@/utils/platformDetection'

export function useReferralCode() {
  const { t } = useI18n()
  const notificationStore = useNotificationStore()
  const { userInfo, fetchUserInfo } = useUserInfo()

  const referralCode = computed(() => userInfo.value?.code || '')

  const copyToClipboard = async () => {
    const link = generateReferralLink(referralCode.value)
    if (!link) return

    try {
      await navigator.clipboard.writeText(link)
      audioService.play('button1')

      notificationStore.addNotification({
        type: 'success',
        title: t('referral.linkCopied'),
        message: t('referral.linkCopiedToClipboard'),
        duration: 3000
      })
    } catch (err) {
      console.error(t('referral.failedToCopyError'), err)
      notificationStore.addNotification({
        type: 'error',
        message: t('referral.failedToCopyLink'),
        duration: 3000
      })
    }
  }

  onMounted(() => {
    fetchUserInfo()
  })

  return {
    referralCode,
    copyToClipboard
  }
}
