import { ref, computed, watch, toRef } from 'vue'
import { getDownlineList } from '../api'
import { devLog } from '@/utils/config'

interface DownlineUser {
  userId: number
  username?: string
  weekBoostMinutes: number
  todayBoostMinutes: number
}

interface FormattedUser {
  id: number
  username: string
  weekBoost: number
  dayBoost: number
  originalData: DownlineUser
}

interface Pagination {
  page: number
  pageSize: number
  total: number
}

export function useReferralDetails(isOpen: () => boolean) {
  const activeTab = ref(0)
  const level1Data = ref<FormattedUser[]>([])
  const level2Data = ref<FormattedUser[]>([])
  const loading = ref(false)
  const pagination = ref<Pagination>({ page: 1, pageSize: 10, total: 0 })

  const currentLevel = computed(() => activeTab.value + 1)

  const displayData = computed(() =>
    activeTab.value === 0 ? level1Data.value : level2Data.value
  )

  const fetchDownlineData = async () => {
    loading.value = true
    try {
      const res = await getDownlineList(currentLevel.value, pagination.value.page, pagination.value.pageSize)
      devLog("getDownlineList", res);
      if (res?.users) {
        const formatted = res.users.map((user: DownlineUser, index: number) => ({
          id: index + 1,
          username: user.username || `User_${user.userId}`,
          weekBoost: user.weekBoostMinutes,
          dayBoost: user.todayBoostMinutes,
          originalData: user
        }))

        if (activeTab.value === 0) level1Data.value = formatted
        else level2Data.value = formatted

        pagination.value.total = res.total
        pagination.value.page = res.page
        pagination.value.pageSize = res.pageSize
      }
    } catch (err) {
      console.error('Failed to fetch downline data', err)
    } finally {
      loading.value = false
    }
  }

  const handleTabChange = (index: number) => {
    activeTab.value = index
    pagination.value.page = 1
    fetchDownlineData()
  }

  const changePage = (page: number) => {
    pagination.value.page = page
    fetchDownlineData()
  }

  watch(isOpen, (open) => {
    if (open) fetchDownlineData()
  }, { immediate: true })

  return {
    activeTab,
    displayData,
    pagination,
    loading,
    handleTabChange,
    changePage
  }
}
