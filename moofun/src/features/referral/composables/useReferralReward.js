import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useI18n } from 'vue-i18n'
import { useNotificationStore } from '@/features/notification'
import { useChestOverlayStore } from '@/features/openChest/stores/chestOverlayStore'
import { getReferralChestCount, openReferralChest } from '../api'
import audioService from '@/lib/audioService'
import { devLog } from '@/utils/config'

export function useReferralReward() {
  const { t } = useI18n()
  const notificationStore = useNotificationStore()
  const chestOverlayStore = useChestOverlayStore()

  const isLoading = ref(false)
  const chestCount = ref(0)
  const refreshInterval = ref(null)

  const fetchChestCount = async () => {
    try {
      const res = await getReferralChestCount()
      devLog("getReferralChestCount", res);
      chestCount.value = res?.count?.total || 0
    } catch (err) {
      console.error('Error fetching referral chest count:', err)
      chestCount.value = 0
    }
  }

const handleOpenReferralChests = async () => {
  if (chestCount.value <= 0) return

  isLoading.value = true

  try {
    const res = await openReferralChest()
    audioService.play('button1')

    if (res) {
      chestOverlayStore.openChest({ ok: true, data: res })
      fetchChestCount()
    } else {
      notificationStore.addNotification({
        type: 'error',
        message: t('chest.failedToOpenChests'),
        duration: 3000
      })
    }
  } catch (err) {
    console.error('Error opening referral chests:', err)

    notificationStore.addNotification({
      type: 'error',
      message: err?.response?.data?.message || t('chest.errorOpeningChests'),
      duration: 3000
    })
  } finally {
    isLoading.value = false
  }
}


  onMounted(() => {
    fetchChestCount()
    refreshInterval.value = window.setInterval(fetchChestCount, 5 * 60 * 1000)
  })

  onBeforeUnmount(() => {
    if (refreshInterval.value) clearInterval(refreshInterval.value)
  })

  return {
    isLoading,
    chestCount,
    handleOpenReferralChests
  }
}
