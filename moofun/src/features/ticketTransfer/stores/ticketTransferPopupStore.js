
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useTicketTransferPopupStore = defineStore('ticketTransferPopup', () => {

  const isTicketTransferShow= ref(false)
  const openTicketTransferPopup = () => (isTicketTransferShow.value = true)
  const closeTicketTransferPopup = () => (isTicketTransferShow.value = false)

  return {
    isTicketTransferShow,
    openTicketTransferPopup,
    closeTicketTransferPopup,
  }
})
