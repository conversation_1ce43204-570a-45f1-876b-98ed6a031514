<script setup>
import { ref, computed, onMounted } from 'vue'
import { AppInput, TitlePanelColor, AppPopup, ColorButton } from '@/components/common'
import { useTopupPopupStore } from '../stores/topupPopupStore'
import { useDeposit } from '../composables/useDeposit'
import { useI18n } from 'vue-i18n'
import { useNotificationStore } from '@/features/notification'
import { devLog } from '@/utils/config'

const { t, locale } = useI18n()
const topupPopupStore = useTopupPopupStore()
const notificationStore = useNotificationStore()
const { doDeposit, getContractInfo, isProcessing, error } = useDeposit()

const amount = ref(0)
const contractInfo = ref(null)
const isLoadingContractInfo = ref(false)

const formatNumber = (value) => {
  if (!value) return '0'
  
  const num = parseFloat(value)
  if (isNaN(num)) return value
  
  const currentLocale = locale.value
  
  if (num >= 1e12) {
    const formatted = (num / 1e12).toFixed(2)
    if (currentLocale === 'zh') return `${formatted}万亿`
    if (currentLocale === 'ja') return `${formatted}兆`
    return `${formatted}T`
  } else if (num >= 1e9) {
    const formatted = (num / 1e9).toFixed(2)
    if (currentLocale === 'zh') return `${formatted}十亿`
    if (currentLocale === 'ja') return `${formatted}十億`
    return `${formatted}B`
  } else if (num >= 1e6) {
    const formatted = (num / 1e6).toFixed(2)
    if (currentLocale === 'zh') return `${formatted}百万`
    if (currentLocale === 'ja') return `${formatted}百万`
    return `${formatted}M`
  } else if (num >= 1e3) {
    const formatted = (num / 1e3).toFixed(2)
    if (currentLocale === 'zh') return `${formatted}千`
    if (currentLocale === 'ja') return `${formatted}千`
    return `${formatted}K`
  } else {
    const decimalPlaces = num < 0.01 ? 8 : 6
    return num.toFixed(decimalPlaces).replace(/\.?0+$/, '')
  }
}

const formatTransactionHash = (hash) => {
  if (!hash || hash.length < 10) return hash
  return `${hash.slice(0, 6)}...${hash.slice(-4)}`
}

const canTopup = computed(() => {
  return amount.value > 0 && !isProcessing.value;
});

const loadContractInfo = async () => {
  isLoadingContractInfo.value = true
  try {
    contractInfo.value = await getContractInfo()
  } catch (error) {
    notificationStore.addNotification({
      type: 'error',
      message: t('topup.failedToLoadContractInfo'),
      duration: 5000
    })
  } finally {
    isLoadingContractInfo.value = false
  }
}

const handleTopup = async () => {
  if(!canTopup.value) return;
  
  try {
    
    const result = await doDeposit(amount.value.toString())
    
    if (result.success) {
      notificationStore.addNotification({
        type: 'success',
        message: `${t('topup.success')} ${t('topup.shortHash')}: ${formatTransactionHash(result.transactionHash)}`,
        duration: 5000
      })
      
      topupPopupStore.closeTopupPopup()
      amount.value = 0
      
    }
  } catch (error) {
    notificationStore.addNotification({
      type: 'error',
      message: t('topup.failed'),
      duration: 5000
    })
  }
}

onMounted(() => {
  loadContractInfo()
})
</script>

<template>
  <AppPopup
    :isOpen="topupPopupStore.isTopupShow"
    @close="topupPopupStore.closeTopupPopup"
  >
    <template #title>
      <TitlePanelColor variant="green" class="title header">{{ $t('topup.title') }}</TitlePanelColor>
    </template>

    <div class="popup-content">
      <AppInput 
        class="input" 
        :type="'number'" 
        :label="$t('topup.amountLabel')" 
        :placeholder="$t('topup.amountPlaceholder')" 
        v-model="amount"
      />
      
      <div v-if="isLoadingContractInfo" class="contract-info loading">
        <div class="loading-text">{{ $t('topup.loadingContractInfo') }}</div>
      </div>
      
      <div v-else-if="contractInfo" class="contract-info">
        <div class="info-item">
          <span>{{ $t('topup.minAmount') }}:</span>
          <span :title="contractInfo.minDepositAmount + ' PHRS'">{{ formatNumber(contractInfo.minDepositAmount) }} PHRS</span>
        </div>
        <div class="info-item">
          <span>{{ $t('topup.maxAmount') }}:</span>
          <span :title="contractInfo.maxDepositAmount + ' PHRS'">{{ formatNumber(contractInfo.maxDepositAmount) }} PHRS</span>
        </div>
        <div class="info-item">
          <span>{{ $t('topup.walletBalance') }}:</span>
          <span :title="contractInfo.walletBalance + ' PHRS'">{{ formatNumber(contractInfo.walletBalance) }} PHRS</span>
        </div>
        <div v-if="contractInfo.isPaused" class="warning">
          {{ $t('topup.contractPaused') }}
        </div>
      </div>
      
      <div v-else class="contract-info error">
        <div class="error-text">{{ $t('topup.failedToLoadContractInfo') }}</div>
      </div>
      
      <div class="info-text">
        {{ $t('topup.infoText') }}
      </div>
      
      <ColorButton @click="handleTopup" :disabled="!canTopup">
        {{ isProcessing ? $t('topup.processing') : $t('topup.topupButton') }}
      </ColorButton>
      
      <div v-if="error" class="error-message">
        {{ error }}
      </div>
    </div>
  </AppPopup>
</template>

<style scoped>
.popup-content {
  padding: calc(var(--base-unit) * 0);
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 20);
}

.info-text {
  color: #6f7b92;
  margin-top: calc(var(--base-unit) * 2);
  text-align: center;
}

.contract-info {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.02) 0%, rgba(0, 0, 0, 0.05) 100%);
  border-radius: calc(var(--base-unit) * 12);
  padding: calc(var(--base-unit) * 16);
  margin: calc(var(--base-unit) * 10) 0;
  border: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.contract-info:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: calc(var(--base-unit) * 6);
  font-size: calc(var(--base-unit) * 13);
  padding: calc(var(--base-unit) * 4) 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.info-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.info-item:hover {
  background: rgba(0, 0, 0, 0.02);
  border-radius: calc(var(--base-unit) * 4);
  padding: calc(var(--base-unit) * 4);
  margin: calc(var(--base-unit) * 2) calc(var(--base-unit) * -4);
}

.warning {
  color: #ff6b6b;
  text-align: center;
  font-weight: 600;
  margin-top: calc(var(--base-unit) * 12);
  padding: calc(var(--base-unit) * 8);
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.05) 0%, rgba(255, 107, 107, 0.1) 100%);
  border-radius: calc(var(--base-unit) * 8);
  border: 1px solid rgba(255, 107, 107, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: calc(var(--base-unit) * 4);
  font-size: calc(var(--base-unit) * 13);
}

.warning::before {
  content: '⚠️';
  font-size: calc(var(--base-unit) * 16);
}

.error-message {
  color: #ff6b6b;
  text-align: center;
  margin-top: calc(var(--base-unit) * 12);
  font-size: calc(var(--base-unit) * 13);
  padding: calc(var(--base-unit) * 8);
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.05) 0%, rgba(255, 107, 107, 0.1) 100%);
  border-radius: calc(var(--base-unit) * 8);
  border: 1px solid rgba(255, 107, 107, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: calc(var(--base-unit) * 4);
  font-weight: 600;
}

.error-message::before {
  content: '❌';
  font-size: calc(var(--base-unit) * 16);
}

.contract-info.loading {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.02) 0%, rgba(0, 0, 0, 0.05) 100%);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.contract-info.error {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.05) 0%, rgba(255, 107, 107, 0.1) 100%);
  border: 1px solid rgba(255, 107, 107, 0.2);
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.1);
}

.loading-text {
  text-align: center;
  color: #6f7b92;
  font-style: italic;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: calc(var(--base-unit) * 4);
}

.loading-text::before {
  content: '';
  width: calc(var(--base-unit) * 8);
  height: calc(var(--base-unit) * 8);
  border: 2px solid #6f7b92;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-text {
  text-align: center;
  color: #ff6b6b;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: calc(var(--base-unit) * 4);
  font-size: calc(var(--base-unit) * 13);
}


.address {
  font-family: monospace;
  font-size: calc(var(--base-unit) * 10);
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .contract-info {
    padding: calc(var(--base-unit) * 12);
    margin: calc(var(--base-unit) * 8) 0;
  }
  
  .info-item {
    font-size: calc(var(--base-unit) * 12);
    margin-bottom: calc(var(--base-unit) * 4);
  }
  
  .loading-text,
  .error-text {
    font-size: calc(var(--base-unit) * 12);
  }
  
  .warning,
  .error-message {
    font-size: calc(var(--base-unit) * 12);
    padding: calc(var(--base-unit) * 6);
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .contract-info {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .info-item {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .info-item:hover {
    background: rgba(255, 255, 255, 0.05);
  }
}
</style> 