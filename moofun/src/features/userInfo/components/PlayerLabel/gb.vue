<script setup>
import { onMounted } from 'vue'
import { useUserInfo } from '@/features/userInfo'

import { usePlayerPopupStore } from '../../stores/playerPopupStore'
import audioService from '@/lib/audioService'

const playerPopupStore = usePlayerPopupStore()
const { userInfo, fetchUserInfo } = useUserInfo()

const OpenPopup = () => {
  audioService.play('button1'); // Play sound effect
  playerPopupStore.openPlayerPopup()
}

onMounted(() => {
  fetchUserInfo()
})
</script>

<template>
  <div class="nickname-container" @click="OpenPopup">
    <div class="icon-box">
      <img :src="userInfo?.photoUrl || '/icon/profile-icon2.png'" alt="Profile" class="profile-icon" />
    </div>
    <div class="name-banner">
      {{ userInfo?.username ?? '' }}
    </div>
  </div>
</template>

<style scoped>
.nickname-container {
  display: flex;
  align-items: center;
  gap: calc(var(--base-unit) * 10);
  padding: calc(var(--base-unit) * 4) calc(var(--base-unit) * 14);
  background-color: rgba(255, 255, 255, 1);
  border: calc(var(--base-unit) * 1) solid rgba(10, 13, 18, 0.18);
  /* background-image: linear-gradient(to bottom, rgba(255, 253, 255, 1), rgba(211, 221, 253, 1)); */
  border-radius: calc(var(--base-unit) * 8);
  /* border-radius: calc(var(--base-unit) * 8) calc(var(--base-unit) * 8) calc(var(--base-unit) * 16) calc(var(--base-unit) * 8); */
  width: calc(var(--base-unit) * 119);
  height: calc(var(--base-unit) * 40);
  box-sizing: border-box;
}

.nickname-container:active {
  transform: scale(0.98);
}

.icon-box {
  width: calc(var(--base-unit) * 24);
  height: calc(var(--base-unit) * 24);
  border-radius: 50%;
  background-color: rgba(245, 245, 245, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  /* border: calc(var(--base-unit) * 1) solid rgba(200, 200, 200, 1); */
}

.profile-icon {
  width: calc(var(--base-unit) * 18);
  height: calc(var(--base-unit) * 18);
  object-fit: contain;
}

.name-banner {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: calc(var(--base-unit) * 14);
  color: rgba(65, 70, 81, 1);
  font-weight: 600;
  -webkit-text-stroke: 0;
  text-shadow: none;
  min-width: 0;
}
</style>
