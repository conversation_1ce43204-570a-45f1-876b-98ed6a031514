<script setup>
import { AppInput, TitlePanelColor, AppPopup, ColorButton } from '@/components/common'
import { usePlayerPopupStore } from '../stores/playerPopupStore'
import { useUserInfo } from '@/features/userInfo'
import { useNotificationStore } from '@/features/notification'


import { updateUsername } from '../api'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'


const { t } = useI18n()
const { userInfo, fetchUserInfo } = useUserInfo()
const playerPopupStore = usePlayerPopupStore()
const notificationStore = useNotificationStore()

const name = ref(userInfo?.username || '')
const isLoading = ref(false)

const validateUsername = (username) => {
  if (!username || username.trim().length === 0) {
    return t('errors.usernameRequired')
  }
  
  if (username.length < 3 || username.length > 30) {
    return t('errors.usernameLength')
  }
  
  return null
}

const onConfirm = async () => {
  const error = validateUsername(name.value)
  if (error) {
    notificationStore.addNotification({
      type: 'error',
      message: error,
      duration: 3000
    })
    return
  }
  
  if (name.value === userInfo.value?.username) {
    notificationStore.addNotification({
      type: 'error',
      message: t('errors.usernameNotChanged'),
      duration: 3000
    })
    return
  }
  
  isLoading.value = true
  
  try {
    await updateUsername(name.value)
    await fetchUserInfo()
    
    notificationStore.addNotification({
      type: 'success',
      message: t('common.usernameUpdated'),
      duration: 3000
    })
    
    playerPopupStore.closePlayerPopup()
  } catch (error) {

    let errorMessage = t('errors.usernameUpdateFailed')
    
    if (error.response?.status === 400) {
      errorMessage = t('errors.usernameAlreadyExists')
    } else if (error.response?.status === 401) {
      errorMessage = t('errors.unauthorized')
    }
    
    notificationStore.addNotification({
      type: 'error',
      message: errorMessage,
      duration: 3000
    })
  } finally {
    isLoading.value = false
  }
}


</script>

<template>
  <AppPopup :isOpen="playerPopupStore.showPlayerPopup" @close="playerPopupStore.closePlayerPopup">
    <template #title>
      <TitlePanelColor variant="green">{{ $t('common.editUsername') }}</TitlePanelColor>
    </template>
    <div class="popup-content">
      <AppInput 
        class="input" 
        :placeholder="userInfo?.username || $t('common.enterName')" 
        v-model="name"
        :disabled="isLoading"
      />
      <ColorButton 
        class="button" 
        @click="onConfirm"
        :disabled="isLoading"
      >
        {{ isLoading ? $t('common.processing') : $t('common.confirm') }}
      </ColorButton>
    </div>
  </AppPopup>
</template>

<style scoped>
.popup-content {
    padding: calc(var(--base-unit) * 0);
    display: flex;
    flex-direction: column;
    gap: calc(var(--base-unit) * 20);
}

.input {
  text-align: center;
}

.button {
  width: 100%;
}
</style>
