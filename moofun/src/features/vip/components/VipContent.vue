<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import VipPopup from './VipPopup.vue'
import VipLabel from './VipLabel.vue'
import { useVipPopupStore } from '../stores/vipPopupStore'

const { t } = useI18n()
const vipStore = useVipPopupStore()

const processing = ref(false)

const handleStartTrial = async () => {
  if (processing.value) return
  
  processing.value = true
  try {
    // TODO: 调用VIP订阅API
    console.log('Starting VIP trial...')

    await new Promise(resolve => setTimeout(resolve, 1000))
    
    vipStore.closeVipPopup()
  } catch (error) {
    console.error('Failed to start VIP trial:', error)
  } finally {
    processing.value = false
  }
}

const openSubscriptionTerms = () => {
  // TODO: 打开订阅条款
  console.log('Opening subscription terms...')
}

const openPrivacyPolicy = () => {
  // TODO: 打开隐私政策
  console.log('Opening privacy policy...')
}
</script>

<template>
  <VipPopup :isOpen="vipStore.showVipPopup" @close="vipStore.closeVipPopup">
    <div class="vip-popup-container">
        
      <VipLabel class="vip-title" :text="$t('common.bullFarmVip')">
      </VipLabel>

        <img src="/iap/vip.png" alt="vip-bg" class="vip-bg">

      <div class="vip-benefits">
        <div class="benefit-item">
          <span class="benefit-text">{{ $t('common.removeAllAds') }}</span>
        </div>
        <div class="benefit-item">
          <span class="benefit-text">{{ $t('common.x2RevenueMultiplier') }}</span>
        </div>
        <div class="benefit-item">
          <span class="benefit-text">{{ $t('common.x3AllBonuses') }}</span>
        </div>
        <div class="benefit-item">
          <span class="benefit-text">{{ $t('common.exclusiveDiamondPrestige') }}</span>
        </div>
      </div>

      <button 
        :class="['start-button', { 'processing': processing }]"
        @click="handleStartTrial"
        :disabled="processing"
      >
        {{ processing ? $t('common.loading') : $t('common.start') }}
      </button>

      <div class="subscription-info">
        <div class="trial-info" v-html="$t('common.trialInfo')">
        </div>
        
        <div class="benefit-text" v-html="$t('common.subscriptionInfo')">
        </div>

        <div class="links">
          <button class="link-button" @click="openSubscriptionTerms">
            {{ $t('common.subscriptionTerms') }}
          </button>
          <span class="separator">|</span>
          <button class="link-button" @click="openPrivacyPolicy">
            {{ $t('common.privacyPolicy') }}
          </button>
        </div>
      </div>
    </div>
  </VipPopup>
</template>

<style scoped>
.vip-popup-container {
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 6);
  max-width: calc(var(--base-unit) * 280);
  width: 100%;
  margin-bottom: calc(var(--base-unit) * -16);
  padding: calc(var(--base-unit) * 2);
  box-sizing: border-box;
  transform: scale(0.9);
}

.vip-title {
  text-align: center;
  margin-bottom: calc(var(--base-unit) * 2);
  font-size: calc(var(--base-unit) * 16);
}

.vip-bg {
  width: 100%;
  height: auto;
  border-radius: calc(var(--base-unit) * 10);
  margin-bottom: calc(var(--base-unit) * 4);
}

.vip-scene {
  background: linear-gradient(to bottom, #87CEEB 0%, #87CEEB 70%, #90EE90 70%, #8B7355 100%);
  border-radius: calc(var(--base-unit) * 10);
  border: calc(var(--base-unit) * 3) solid #8B4513;
  height: calc(var(--base-unit) * 140);
  position: relative;
  overflow: hidden;
  margin-bottom: calc(var(--base-unit) * 4);
}

.scene-background {
  position: relative;
  width: 100%;
  height: 100%;
}

.clouds {
  position: absolute;
  top: calc(var(--base-unit) * 20);
  width: 100%;
}

.cloud {
  position: absolute;
  background: white;
  border-radius: calc(var(--base-unit) * 20);
  opacity: 0.9;
}

.cloud::before,
.cloud::after {
  content: '';
  position: absolute;
  background: white;
  border-radius: 50%;
}

.cloud-1 {
  width: calc(var(--base-unit) * 40);
  height: calc(var(--base-unit) * 15);
  left: calc(var(--base-unit) * 20);
  top: calc(var(--base-unit) * 10);
}

.cloud-1::before {
  width: calc(var(--base-unit) * 20);
  height: calc(var(--base-unit) * 20);
  top: calc(var(--base-unit) * -10);
  left: calc(var(--base-unit) * 10);
}

.cloud-1::after {
  width: calc(var(--base-unit) * 15);
  height: calc(var(--base-unit) * 15);
  top: calc(var(--base-unit) * -8);
  right: calc(var(--base-unit) * 8);
}

.cloud-2 {
  width: calc(var(--base-unit) * 35);
  height: calc(var(--base-unit) * 12);
  right: calc(var(--base-unit) * 30);
  top: calc(var(--base-unit) * 5);
}

.cloud-2::before {
  width: calc(var(--base-unit) * 18);
  height: calc(var(--base-unit) * 18);
  top: calc(var(--base-unit) * -9);
  left: calc(var(--base-unit) * 8);
}

.cloud-3 {
  width: calc(var(--base-unit) * 25);
  height: calc(var(--base-unit) * 10);
  left: 50%;
  transform: translateX(-50%);
  top: calc(var(--base-unit) * 15);
}

.cloud-3::before {
  width: calc(var(--base-unit) * 12);
  height: calc(var(--base-unit) * 12);
  top: calc(var(--base-unit) * -6);
  left: calc(var(--base-unit) * 6);
}

.scene-content {
  position: absolute;
  bottom: calc(var(--base-unit) * 40);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: calc(var(--base-unit) * 20);
}

.arrow-up {
  width: 0;
  height: 0;
  border-left: calc(var(--base-unit) * 25) solid transparent;
  border-right: calc(var(--base-unit) * 25) solid transparent;
  border-bottom: calc(var(--base-unit) * 40) solid #FFD700;
  border-radius: calc(var(--base-unit) * 5);
  position: relative;
}

.arrow-up::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-left: calc(var(--base-unit) * 20) solid transparent;
  border-right: calc(var(--base-unit) * 20) solid transparent;
  border-bottom: calc(var(--base-unit) * 32) solid #FFA500;
  left: calc(var(--base-unit) * -20);
  top: calc(var(--base-unit) * 8);
}

.train-container {
  position: relative;
}

.train {
  width: calc(var(--base-unit) * 80);
  height: calc(var(--base-unit) * 50);
  background: linear-gradient(45deg, #8B4B9B 0%, #4B0B8B 100%);
  border-radius: calc(var(--base-unit) * 8);
  border: calc(var(--base-unit) * 3) solid #2D1B4D;
  position: relative;
}

.train::before {
  content: '';
  position: absolute;
  width: calc(var(--base-unit) * 15);
  height: calc(var(--base-unit) * 15);
  background: #00CED1;
  border: calc(var(--base-unit) * 2) solid #008B8B;
  border-radius: calc(var(--base-unit) * 3);
  left: calc(var(--base-unit) * 10);
  top: calc(var(--base-unit) * 8);
}

.train::after {
  content: '';
  position: absolute;
  width: calc(var(--base-unit) * 12);
  height: calc(var(--base-unit) * 12);
  background: #FF6347;
  border: calc(var(--base-unit) * 2) solid #CD5C5C;
  border-radius: calc(var(--base-unit) * 2);
  right: calc(var(--base-unit) * 10);
  top: calc(var(--base-unit) * 10);
}

.ground {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: calc(var(--base-unit) * 40);
  background: linear-gradient(to bottom, #90EE90 0%, #8B7355 50%);
}

.fence {
  position: absolute;
  bottom: calc(var(--base-unit) * 35);
  left: calc(var(--base-unit) * 20);
  right: calc(var(--base-unit) * 20);
  height: calc(var(--base-unit) * 12);
  background: repeating-linear-gradient(
    90deg,
    #8B4513 0px,
    #8B4513 calc(var(--base-unit) * 4),
    transparent calc(var(--base-unit) * 4),
    transparent calc(var(--base-unit) * 8)
  );
  border-top: calc(var(--base-unit) * 2) solid #654321;
  border-bottom: calc(var(--base-unit) * 2) solid #654321;
}

.vip-benefits {
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 3);
  padding: calc(var(--base-unit) * 2) calc(var(--base-unit) * 12);
  margin-bottom: calc(var(--base-unit) * 4);
}

.benefit-item {
  display: flex;
  align-items: center;
}

.benefit-text {
  font-family: system-ui, -apple-system, sans-serif !important;
  font-weight: 300 !important;
  font-size: calc(var(--base-unit) * 14);
  color: #FCF1CB;
  line-height: 1.3;
  text-align: center;
}

.start-button {
    color: #FFFFFF;
    text-shadow: 2px 1px #3B3B3B;
    background: #FDC844;
    border-radius: calc(var(--base-unit) * 10);
    padding: calc(var(--base-unit) * 12) calc(var(--base-unit) * 16);
    border: calc(var(--base-unit) * 3) solid #FF8A28;
    box-shadow: 0 0 0 calc(var(--base-unit) * 3) #252635;
    display: flex;
    align-items: center;
    gap: calc(var(--base-unit) * 8);
    cursor: pointer;
    transition: transform 0.2s ease;
    justify-content: center;
    align-items: center;
}

.start-button:hover:not(:disabled) {
  transform: scale(1.02);
}

.start-button:active:not(:disabled) {
  transform: scale(0.98);
}

.start-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.start-button.processing {
  background: linear-gradient(to bottom, #D3D3D3, #A9A9A9);
}

.subscription-info {
  background: #B56A28;
  border-radius: calc(var(--base-unit) * 10);
  border: calc(var(--base-unit) * 3) solid #8B4513;
  padding: calc(var(--base-unit) * 8);
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 4);
}

.trial-info {
  border-radius: calc(var(--base-unit) * 8);
  padding: calc(var(--base-unit) * 8);
  text-align: center;
  font-size: calc(var(--base-unit) * 14);
  font-weight: bold;
  color: #FCF1CB;
  line-height: 1.3;
}

.cancel-info {
  font-size: calc(var(--base-unit) * 14);
  color: #FCF1CB;
  line-height: 1.3;
  text-align: left;
}

.links {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: calc(var(--base-unit) * 8);
  margin-top: calc(var(--base-unit) * 4);
  font-size: calc(var(--base-unit) * 14);
}

.link-button {
  background: none;
  border: none;
  color: #FCF1CB;
  font-size: calc(var(--base-unit) * 12);
  text-decoration: underline;
  cursor: pointer;
  padding: 0;
}

.link-button:hover {
  color: #FFFFFF;
}

.separator {
  color: #FCF1CB;
  font-size: calc(var(--base-unit) * 12);
}

.vip-popup-container {
    max-width: 95vw;
    gap: calc(var(--base-unit) * 20);
    padding: calc(var(--base-unit) * 4);
}

:deep(.info-panel) {
  padding: calc(var(--base-unit) * 0) !important;
}

:deep(.popup-content) {
  padding: 0 !important;
}

:deep(.popup-overlay) {
  pointer-events: auto;
}

:deep(.popup-container) {
  max-width: calc(var(--base-unit) * 800) !important;
}

@media (max-width: 414px) {
  :deep(.popup-container) {
    transform: scale(0.8) !important;
  }
}
</style> 