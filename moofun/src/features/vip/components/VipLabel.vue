<template>
    <div class="banner-wrapper">
      <div class="banner-container">
        <div class="banner-content">
          {{ text }}
        </div>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    name: 'VipLabel',
    props: {
      text: {
        type: String,
        required: true,
        default: 'Bull Farm VIP'
      }
    }
  }
  </script>
  
<style scoped>
.banner-wrapper {
  position: relative;
  height: calc(var(--base-unit) * 46);
  margin-top: calc(var(--base-unit) * -24);
  margin-bottom: calc(var(--base-unit) * 0);
}

.banner-container {
  height: 100%;
  z-index: 2;
  border-radius:
  calc(var(--base-unit) * 16)
  calc(var(--base-unit) * 16)
  0
  0;
  background-color: #8FCD2D;
  border: 3px solid #FDD99B;
}

.banner-content {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 110%;
  color: rgba(255, 255, 255, 1);
  font-size: calc(var(--base-unit) * 20);
  text-shadow: none;
  -webkit-text-stroke: 3px #251B12;
}
  </style>
  