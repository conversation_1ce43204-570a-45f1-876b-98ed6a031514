import { createI18n } from 'vue-i18n';

// Function to get system language and map to supported locales
function getSystemLanguage() {
  const systemLang = navigator.language || navigator.userLanguage || 'en';
  const langCode = systemLang.toLowerCase().split('-')[0]; // Get primary language code
  
  // Map system language to supported locales
  const supportedLocales = ['en', 'ja', 'zh'];
  const languageMap = {
    'ja': 'ja',     // Japanese
    'zh': 'zh',     // Chinese (Simplified)
    'zh-cn': 'zh',  // Chinese (China)
    'zh-tw': 'zh',  // Chinese (Taiwan)
    'zh-hk': 'zh',  // Chinese (Hong Kong)
    'en': 'en',     // English
    'en-us': 'en',  // English (US)
    'en-gb': 'en',  // English (UK)
  };
  
  return languageMap[langCode] || languageMap[systemLang] || 'en';
}

// Get saved language or fall back to system language
export const savedLanguage = localStorage.getItem('app_language') || getSystemLanguage();

const i18n = createI18n({
  legacy: false,
  locale: savedLanguage,
  fallbackLocale: 'en',
  messages: {}, // start empty
  globalInjection: true,
});

// Load locale messages for a given locale and theme ("gb" | "mf").
// Falls back to the currently active theme if the caller does not provide one –
// this keeps existing call-sites working (they only pass the locale).
export async function loadLocaleMessages(locale, theme) {
  try {
    // Determine theme if not supplied so existing calls remain backward-compatible.
    if (!theme) {
      // Lazy import to avoid a direct circular dependency at module initialisation time.
      const { useTheme } = await import('@/themes/useTheme');
      const { themeName } = useTheme();
      theme = themeName.value;
    }

    // Prevent double fetches if the locale is already loaded.
    if (!i18n.global.availableLocales.includes(locale)) {
      const response = await fetch(`/locales/${theme}/${locale}.json`);
      const messages = await response.json();
      i18n.global.setLocaleMessage(locale, messages);
    }

    // Set active locale.
    i18n.global.locale.value = locale;
    localStorage.setItem('app_language', locale);
  } catch (error) {
    console.error(`Failed to load locale ${locale} for theme ${theme}:`, error);
  }
}

export default i18n;
