import { createApp } from 'vue';
import App from './App.vue';
import { createPinia } from 'pinia';
import i18n, { loadLocaleMessages } from '@/lib/i18n'
import router from './router';
import liff from "@line/liff";
import { useGameplayStateManager } from '@/features/gameplay/stores/gameplayStateManager';
import { useThemeAssets } from '@/themes';
import { useTheme } from '@/themes/useTheme';
import { applyTheme } from '@/themes/applyTheme';
import { createAppKit } from '@reown/appkit/vue'
// import { mainnet } from '@reown/appkit/networks'
import { defaultNetwork } from './config/networks'
import { Ethers5Adapter } from "@reown/appkit-adapter-ethers5";
import { useNetworkSwitchHandler } from './features/auth/composables/useNetworkSwitchHandler';
import { applySafariCompatibilityFixes, setupSafariErrorHandling, isSafari } from './utils/safariCompatibility';

// Safari兼容性：延迟主题应用和应用初始化
function initializeApp() {
    // 检测Safari浏览器并应用兼容性修复
    if (isSafari()) {
        console.log('Safari browser detected, applying compatibility measures');
        setupSafariErrorHandling();
        applySafariCompatibilityFixes();
    }

    try {
        // 加载主题资源
        const themeAssets = useThemeAssets();
        applyTheme(themeAssets.value);
        console.log('Theme applied successfully');
    } catch (error) {
        console.error('Failed to apply theme:', error);
    }
}

// Safari兼容性：使用DOMContentLoaded确保DOM准备就绪
document.addEventListener('DOMContentLoaded', initializeApp);

window.addEventListener('load', async function () {
    try {
        // Resolve current theme and load corresponding locale messages first
        const { themeName } = useTheme();
        await loadLocaleMessages(i18n.global.locale.value, themeName.value);

        if(themeName.value === 'gb') {
            await createAppKit({
            adapters: [new Ethers5Adapter()],
            networks: [defaultNetwork],
            projectId: import.meta.env.VITE_APP_KIT_PROJECT_ID,
            metadata: {
              name: 'GooseBox',
              description: 'GooseBox Game',
              url: window.location.origin,
              icons: ['https://avatars.githubusercontent.com/u/37784886'],
            },
          })
        }

        // Now that the translations are loaded, mount the Vue app
        console.log('Creating Vue app...');
        const app = createApp(App);

        // Safari兼容性：添加全局错误处理
        app.config.errorHandler = (err, _instance, info) => {
            console.error('Vue error:', err, info);
            // 在Safari中显示更友好的错误信息
            if (isSafari()) {
                console.error('Safari compatibility issue detected:', err.message);
                // 尝试重新初始化主题
                setTimeout(() => {
                    try {
                        initializeApp();
                    } catch (retryError) {
                        console.error('Failed to reinitialize app:', retryError);
                    }
                }, 1000);
            }
        };

        app.use(i18n).use(createPinia()).use(router);

        // Safari兼容性：确保DOM元素存在
        const appElement = document.getElementById('app');
        if (!appElement) {
            throw new Error('App mount point not found');
        }

        app.mount('#app');
        console.log('Vue app mounted successfully');


        // Now Pinia is active, it's safe to use stores
        const gameplayStateManager = useGameplayStateManager();
        gameplayStateManager.enterGameplay();

        // Initialize network switch handler
        const networkSwitchHandler = useNetworkSwitchHandler();
        networkSwitchHandler.handleNetworkEvents();

        // Ensure final sync on app exit
        window.addEventListener('beforeunload', async (event) => {
            // Attempt to sync latest values before app closes
            await gameplayStateManager.exitGameplay();
        });

        liff.init({
            liffId: import.meta.env.VITE_LINE_LIFF_ID,
        });

    } catch (error) {
        console.error("Error initializing application:", error);
    }
});
