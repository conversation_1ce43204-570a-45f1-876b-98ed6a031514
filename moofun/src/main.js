import { createApp } from 'vue';
import App from './App.vue';
import { createPinia } from 'pinia';
import i18n, { loadLocaleMessages } from '@/lib/i18n'
import router from './router';
import liff from "@line/liff";
import { useGameplayStateManager } from '@/features/gameplay/stores/gameplayStateManager';
import { useThemeAssets } from '@/themes';
import { useTheme } from '@/themes/useTheme';
import { applyTheme } from '@/themes/applyTheme';
import { createAppKit } from '@reown/appkit/vue'
// import { mainnet } from '@reown/appkit/networks'
import { defaultNetwork } from './config/networks'
import { Ethers5Adapter } from "@reown/appkit-adapter-ethers5";
import { useNetworkSwitchHandler } from './features/auth/composables/useNetworkSwitchHandler';

// 全局错误处理
window.addEventListener('error', (event) => {
    console.error('Global Error:', event.error);
    console.error('Message:', event.message);
    console.error('Filename:', event.filename);
    console.error('Line:', event.lineno);
    console.error('Column:', event.colno);
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled Promise Rejection:', event.reason);
    console.error('Promise:', event.promise);
});

// 确保CSS变量正确设置
function ensureCSSVariables() {
    const root = document.documentElement;
    const currentWidth = window.innerWidth;

    // 检查CSS变量是否正确计算
    const baseUnit = getComputedStyle(root).getPropertyValue('--base-unit').trim();

    if (!baseUnit || baseUnit === '' || baseUnit === '0px') {
        console.warn('CSS variables not properly calculated, setting fallback values');

        // 手动设置CSS变量
        if (currentWidth > 600) {
            root.style.setProperty('--app-width', '375px');
            root.style.setProperty('--base-unit', '1px');
        } else {
            root.style.setProperty('--app-width', '100vw');
            root.style.setProperty('--base-unit', (currentWidth / 375) + 'px');
        }
        root.style.setProperty('--app-height', '100vh');
        root.style.setProperty('--vh', '1vh');

        console.log('CSS variables set manually:', {
            width: currentWidth,
            appWidth: root.style.getPropertyValue('--app-width'),
            baseUnit: root.style.getPropertyValue('--base-unit')
        });
    }
}

// 在DOM准备就绪时确保CSS变量正确
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        console.log('DOMContentLoaded event triggered');
        ensureCSSVariables();
    });
} else {
    console.log('DOM already ready, ensuring CSS variables');
    ensureCSSVariables();
}

// 添加额外的DOM检查
function verifyDOMState() {
    const appElement = document.getElementById('app');
    const bodyElement = document.body;
    const headElement = document.head;

    console.log('DOM State Check:', {
        appElement: !!appElement,
        bodyElement: !!bodyElement,
        headElement: !!headElement,
        readyState: document.readyState,
        location: window.location.href
    });

    if (!appElement) {
        console.error('CRITICAL: #app element missing from DOM');
        console.log('Body innerHTML:', document.body.innerHTML);
    }

    return !!appElement;
}

// 在多个时机检查DOM状态
document.addEventListener('DOMContentLoaded', verifyDOMState);
if (document.readyState !== 'loading') {
    verifyDOMState();
}

window.addEventListener('load', async function () {
    console.log('Window load event triggered');

    try {
        // 检查DOM元素是否存在
        const appElement = document.getElementById('app');
        if (!appElement) {
            console.error('CRITICAL: #app element not found in DOM');
            return;
        }
        console.log('✓ #app element found');

        // Resolve current theme and load corresponding locale messages first
        console.log('Loading theme and locale messages...');
        const { themeName } = useTheme();
        console.log('Current theme:', themeName.value);

        await loadLocaleMessages(i18n.global.locale.value, themeName.value);
        console.log('✓ Locale messages loaded');

        if(themeName.value === 'gb') {
            console.log('Creating AppKit for gb theme...');
            await createAppKit({
            adapters: [new Ethers5Adapter()],
            networks: [defaultNetwork],
            projectId: import.meta.env.VITE_APP_KIT_PROJECT_ID,
            metadata: {
              name: 'GooseBox',
              description: 'GooseBox Game',
              url: window.location.origin,
              icons: ['https://avatars.githubusercontent.com/u/37784886'],
            },
          })
          console.log('✓ AppKit created');
        }

        // Now that the translations are loaded, mount the Vue app
        console.log('Creating Vue app...');
        const app = createApp(App);

        // 添加全局错误处理
        app.config.errorHandler = (err, instance, info) => {
            console.error('Vue Error:', err);
            console.error('Component:', instance);
            console.error('Info:', info);
        };

        console.log('Adding plugins...');
        app.use(i18n).use(createPinia()).use(router);

        console.log('Mounting Vue app to #app...');
        app.mount('#app');
        console.log('✓ Vue app mounted successfully');


        // Now Pinia is active, it's safe to use stores
        const gameplayStateManager = useGameplayStateManager();
        gameplayStateManager.enterGameplay();

        // Initialize network switch handler
        const networkSwitchHandler = useNetworkSwitchHandler();
        networkSwitchHandler.handleNetworkEvents();

        // Ensure final sync on app exit
        window.addEventListener('beforeunload', async (event) => {
            // Attempt to sync latest values before app closes
            await gameplayStateManager.exitGameplay();
        });

        liff.init({
            liffId: import.meta.env.VITE_LINE_LIFF_ID,
        });

    } catch (error) {
        console.error("Error initializing application:", error);
        console.error("Stack trace:", error.stack);

        // 尝试简单的应用挂载作为fallback
        try {
            console.log('Attempting fallback app mounting...');
            const fallbackApp = createApp(App);
            fallbackApp.mount('#app');
            console.log('✓ Fallback app mounted successfully');
        } catch (fallbackError) {
            console.error('Fallback mounting also failed:', fallbackError);

            // 最后的fallback：显示错误信息
            const appElement = document.getElementById('app');
            if (appElement) {
                appElement.innerHTML = `
                    <div style="padding: 20px; color: red; font-family: Arial, sans-serif;">
                        <h2>应用加载失败</h2>
                        <p>请刷新页面重试</p>
                        <p>错误信息: ${error.message}</p>
                        <button onclick="window.location.reload()" style="padding: 10px; margin-top: 10px;">刷新页面</button>
                    </div>
                `;
            }
        }
    }
});

// 添加额外的超时保护
setTimeout(() => {
    const appElement = document.getElementById('app');
    if (appElement && appElement.innerHTML.trim() === '') {
        console.warn('App element is still empty after 10 seconds, attempting emergency mount');
        try {
            const emergencyApp = createApp(App);
            emergencyApp.mount('#app');
            console.log('✓ Emergency app mounted');
        } catch (emergencyError) {
            console.error('Emergency mounting failed:', emergencyError);
            appElement.innerHTML = `
                <div style="padding: 20px; color: orange; font-family: Arial, sans-serif;">
                    <h2>应用启动超时</h2>
                    <p>请刷新页面重试</p>
                    <button onclick="window.location.reload()" style="padding: 10px; margin-top: 10px;">刷新页面</button>
                </div>
            `;
        }
    }
}, 10000);
