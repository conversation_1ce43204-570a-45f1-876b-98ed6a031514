import { createApp } from 'vue';
import App from './App.vue';
import { createPinia } from 'pinia';
import i18n, { loadLocaleMessages } from '@/lib/i18n'
import router from './router';
import liff from "@line/liff";
import { useGameplayStateManager } from '@/features/gameplay/stores/gameplayStateManager';
import { useThemeAssets } from '@/themes';
import { useTheme } from '@/themes/useTheme';
import { applyTheme } from '@/themes/applyTheme';
import { createAppKit } from '@reown/appkit/vue'
// import { mainnet } from '@reown/appkit/networks'
import { defaultNetwork } from './config/networks'
import { Ethers5Adapter } from "@reown/appkit-adapter-ethers5";
import { useNetworkSwitchHandler } from './features/auth/composables/useNetworkSwitchHandler';
import { applySafariCompatibilityFixes, setupSafariErrorHandling, isSafari } from './utils/safariCompatibility';

// Safari兼容性：等待CSS加载完成后再初始化
function waitForCSS() {
    return new Promise((resolve) => {
        const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
        let loadedCount = 0;
        const totalCount = stylesheets.length;

        if (totalCount === 0) {
            resolve();
            return;
        }

        const checkComplete = () => {
            loadedCount++;
            if (loadedCount === totalCount) {
                console.log('All CSS loaded successfully');
                resolve();
            }
        };

        stylesheets.forEach(link => {
            if (link.sheet) {
                checkComplete();
            } else {
                link.onload = checkComplete;
                link.onerror = () => {
                    console.warn('CSS failed to load:', link.href);
                    checkComplete(); // 继续执行，即使CSS加载失败
                };
            }
        });

        // 超时保护：3秒后强制继续
        setTimeout(() => {
            if (loadedCount < totalCount) {
                console.warn('CSS loading timeout, continuing anyway');
                resolve();
            }
        }, 3000);
    });
}

// Safari兼容性：延迟主题应用和应用初始化
async function initializeApp() {
    try {
        // 等待CSS加载完成
        await waitForCSS();

        // 检测Safari浏览器并应用兼容性修复
        if (isSafari()) {
            console.log('Safari browser detected, applying compatibility measures');
            setupSafariErrorHandling();
            applySafariCompatibilityFixes();
        }

        // 加载主题资源
        const themeAssets = useThemeAssets();
        applyTheme(themeAssets.value);
        console.log('Theme applied successfully');

        // 确保CSS变量已经生效
        await new Promise(resolve => setTimeout(resolve, 100));

    } catch (error) {
        console.error('Failed to initialize app:', error);
    }
}

// Safari兼容性：使用DOMContentLoaded确保DOM准备就绪
document.addEventListener('DOMContentLoaded', initializeApp);

window.addEventListener('load', async function () {
    try {
        // Safari兼容性：额外等待确保所有资源都已加载
        if (isSafari()) {
            console.log('Safari detected, waiting for additional resource loading...');
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        // Resolve current theme and load corresponding locale messages first
        const { themeName } = useTheme();
        await loadLocaleMessages(i18n.global.locale.value, themeName.value);

        if(themeName.value === 'gb') {
            await createAppKit({
            adapters: [new Ethers5Adapter()],
            networks: [defaultNetwork],
            projectId: import.meta.env.VITE_APP_KIT_PROJECT_ID,
            metadata: {
              name: 'GooseBox',
              description: 'GooseBox Game',
              url: window.location.origin,
              icons: ['https://avatars.githubusercontent.com/u/37784886'],
            },
          })
        }

        // Now that the translations are loaded, mount the Vue app
        console.log('Creating Vue app...');
        const app = createApp(App);

        // Safari兼容性：添加全局错误处理
        app.config.errorHandler = (err, _instance, info) => {
            console.error('Vue error:', err, info);
            // 在Safari中显示更友好的错误信息
            if (isSafari()) {
                console.error('Safari compatibility issue detected:', err.message);
                // 尝试重新初始化主题
                setTimeout(() => {
                    try {
                        initializeApp();
                    } catch (retryError) {
                        console.error('Failed to reinitialize app:', retryError);
                    }
                }, 1000);
            }
        };

        app.use(i18n).use(createPinia()).use(router);

        // Safari兼容性：验证CSS变量是否正确设置
        const validateCSSVariables = () => {
            const root = document.documentElement;
            const appWidth = getComputedStyle(root).getPropertyValue('--app-width').trim();
            const baseUnit = getComputedStyle(root).getPropertyValue('--base-unit').trim();

            console.log('CSS Variables:', { appWidth, baseUnit });

            if (!appWidth || !baseUnit) {
                console.warn('CSS variables not properly set, applying fallbacks');
                // 应用fallback值
                root.style.setProperty('--app-width', window.innerWidth > 600 ? '375px' : '100vw');
                root.style.setProperty('--base-unit', window.innerWidth > 600 ? '1px' : (window.innerWidth / 375) + 'px');
                root.style.setProperty('--app-height', '100vh');
            }
        };

        validateCSSVariables();

        // Safari兼容性：确保DOM元素存在
        const appElement = document.getElementById('app');
        if (!appElement) {
            throw new Error('App mount point not found');
        }

        app.mount('#app');
        console.log('Vue app mounted successfully');

        // Safari兼容性：挂载后再次验证CSS变量
        setTimeout(validateCSSVariables, 100);


        // Now Pinia is active, it's safe to use stores
        const gameplayStateManager = useGameplayStateManager();
        gameplayStateManager.enterGameplay();

        // Initialize network switch handler
        const networkSwitchHandler = useNetworkSwitchHandler();
        networkSwitchHandler.handleNetworkEvents();

        // Ensure final sync on app exit
        window.addEventListener('beforeunload', async (event) => {
            // Attempt to sync latest values before app closes
            await gameplayStateManager.exitGameplay();
        });

        liff.init({
            liffId: import.meta.env.VITE_LINE_LIFF_ID,
        });

    } catch (error) {
        console.error("Error initializing application:", error);
    }
});
