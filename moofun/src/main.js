import { createApp } from 'vue';
import App from './App.vue';
import { createPinia } from 'pinia';
import i18n, { loadLocaleMessages } from '@/lib/i18n'
import router from './router';
import liff from "@line/liff";
import { useGameplayStateManager } from '@/features/gameplay/stores/gameplayStateManager';
import { useThemeAssets } from '@/themes';
import { useTheme } from '@/themes/useTheme';
import { applyTheme } from '@/themes/applyTheme';
import { createAppKit } from '@reown/appkit/vue'
// import { mainnet } from '@reown/appkit/networks'
import { defaultNetwork } from './config/networks'
import { Ethers5Adapter } from "@reown/appkit-adapter-ethers5";
import { useNetworkSwitchHandler } from './features/auth/composables/useNetworkSwitchHandler';

const themeAssets = useThemeAssets();
applyTheme(themeAssets.value);

window.addEventListener('load', async function () {
    try {
        // Resolve current theme and load corresponding locale messages first
        const { themeName } = useTheme();
        await loadLocaleMessages(i18n.global.locale.value, themeName.value);

        if(themeName.value === 'gb') {
            await createAppKit({
            adapters: [new Ethers5Adapter()],
            networks: [defaultNetwork],
            projectId: import.meta.env.VITE_APP_KIT_PROJECT_ID,
            metadata: {
              name: 'GooseBox',
              description: 'GooseBox Game',
              url: window.location.origin,
              icons: ['https://avatars.githubusercontent.com/u/37784886'],
            },
          })
        }

        // Now that the translations are loaded, mount the Vue app
        const app = createApp(App);
        app.use(i18n).use(createPinia()).use(router);
        app.mount('#app');


        // Now Pinia is active, it's safe to use stores
        const gameplayStateManager = useGameplayStateManager();
        gameplayStateManager.enterGameplay();

        // Initialize network switch handler
        const networkSwitchHandler = useNetworkSwitchHandler();
        networkSwitchHandler.handleNetworkEvents();

        // Ensure final sync on app exit
        window.addEventListener('beforeunload', async (event) => {
            // Attempt to sync latest values before app closes
            await gameplayStateManager.exitGameplay();
        });

        liff.init({
            liffId: import.meta.env.VITE_LINE_LIFF_ID,
        });

    } catch (error) {
        console.error("Error initializing application:", error);
    }
});
