import Phaser from 'phaser';
import { SpinePlugin } from '@esotericsoftware/spine-phaser';

export default class CowScene extends Phaser.Scene {
    constructor() {
        super({ key: 'CowScene', active: false });
    }

    preload() {
        // Load the asset pack
		this.load.pack('game-assets', 'asset-pack.json');
    }

    create() {
        // Add spine character
        this.cow = this.add.spine(200, 500, 'Cow-data', 'Cow-atlas');
        this.cow.animationState.setAnimation(0, "Idle", true);

        // Initial resize
        this.resize();

        // Listen for window resize events
        this.scale.on('resize', this.resize, this);
    }

    resize() {
        const x = this.game.scale.width;
        const y = this.game.scale.height;

        // Handle cow scaling
        const cowX = x / 601;
        const cowY = y / 941;
        let cowScale = cowY * 1 - cowX * 0.4;
        cowScale = Phaser.Math.Clamp(cowScale, 0, cowX * 0.6);

        this.cow.x = x / 2;
        this.cow.y = y - y * 0.09;
        this.cow.setScale(cowScale);
        this.cow.setOrigin(0.5, 0.5);
    }
}