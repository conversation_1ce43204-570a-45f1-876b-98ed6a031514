export function applyTheme(theme) {
  try {
    const root = document.documentElement;
    if (!root) {
      console.error('Document root not available');
      return false;
    }

    // Safari兼容性：检查主题对象是否有效
    if (!theme || typeof theme !== 'object') {
      console.error('Invalid theme object:', theme);
      return false;
    }

    // 主题对象包含的是资源路径，不是CSS变量
    // 这些资源路径会在Vue组件中直接使用，不需要设置为CSS变量
    console.log('Theme assets loaded:', Object.keys(theme).length, 'assets');

    // 只设置真正的CSS变量（以--开头的属性）
    Object.entries(theme).forEach(([key, value]) => {
      if (key.startsWith('--')) {
        try {
          root.style.setProperty(key, value);
        } catch (error) {
          console.error('Failed to set CSS property:', key, error);
        }
      }
    });

    return true;
  } catch (error) {
    console.error('Failed to apply theme:', error);
    return false;
  }
}