export default {

  // Cow movement configuration
  cowMovement: {
    yRange: 0.25,     // Math.random() * 0.25
    yOffset: 0.1,     // + 0.1
    xRange: 0.9,      // Math.random() * 0.9
    xOffset: -0.45,   // + (-0.45)
  },

  home: '/icon/mf/home.png',
  task: '/icon/mf/chest.png',
  inventory: '/icon/mf/inventory.png',
  invite: '/icon/mf/invite.png',
  profile: '/icon/mf/profile.png',
  intro: '/img/mf/intro.png',
  logo: '/img/mf/logo.png',
  menuBackground: '/img/mf/menu-background.svg',
  chest: '/icon/mf/chest.png',
  closeBtn: '/ui/mf/close-btn.png',
  milkProduce: '/assets/deliveryLine/mf/milk-produce.png',
  conveyor: '/assets/deliveryLine/mf/conveyor.png',
  frameEmpty: '/assets/farmPlot/mf/frame-empty.png',
  product: '/assets/deliveryLine/mf/product.png',
  farmPlot1: '/assets/farmPlot/mf/farm-plot-1.png',
  farmPlot2: '/assets/farmPlot/mf/farm-plot-2.png',
  farmPlot3: '/assets/farmPlot/mf/farm-plot-3.png',
  farmPlot4: '/assets/farmPlot/mf/farm-plot-4.png',
  farmSide: '/assets/farmPlot/mf/milk.png',
  gem: '/icon/mf/gem.png',
  diamond: '/icon/mf/diamond.png',

  farmUnlock: '/assets/upgrade/mf/unlock.png',
  productionUpgrade: '/assets/upgrade/mf/production.png',
  productionSpeedUpgrade: '/assets/upgrade/mf/productionSpeed.png',
  cowCountUpgrade: '/assets/upgrade/mf/count.png',
  profitUpgrade: '/assets/upgrade/mf/profit.png',
  deliverySpeedUpgrade: '/assets/upgrade/mf/speed.png',
  capacityUpgrade: '/assets/upgrade/mf/capacity.png',
};