/**
 * Centralized configuration for the application
 * Handles environment-specific URLs and settings
 */

import { getDefaultReferralPlatform } from './platformDetection'

// Get the current environment
const isDevelopment = import.meta.env.DEV
const isProduction = import.meta.env.PROD

// App domain configuration
const getAppDomain = () => {
  if (isDevelopment) {
    return 'http://localhost:3000'
  }
  
  // For production, you can configure this via environment variables
  return import.meta.env.VITE_APP_DOMAIN || ''//'https://moofun.app'
}

// Telegram bot configuration
const getTelegramBotUrl = () => {
  // You can make this configurable via environment variables
  const botUsername = import.meta.env.VITE_TELEGRAM_BOT_USERNAME || ''//'moofun_bot'
  return `https://t.me/${botUsername}`
}

// Line LIFF configuration
const getLineLiffUrl = () => {
  const liffId = import.meta.env.VITE_LINE_LIFF_ID || ''// 'moofun'
  return `https://liff.line.me/${liffId}`
}

// Social media configuration
const getSocialMediaUrls = () => {
  return {
    telegram: import.meta.env.VITE_TELEGRAM_CHANNEL || '',//'https://t.me/MooFun',
    twitter: import.meta.env.VITE_TWITTER_HANDLE || ''//'https://x.com/MooFunGame'
  }
}

/**
 * Generate referral links for different platforms
 * @param {string} code - The referral code
 * @param {string} platform - The platform ('telegram', 'kaia', 'line', 'auto')
 * @returns {string} The generated referral link
 */
export const generateReferralLink = (code, platform = 'auto') => {
  if (!code) return ''
  
  // If platform is 'auto', detect the current platform
  if (platform === 'auto') {
    platform = getDefaultReferralPlatform()
  }
  
  const platformStr = String(platform || 'kaia').toLowerCase()

  switch (platformStr) {
    case 'telegram':
      return `${getTelegramBotUrl()}?startapp=r_${code}`
    case 'kaia':
      return `${getAppDomain()}/?ref=r_${code}`
    case 'line':
      return `${getLineLiffUrl()}?ref=r_${code}`
    default:
      console.warn(`Unknown platform "${platform}", using default link format`)
      return `${getAppDomain()}/?ref=r_${code}`
  }
}

/**
 * Generate boost links for chest sharing
 * @param {string} boostCode - The boost code
 * @param {string} platform - The platform ('telegram', 'kaia', 'line', 'auto')
 * @returns {string} The generated boost link
 */
export const generateBoostLink = (boostCode, platform = 'auto') => {
  if (!boostCode) return ''
  
  // If platform is 'auto', detect the current platform
  if (platform === 'auto') {
    platform = getDefaultReferralPlatform()
  }
  
  const platformStr = String(platform || 'kaia').toLowerCase()

  switch (platformStr) {
    case 'telegram':
      return `${getTelegramBotUrl()}?startapp=b_${boostCode}`
    case 'kaia':
      return `${getAppDomain()}/?ref=b_${boostCode}`
    case 'line':
      return `${getLineLiffUrl()}?ref=b_${boostCode}`
    default:
      console.warn(`Unknown platform "${platform}", using default link format`)
      return `${getAppDomain()}/?ref=b_${boostCode}`
  }
}

/**
 * Get social media URLs
 * @returns {object} Object containing social media URLs
 */
export const getSocialLinks = () => {
  return getSocialMediaUrls()
}

// Utility: devLog only logs in development mode
export function devLog(...args) {
  if (isDevelopment) {
    // eslint-disable-next-line no-console
    console.log(...args);
  }
}

// Export configuration constants
export const config = {
  isDevelopment,
  isProduction,
  appDomain: getAppDomain(),
  telegramBotUrl: getTelegramBotUrl(),
  lineLiffUrl: getLineLiffUrl(),
  socialLinks: getSocialMediaUrls(),
  generateReferralLink,
  generateBoostLink,
  getSocialLinks
}

export default config 