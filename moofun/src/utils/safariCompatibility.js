/**
 * Safari兼容性工具
 * 专门解决Safari浏览器的兼容性问题
 */

/**
 * 检测是否为Safari浏览器
 */
export function isSafari() {
  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
}

/**
 * 获取Safari版本
 */
export function getSafariVersion() {
  if (!isSafari()) return null;
  const match = navigator.userAgent.match(/Version\/(\d+\.\d+)/);
  return match ? match[1] : null;
}

/**
 * 应用Safari兼容性修复
 */
export function applySafariCompatibilityFixes() {
  if (!isSafari()) return;

  console.log('Applying Safari compatibility fixes...');

  // 修复CSS变量支持问题
  const style = document.createElement('style');
  style.textContent = `
    /* Safari兼容性修复 */
    @supports not (--css: variables) {
      :root {
        --app-width: 100vw;
        --app-height: 100vh;
        --base-unit: calc(100vw / 375);
      }
      
      @media (min-width: 600px) {
        :root {
          --app-width: 375px;
          --base-unit: 1px;
        }
      }
    }
    
    /* 确保基本样式在Safari中正常工作 */
    body {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    
    #app {
      -webkit-transform: translateX(-50%);
      transform: translateX(-50%);
    }
    
    /* 修复flexbox在Safari中的问题 */
    .flex-center {
      display: -webkit-flex;
      display: flex;
      -webkit-justify-content: center;
      justify-content: center;
      -webkit-align-items: center;
      align-items: center;
    }
  `;
  
  document.head.appendChild(style);
  console.log('Safari compatibility styles applied');
}

/**
 * 设置Safari错误处理
 */
export function setupSafariErrorHandling() {
  if (!isSafari()) return;

  // 全局错误处理
  window.addEventListener('error', (event) => {
    console.error('Safari Error:', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      userAgent: navigator.userAgent
    });
  });

  // Promise错误处理
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Safari Promise Rejection:', {
      reason: event.reason,
      userAgent: navigator.userAgent
    });
  });

  console.log('Safari error handling setup complete');
}

/**
 * 检查关键资源是否加载
 */
export function checkCriticalResources() {
  return new Promise((resolve) => {
    const results = [];
    
    // 检查CSS是否加载
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
    let loadedCount = 0;
    const totalCount = stylesheets.length;
    
    if (totalCount === 0) {
      resolve(results);
      return;
    }
    
    stylesheets.forEach((link, index) => {
      if (link.sheet) {
        results.push({ type: 'css', url: link.href, status: 'loaded' });
        loadedCount++;
      } else {
        const checkLoad = () => {
          if (link.sheet) {
            results.push({ type: 'css', url: link.href, status: 'loaded' });
          } else {
            results.push({ type: 'css', url: link.href, status: 'failed' });
          }
          loadedCount++;
          
          if (loadedCount === totalCount) {
            resolve(results);
          }
        };
        
        link.onload = checkLoad;
        link.onerror = checkLoad;
        
        // 超时检查
        setTimeout(checkLoad, 3000);
      }
    });
  });
}

export default {
  isSafari,
  getSafariVersion,
  applySafariCompatibilityFixes,
  setupSafariErrorHandling,
  checkCriticalResources
};
