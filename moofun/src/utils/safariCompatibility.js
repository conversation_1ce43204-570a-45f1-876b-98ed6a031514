/**
 * Safari兼容性工具
 * 专门解决Safari浏览器的兼容性问题
 */

/**
 * 检测是否为Safari浏览器
 */
export function isSafari() {
  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
}

/**
 * 获取Safari版本
 */
export function getSafariVersion() {
  if (!isSafari()) return null;
  const match = navigator.userAgent.match(/Version\/(\d+\.\d+)/);
  return match ? match[1] : null;
}

/**
 * 应用Safari兼容性修复
 */
export function applySafariCompatibilityFixes() {
  if (!isSafari()) return;

  console.log('Applying Safari compatibility fixes...');

  // 强制设置CSS变量，确保它们在Safari中正确工作
  const root = document.documentElement;
  const currentWidth = window.innerWidth;

  // 设置基础CSS变量
  root.style.setProperty('--vh', '1vh');
  root.style.setProperty('--app-height', '100vh');

  if (currentWidth > 600) {
    root.style.setProperty('--app-width', '375px');
    root.style.setProperty('--base-unit', '1px');
  } else {
    root.style.setProperty('--app-width', '100vw');
    root.style.setProperty('--base-unit', (currentWidth / 375) + 'px');
  }

  // 修复CSS变量支持问题
  const style = document.createElement('style');
  style.textContent = `
    /* Safari兼容性修复 */
    body {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      /* 确保字体大小有fallback */
      font-size: ${currentWidth > 600 ? '16px' : Math.round(currentWidth / 375 * 16) + 'px'};
    }

    #app {
      -webkit-transform: translateX(-50%);
      transform: translateX(-50%);
      /* 确保宽度有fallback */
      width: ${currentWidth > 600 ? '375px' : '100vw'};
      max-width: ${currentWidth > 600 ? '375px' : 'none'};
    }

    /* 修复flexbox在Safari中的问题 */
    .flex-center {
      display: -webkit-flex;
      display: flex;
      -webkit-justify-content: center;
      justify-content: center;
      -webkit-align-items: center;
      align-items: center;
    }

    /* 确保calc()函数在Safari中正常工作 */
    .main-text {
      -webkit-text-stroke: ${currentWidth > 600 ? '2px' : Math.round(currentWidth / 375 * 2) + 'px'} rgba(59, 59, 59, 1);
      text-shadow: 0 ${currentWidth > 600 ? '1px' : Math.round(currentWidth / 375) + 'px'} ${currentWidth > 600 ? '2px' : Math.round(currentWidth / 375 * 2) + 'px'} rgba(59, 59, 59, 1);
    }
  `;

  document.head.appendChild(style);

  // 监听窗口大小变化，重新计算CSS变量
  window.addEventListener('resize', () => {
    const newWidth = window.innerWidth;
    if (newWidth > 600) {
      root.style.setProperty('--app-width', '375px');
      root.style.setProperty('--base-unit', '1px');
    } else {
      root.style.setProperty('--app-width', '100vw');
      root.style.setProperty('--base-unit', (newWidth / 375) + 'px');
    }
  });

  console.log('Safari compatibility styles applied');
}

/**
 * 设置Safari错误处理
 */
export function setupSafariErrorHandling() {
  if (!isSafari()) return;

  // 全局错误处理
  window.addEventListener('error', (event) => {
    console.error('Safari Error:', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      userAgent: navigator.userAgent
    });
  });

  // Promise错误处理
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Safari Promise Rejection:', {
      reason: event.reason,
      userAgent: navigator.userAgent
    });
  });

  console.log('Safari error handling setup complete');
}

/**
 * 检查关键资源是否加载
 */
export function checkCriticalResources() {
  return new Promise((resolve) => {
    const results = [];
    
    // 检查CSS是否加载
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
    let loadedCount = 0;
    const totalCount = stylesheets.length;
    
    if (totalCount === 0) {
      resolve(results);
      return;
    }
    
    stylesheets.forEach((link, index) => {
      if (link.sheet) {
        results.push({ type: 'css', url: link.href, status: 'loaded' });
        loadedCount++;
      } else {
        const checkLoad = () => {
          if (link.sheet) {
            results.push({ type: 'css', url: link.href, status: 'loaded' });
          } else {
            results.push({ type: 'css', url: link.href, status: 'failed' });
          }
          loadedCount++;
          
          if (loadedCount === totalCount) {
            resolve(results);
          }
        };
        
        link.onload = checkLoad;
        link.onerror = checkLoad;
        
        // 超时检查
        setTimeout(checkLoad, 3000);
      }
    });
  });
}

export default {
  isSafari,
  getSafariVersion,
  applySafariCompatibilityFixes,
  setupSafariErrorHandling,
  checkCriticalResources
};
