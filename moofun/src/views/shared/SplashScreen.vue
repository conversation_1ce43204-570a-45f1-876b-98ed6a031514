<script setup>
import VersionText from '@/components/utility/VersionText.vue';
import { Background } from '@/components/common';

import { onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import audioService from '@/lib/audioService'
import { useThemeAssets } from '@/themes'

const currentTheme = useThemeAssets()
    
const router = useRouter()
const route = useRoute()
let userInteracted = false
let splashTimeout = null

function handleUserInteraction() {
  if (!userInteracted) {
    userInteracted = true
    setTimeout(() => {
      audioService.playBGM()
    }, 500)
  }
}

function proceedToAuth() {
  if (splashTimeout) {
    clearTimeout(splashTimeout)
    splashTimeout = null
  }
  // Preserve query parameters (like referral codes) when navigating to authentication
  router.push({
    path: '/authentication',
    query: route.query
  })
}

onMounted(async () => {
  document.addEventListener('click', handleUserInteraction)
  document.addEventListener('touchstart', handleUserInteraction)

  // Route to main menu after 5s if video hasn't loaded
  splashTimeout = setTimeout(() => {
    proceedToAuth()
  }, 5000)
})
</script>

<template>
  <div class="container">
    <Background class="background-image" :imagePath="currentTheme.intro"/>
    <img class="logo" :src="currentTheme.logo" alt="Logo" />
    <VersionText />
  </div>
</template>

<style scoped>
.container {
  position: relative;
  width: 100%;
  height: var(--app-height);
  overflow: hidden;
  background-color: #2c3757;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  filter: brightness(0.2) opacity(0.8) saturate(0.5);
  z-index: 1;
  object-fit: cover;
}

.logo {
  position: absolute;
  top: calc(var(--base-unit) * 16);
  left: calc(50% - calc(var(--base-unit) * 128));
  width: calc(var(--base-unit) * 256);
  height: calc(var(--base-unit) * 256);
  object-fit: contain;
  z-index: 1;
  animation: pulse 1s ease-in-out infinite alternate;
  filter: brightness(0.2) saturate(0.5);
}
</style>