<script setup>
import { DeliveryLine, Delivery<PERSON>inePanel, DeliveryLineUpgradePopup } from '@/features/gameplay/deliveryLine'
import { BoosterInventoryPopup } from '@/features/gameplay/booster'
import { FarmGroup, FarmPlotUpgradePopup, FarmPlotUnlockPopup } from '@/features/gameplay/farmPlot'
import { Gem<PERSON>ounter, PhrsCounter, DiamondCounter } from '@/features/userInfo'
import { TopupPopup } from '@/features/topup'
import { TaskBar, TaskBarPopup } from '@/features/newTask'
import { useTheme } from '@/themes/useTheme'

const { themeName } = useTheme()

</script>

<template>
  <div id="Home">
    <div v-if="themeName === 'gb'" class="counters-container">
      <DiamondCounter />
      <PhrsCounter />
    </div>
    
    <div v-if="themeName === 'gb'" class="bottom-counter">
      <GemCounter />
    </div>

    <div v-if="themeName === 'mf'" class="counters-container">
      <GemCounter />
      <DiamondCounter />
    </div>
    <TaskBar />
    <DeliveryLinePanel />
    <div class="game-area">
      <DeliveryLine />
      <FarmGroup />
    </div>
  </div>

  <BoosterInventoryPopup />
  <FarmPlotUpgradePopup />
  <FarmPlotUnlockPopup />
  <DeliveryLineUpgradePopup />
  <OfflineRewardPopup />
  <TopupPopup v-if="themeName === 'gb'" />
  <TaskBarPopup />
</template>

<style scoped>
#Home {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.counters-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: calc(var(--base-unit) * 16);
  width: 100%;
  padding: calc(var(--base-unit) * 8) 0;
}

.bottom-counter {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: calc(var(--base-unit) * 8) 0;
}

.game-area {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.delivery-line {
  height: calc(var(--base-unit) * 120);
  flex-shrink: 0;
}

.farm-group {
  flex: 1;
  overflow-y: auto;
}
</style>
