<script setup>
import { TitlePanelColor } from '@/components/common';
import { JackpotPanel, JackpotInfoPopup } from '@/features/jackpot';

const isConnected = true;
</script>


<template>
    <div class="jackpot-chest-container">
        <TitlePanelColor :variant="'green'">
            {{ $t('chest.jackpot') }}
        </TitlePanelColor>

        <div class="chest-wrapper">
            <JackpotPanel :class="{ 'grayscale-overlay': !isConnected }"/>
        </div>

        <JackpotInfoPopup/>
    </div>
</template>

<style scoped>
.jackpot-chest-container {
    margin: calc(var(--base-unit) * 10) calc(var(--base-unit) * 20);
    margin-bottom: calc(var(--base-unit) * 20);
    display: flex;
    flex-direction: column;
    gap: calc(var(--base-unit) * 26);
}

.grayscale-overlay {
    filter: grayscale(100%) opacity(0.8) brightness(0.4) contrast(1.2);
    pointer-events: none; /* prevents interaction */
}
</style>