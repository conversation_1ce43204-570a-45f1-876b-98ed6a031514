<script setup>
import { useI18n } from 'vue-i18n'
import { useReferralDetailsPopup } from '@/features/referral'
import { useChestOverlayStore } from '@/features/openChest/stores/chestOverlayStore'
import { useReferralCode } from '@/features/referral/composables/useReferralCode'
import { usePlatform } from '@/composables/usePlatform'
import { generateReferralLink } from '@/utils/config'
import { TitlePanelColor, AppRow, ColorButton } from '@/components/common'

// Components
import {
  ReferralRewardCard,
  ReferralRewardButton,
  DailyReferralRewardPanel,
  ReferralCode,
  ReferralDetailsPopup
} from '@/features/referral'


const { t } = useI18n()
const { isOpen, open, close } = useReferralDetailsPopup()
const chestOverlayStore = useChestOverlayStore()
const { referralCode, copyToClipboard } = useReferralCode()
const { isInLIFF } = usePlatform()



const shareReferralCode = async () => {
  // Check if we're in LIFF and shareTargetPicker is available
  const liff = window.liff
  if (isInLIFF.value && liff && liff.isApiAvailable("shareTargetPicker")) {
    try {
      const link = generateReferralLink(referralCode.value)
      if (link) {
        await liff.shareTargetPicker([
          {
            type: "text",
            text: link,
          },
        ])
      }
    } catch (error) {
      console.error('Failed to share via LIFF:', error)
      // Fall back to copy to clipboard if sharing fails
      copyToClipboard()
    }
  } else {
    // Fall back to copy to clipboard if not in LIFF or shareTargetPicker not available
    copyToClipboard()
  }
}
</script>

<template>
  <div class="invite-container">
    <TitlePanelColor variant="orange">
      {{ t('invite.title') }}
    </TitlePanelColor>

    <ReferralRewardCard labelKey="invite.inviteFriend" :amount="1" />
    <!-- <ReferralRewardCard labelKey="invite.invitePremiumFriend" :amount="2" /> -->
    <ReferralRewardButton />
    <AppRow :clickable="true" @click="open">
      <span class="left">{{ t('invite.referralDetails') }}</span>
      <template #right>
        <img class="arrow-icon" src="/ui/navigate-arrow-2.png" />
      </template>
    </AppRow>
    <ReferralCode />
    <ColorButton variant="orange" @click="shareReferralCode">
      {{ t('invite.invite') }}
    </ColorButton>
    <DailyReferralRewardPanel />
    <ReferralDetailsPopup :isOpen="isOpen" @close="close" />
  </div>
</template>

  
  <style scoped>
  .arrow-icon {
    width: calc(var(--base-unit) * 20);
    height: calc(var(--base-unit) * 20);
    object-fit: contain;
}


  .invite-container {
    margin:  calc(var(--base-unit) * 10) calc(var(--base-unit) * 20);
    margin-bottom: calc(var(--base-unit) * 50);
    display: flex;
    flex-direction: column;
    gap: calc(var(--base-unit) * 16);
}
  </style>
