<script setup>
import { useI18n } from 'vue-i18n'
import { useTasks } from '@/features/task'
import { TaskPanel } from '@/features/task'
import { TitlePanelColor, HeaderLabel } from '@/components/common'

const { t } = useI18n()

const {
  loading,
  error,
  dailyTasks,
  oneTimeTasks,
  chestRewardMap,
  handleTaskClick
} = useTasks()


</script>

<template>
  <div class="earn-container main-text">
    <TitlePanelColor :style="'margin-bottom:calc(var(--base-unit) * 18)'" variant="yellow">
      {{ $t('earn.title') }}
    </TitlePanelColor>

    <!-- Daily Tasks Section -->
    <HeaderLabel :text="$t('earn.dailyCheckIn')" />
    <div v-if="loading">{{ $t('earn.loading') }}</div>
    <div v-else-if="error" class="flex-center">{{ error }}</div>
    <template v-else>
      <TaskPanel
        v-for="task in dailyTasks"
        :key="task.id"
        :task="task"
        :rewardAmount="chestRewardMap[task.id] || 1"
        @click="handleTaskClick(task.id)"
      />
    </template>

    <!-- Social Tasks Section -->
    <HeaderLabel :text="$t('earn.socialTask')" />
    <div v-if="loading">{{ $t('earn.loading') }}</div>
    <div v-else-if="error" class="flex-center">{{ error }}</div>
    <template v-else>
      <TaskPanel
        v-for="task in oneTimeTasks"
        :key="task.id"
        :task="task"
        :rewardAmount="chestRewardMap[task.id] || 1"
        @click="handleTaskClick(task.id)"
      />
    </template>
  </div>
</template>


<style scoped>
.earn-container {
  margin: calc(var(--base-unit) * 10) calc(var(--base-unit) * 20);
  margin-bottom: calc(var(--base-unit) * 20);
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 16);
}

.task-panel {
  transition: transform 0.2s ease, opacity 0.2s ease;
  transform-origin: center;
  will-change: transform, opacity;
}

.task-panel:active {
  transform: scale(0.95);
}

.task-panel-disabled {
  opacity: 0.5;
  cursor: default;
  pointer-events: none;
}

.task-panel-available {
  opacity: 1;
  cursor: pointer;
}

.task-panel-completed {
  opacity: 1;
}

.disabled-reward {
  opacity: 0.5;
}
</style>
