<template>
  <div class="profile-container">
    <TitlePanelColor variant="purple">
      {{ t('profile.title') }}
    </TitlePanelColor>

    <AppRow :iconSrc="'/icon/leaderboard.png'" :showArrow="true" :clickable="true" @click="goToLeaderboard">
      <slot>{{ t('profile.leaderboard') }}</slot>
    </AppRow>

    <TicketTransferRow/>
    <!--- <KaiaWithdrawRow/> -->


    <AppRow :iconSrc="'/icon/setting.png'" :showArrow="true" :clickable="true" @click="goToSetting">
      <slot>{{ t('profile.settings') }}</slot>
    </AppRow>
    
    <TicketTransferPopup/>
    <KaiaWithdrawPopup/>
  </div>
</template>
  
<script setup>
import { useI18n } from 'vue-i18n';
import { TicketTransferRow, TicketTransferPopup } from '@/features/ticketTransfer';
import { KaiaWithdrawRow, KaiaWithdrawPopup } from '@/features/kaiaWithdraw';
import { useRouter } from 'vue-router'
import audioService from '@/lib/audioService'
import { TitlePanelColor, AppRow } from '@/components/common'
  
const { t } = useI18n();
const router = useRouter()

const goToLeaderboard = () => {
  audioService.play('button1'); // Play sound effect
  router.push("leaderboard")
};

const goToSetting = () => {
  audioService.play('button1'); // Play sound effect
  router.push("profile/setting")
};
</script>
  
  <style scoped>

  .profile-container {
    margin:  calc(var(--base-unit) * 10) calc(var(--base-unit) * 20);
    margin-bottom: calc(var(--base-unit) * 20);
    display: flex;
    flex-direction: column;
    gap: calc(var(--base-unit) * 16);
}


  </style>