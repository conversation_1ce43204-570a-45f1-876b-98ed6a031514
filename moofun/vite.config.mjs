import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import pkg from './package.json';

export default defineConfig(({ mode }) => {
    // Set output directory based on mode
    const getOutDir = (mode) => {
        switch (mode) {
            case 'theme-gb':
                return 'dist/gb';
            case 'theme-mf':
                return 'dist/mf';
            default:
                return 'dist';
        }
    };

    return {
        base: '/',
        plugins: [
            vue(),
        ],

        define: {
            __APP_VERSION__: JSON.stringify(pkg.version),
        },
        
        server: {
            port: 3000,
            open: true,
        },

        build: {
            outDir: getOutDir(mode),
            chunkSizeWarningLimit: 2000,
            emptyOutDir: true,
            target: 'es2022', // Enable top-level await support
        },

        // Add optimizeDeps configuration for Phaser
        optimizeDeps: {
            include: ['phaser']
        },

        // Add resolve configuration for asset paths
        resolve: {
            alias: {
                '@': '/src'
            }
        },
        // Configure asset handling
        assetsInclude: ['**/*.png', '**/*.jpg', '**/*.svg'],

        // Configure public directory
        publicDir: 'public',
    };
});